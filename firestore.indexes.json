{"indexes": [{"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "agents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "active", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "agents_container", "queryScope": "COLLECTION", "fields": [{"fieldPath": "processed", "order": "ASCENDING"}, {"fieldPath": "processing", "order": "ASCENDING"}, {"fieldPath": "error", "order": "ASCENDING"}]}, {"collectionGroup": "agents_container", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isComment", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isComment", "order": "ASCENDING"}, {"fieldPath": "parentPostId", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isComment", "order": "ASCENDING"}, {"fieldPath": "platform", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "parentPostId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "platform", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "posts-migrated", "queryScope": "COLLECTION", "fields": [{"fieldPath": "currentAgent", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.art_generator", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.artGenerator", "order": "ASCENDING"}, {"fieldPath": "agents.cultureChecker", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.artGenerator.error", "order": "ASCENDING"}, {"fieldPath": "agents.artGenerator.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.artGenerator.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.cultural_fit_checker", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.cultureChecker", "order": "ASCENDING"}, {"fieldPath": "agents.qualityChecker", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.cultureChecker.error", "order": "ASCENDING"}, {"fieldPath": "agents.cultureChecker.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.cultureChecker.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.imageGenerator", "order": "ASCENDING"}, {"fieldPath": "agents.artGenerator", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.imageGenerator.error", "order": "ASCENDING"}, {"fieldPath": "agents.imageGenerator.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.imageGenerator.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.quality_checker", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.qualityChecker", "order": "ASCENDING"}, {"fieldPath": "agents.storyGenerator", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.qualityChecker", "order": "ASCENDING"}, {"fieldPath": "agents.storyGenerator.processed_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.qualityChecker.error", "order": "ASCENDING"}, {"fieldPath": "agents.qualityChecker.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.qualityChecker.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.sentiment", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.sentiment_analyzer", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.sentimentAnalyzer", "order": "ASCENDING"}, {"fieldPath": "agents.textAnalyzer", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.sentimentAnalyzer", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "agents.textAnalyzer", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.sentimentAnalyzer.error", "order": "ASCENDING"}, {"fieldPath": "agents.sentimentAnalyzer.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.sentimentAnalyzer.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.story_generator", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.storyGenerator", "order": "ASCENDING"}, {"fieldPath": "agents.sentimentAnalyzer", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.storyGenerator", "order": "ASCENDING"}, {"fieldPath": "agents.textAnalyzer", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.storyGenerator.error", "order": "ASCENDING"}, {"fieldPath": "agents.storyGenerator.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.storyGenerator.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.text_analyzer", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agents.textAnalyzer.error", "order": "ASCENDING"}, {"fieldPath": "agents.textAnalyzer.processed_at", "order": "ASCENDING"}, {"fieldPath": "agents.textAnalyzer.started_at", "order": "ASCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created_at", "order": "DESCENDING"}, {"fieldPath": "agents.imageGenerator", "order": "DESCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hashtag", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hashtag", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hashtag", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "mosaic_generated", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}, {"fieldPath": "mosaic_url", "order": "DESCENDING"}]}, {"collectionGroup": "social_media_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "streams", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "training_jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "model_type", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "training_jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "tweets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "tweets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "arrayConfig": "CONTAINS"}, {"fieldPath": "created_at", "order": "DESCENDING"}, {"fieldPath": "analyzed_at", "order": "DESCENDING"}]}], "fieldOverrides": []}
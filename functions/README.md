# Firebase Functions

This directory contains the Firebase Cloud Functions for the id8media-agents-platform.

## Deployment

To deploy the functions, run:

```bash
cd functions
firebase deploy --only functions
```

## Troubleshooting

### ESLint Configuration Issues

If you encounter ESLint configuration issues during deployment, ensure that:

1. The package.json and package-lock.json files are in sync by running:

   ```bash
   npm install
   ```

2. The lint script in package.json explicitly specifies the ESLint configuration file:

   ```json
   "lint": "eslint --no-error-on-unmatched-pattern 'src/**/*.{js,ts}' --config .eslintrc.js"
   ```

3. The .eslintrc.js file has `root: true` to prevent it from inheriting configurations from parent directories.

### Package Lock Issues

If you encounter package lock issues during deployment, ensure that all dependencies listed in package.json are also in package-lock.json by running:

```bash
npm install
```

## Functions

The following functions are deployed:

- textAnalyzer
- commentGenerator
- sentimentAnalyzer
- storyGenerator
- cultureFit
- qualityCheck
- artDirector
- imageGenerator

{"name": "functions", "scripts": {"lint": "eslint --no-error-on-unmatched-pattern 'src/**/*.{js,ts}' --config .eslintrc.js", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "deploy-no-lint": "firebase deploy --only functions --no-build", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@google-cloud/functions-framework": "^3.4.6", "@google-cloud/storage": "^7.7.0", "@google-cloud/vertexai": "^1.9.3", "date-fns": "^4.1.0", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}
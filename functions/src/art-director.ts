import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

export const artDirector = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        style: {
          type: SchemaType.STRING,
          description:
            "The primary visual style for the artwork (e.g., photorealistic, digital art, watercolor, etc.), using vertexai imagen model styles.",
        },
        color_palette: {
          type: SchemaType.ARRAY,
          description:
            "List of 3-5 colors that should be used in the artwork, including hex codes",
          items: {
            type: SchemaType.STRING,
          },
        },
        composition: {
          type: SchemaType.STRING,
          description:
            "Detailed description of the composition layout and arrangement",
        },
        mood: {
          type: SchemaType.STRING,
          description: "The emotional mood or atmosphere of the artwork",
        },
        visual_elements: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 key visual elements to include",
          items: {
            type: SchemaType.STRING,
          },
        },
        focal_points: {
          type: SchemaType.ARRAY,
          description: "List of 2-3 main focal points for the artwork",
          items: {
            type: SchemaType.STRING,
          },
        },
        visual_flow: {
          type: SchemaType.STRING,
          description:
            "How the viewer's eye should move through the composition",
        },
        emotional_impact: {
          type: SchemaType.STRING,
          description: "The intended emotional response from viewers",
        },
        atmosphere: {
          type: SchemaType.STRING,
          description: "The overall atmosphere and feeling of the scene",
        },
        lighting: {
          type: SchemaType.STRING,
          description: "The lighting style and direction for the image",
        },
        texture_style: {
          type: SchemaType.STRING,
          description: "The texture characteristics to emphasize in the image",
        },
        key_elements: {
          type: SchemaType.ARRAY,
          description:
            "List of specific elements that must be included in the image",
          items: {
            type: SchemaType.STRING,
          },
        },
      },
      required: [
        "style",
        "color_palette",
        "composition",
        "mood",
        "visual_elements",
        "focal_points",
        "visual_flow",
        "emotional_impact",
        "atmosphere",
        "lighting",
        "texture_style",
        "key_elements",
      ],
    };
    const model = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are an expert art director with extensive experience in visual design, photography, and digital art. Your role is to create detailed art direction for visual content generation that will result in stunning, professional images.",
    });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `
Create detailed art direction for visual content generation that will produce a stunning, professional image.

The art direction should include:
- Style: Choose the most appropriate style for the content (e.g., photorealistic, digital art, watercolor, etc.)
- Color palette: List of 3-5 specific colors with hex codes that will create visual harmony
- Composition: Detailed description of the layout and arrangement using professional composition techniques
- Mood: The precise emotional mood or atmosphere that should be conveyed
- Visual elements: List of 3-5 specific visual elements that must be included
- Focal points: List of 2-3 main focal points that will draw the viewer's attention
- Visual flow: How the viewer's eye should move through the composition
- Emotional impact: The specific emotional response you want to evoke from viewers
- Atmosphere: The overall atmosphere and feeling of the scene
- Lighting: The specific lighting style, direction, and quality
- Texture style: The texture characteristics to emphasize
- Key elements: List of specific elements that must be included in the image

Content to analyze: "${req.body.content}"

Text analysis agent results: ${JSON.stringify(req.body.textAnalysis)}
Sentiment analysis agent results: ${JSON.stringify(req.body.sentimentAnalysis)}
Story analysis agent results: ${JSON.stringify(req.body.storyAnalysis)}
Culture fit analysis agent results: ${JSON.stringify(
                req.body.cultureFitAnalysis
              )}
Quality check agent results: ${JSON.stringify(req.body.qualityCheck)}

Based on all this information, create art direction that will result in a visually stunning, professional image that perfectly captures the essence of the content. Focus on creating a cohesive visual style with perfect composition, lighting, and color harmony.

Please provide a structured response following the schema.`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.2, // Slightly increased for more creative variation while maintaining consistency
        maxOutputTokens: 4096,
        topP: 0.95,
        topK: 70,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const response = await result.response;
    const data = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!data) {
      throw new Error("No response generated from the model");
    }

    let jsonResponse;
    try {
      jsonResponse = JSON.parse(data);

      // Validate and enhance color palette if needed
      if (
        jsonResponse.color_palette &&
        Array.isArray(jsonResponse.color_palette)
      ) {
        // Ensure all colors have hex codes
        jsonResponse.color_palette = jsonResponse.color_palette.map(
          (color: string) => {
            if (!color.includes("#")) {
              // Add a default hex code for common colors
              const colorMap: Record<string, string> = {
                red: "#FF0000",
                blue: "#0000FF",
                green: "#00FF00",
                yellow: "#FFFF00",
                purple: "#800080",
                orange: "#FFA500",
                black: "#000000",
                white: "#FFFFFF",
                gray: "#808080",
                pink: "#FFC0CB",
                brown: "#A52A2A",
                teal: "#008080",
                navy: "#000080",
                gold: "#FFD700",
                silver: "#C0C0C0",
              };

              // Try to find a matching color
              for (const [name, hex] of Object.entries(colorMap)) {
                if (color.toLowerCase().includes(name)) {
                  return `${color} (${hex})`;
                }
              }

              // If no match, return the original color
              return color;
            }
            return color;
          }
        );
      }

      // Ensure all required fields exist
      const requiredFields = responseSchema.required || [];
      for (const field of requiredFields) {
        if (!jsonResponse[field]) {
          // Set default values for missing fields
          switch (field) {
            case "style":
              jsonResponse[field] = "photorealistic";
              break;
            case "color_palette":
              jsonResponse[field] = [
                "#4285F4",
                "#34A853",
                "#FBBC05",
                "#EA4335",
                "#FFFFFF",
              ];
              break;
            case "composition":
              jsonResponse[field] = "balanced composition with rule of thirds";
              break;
            case "mood":
              jsonResponse[field] = "professional and contemporary";
              break;
            case "visual_elements":
              jsonResponse[field] = [
                "clean lines",
                "subtle textures",
                "balanced shapes",
              ];
              break;
            case "focal_points":
              jsonResponse[field] = ["central subject", "supporting elements"];
              break;
            case "visual_flow":
              jsonResponse[field] = "dynamic and engaging";
              break;
            case "emotional_impact":
              jsonResponse[field] = "professional and impactful";
              break;
            case "atmosphere":
              jsonResponse[field] = "clean and professional";
              break;
            case "lighting":
              jsonResponse[field] =
                "soft, diffused lighting with subtle highlights";
              break;
            case "texture_style":
              jsonResponse[field] = "smooth with subtle details";
              break;
            case "key_elements":
              jsonResponse[field] = [
                "main subject",
                "supporting elements",
                "background context",
              ];
              break;
            default:
              jsonResponse[field] = "";
          }
        }
      }
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      jsonResponse = {
        style: "photorealistic",
        color_palette: ["#4285F4", "#34A853", "#FBBC05", "#EA4335", "#FFFFFF"],
        composition: "balanced composition with rule of thirds",
        mood: "professional and contemporary",
        visual_elements: ["clean lines", "subtle textures", "balanced shapes"],
        focal_points: ["central subject", "supporting elements"],
        visual_flow: "dynamic and engaging",
        emotional_impact: "professional and impactful",
        atmosphere: "clean and professional",
        lighting: "soft, diffused lighting with subtle highlights",
        texture_style: "smooth with subtle details",
        key_elements: [
          "main subject",
          "supporting elements",
          "background context",
        ],
      };
    }

    res.status(200).send(jsonResponse);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send({ error: "An error occurred" });
  }
});

import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

export const cultureFit = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        alignment_score: {
          type: SchemaType.STRING,
          description: "Cultural alignment score as a string from '1' to '10'",
        },
        market_fit: {
          type: SchemaType.STRING,
          description: "Market fit score as a string from '1' to '10'",
        },
        brand_values: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 brand values identified in the content",
          items: {
            type: SchemaType.STRING,
          },
        },
        suggestions: {
          type: SchemaType.ARRAY,
          description:
            "List of 2-4 suggestions for improving cultural alignment",
          items: {
            type: SchemaType.STRING,
          },
        },
        warnings: {
          type: SchemaType.ARRAY,
          description:
            "List of any cultural sensitivity warnings (if applicable)",
          items: {
            type: SchemaType.STRING,
          },
        },
        recommendations: {
          type: SchemaType.ARRAY,
          description: "List of 2-4 actionable recommendations",
          items: {
            type: SchemaType.STRING,
          },
        },
      },
      required: [
        "alignment_score",
        "market_fit",
        "brand_values",
        "suggestions",
        "recommendations",
        "warnings",
      ],
    };

    const model = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are an expert culture fit analyzer that analyzes the cultural alignment and brand values in this content.",
    });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `
Analyze the cultural alignment and brand values in this content.

The analysis should include:
- Cultural alignment score (as a string between '1' to '10')
- Market fit score (as a string between '1' to '10')
- List of 3-5 brand values identified in the content
- List of 2-4 suggestions for improving cultural alignment
- List of any cultural sensitivity warnings (if applicable)
- List of 2-4 actionable recommendations
- The content should fit the MENSA (Market, Experience, Needs, Strengths, and Audience)

Content to analyze: "${req.body.content}"

Text analysis agent results: ${JSON.stringify(req.body.textAnalysis)}
Sentiment analysis agent results: ${JSON.stringify(req.body.sentimentAnalysis)}
Story analysis agent results: ${JSON.stringify(req.body.storyAnalysis)}

Please provide a structured response following the schema.`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2048,
        topP: 0.8,
        topK: 40,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const response = await result.response;
    const data = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!data) {
      throw new Error("No response generated from the model");
    }

    let jsonResponse;
    try {
      jsonResponse = JSON.parse(data);
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      jsonResponse = {};
    }

    res.status(200).send(jsonResponse);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send({ error: "An error occurred" });
  }
});

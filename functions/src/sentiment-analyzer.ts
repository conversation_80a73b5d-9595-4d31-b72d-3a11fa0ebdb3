import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

export const sentimentAnalyzer = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        summary: {
          type: SchemaType.STRING,
          description:
            "A concise summary of the content (1-2 sentences), and explain the sentiment of the content.",
        },
        sentiment: {
          type: SchemaType.STRING,
          description:
            "Overall sentiment (positive, negative, neutral, or mixed)",
        },
        sentiment_score: {
          type: SchemaType.STRING,
          description: "Sentiment intensity score as a string from '1' to '10'",
        },
        emotions: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 specific emotions detected",
          items: {
            type: SchemaType.STRING,
          },
        },
        highlights: {
          type: SchemaType.ARRAY,
          description: "List of 2-3 key phrases that convey the sentiment",
          items: {
            type: SchemaType.STRING,
          },
        },
        intensity: {
          type: SchemaType.STRING,
          description: "Emotional intensity (low, medium, high)",
        },
      },
      required: [
        "summary",
        "sentiment",
        "sentiment_score",
        "emotions",
        "highlights",
        "intensity",
      ],
    };

    const model = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are an expert sentiment analyzer that extracts the sentiment, intensity, and emotional tone of text.",
    });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `
Analyze the sentiment and emotional tone of this content.

The analysis should include:
- A concise summary of the main points
- 4 key terms or phrases that capture the essence
- 3 main topics covered
- 3 key focal points for emphasis
- Overall sentiment (positive, negative, neutral, or mixed)
- Sentiment score (as a string from '1' to '10')
- List of emotions detected
- Key sentiment highlights
- Emotional intensity

Content to analyze: "${req.body.content}"

text analysis agent results: ${JSON.stringify(req.body.textAnalysis)}

Please provide a structured response following the schema.`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2048,
        topP: 0.8,
        topK: 40,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const response = await result.response;
    const data = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!data) {
      throw new Error("No response generated from the model");
    }

    let jsonResponse;
    try {
      jsonResponse = JSON.parse(data);
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      jsonResponse = {};
    }

    res.status(200).send(jsonResponse);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send({ error: "An error occurred" });
  }
});

import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

export const textAnalyzer = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        summary: {
          type: SchemaType.STRING,
          description: "A concise summary of the content (1-2 sentences)",
        },
        keywords: {
          type: SchemaType.ARRAY,
          description: "List of 5-8 keywords from the content",
          items: {
            type: SchemaType.STRING,
          },
        },
        topics: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 main topics covered",
          items: {
            type: SchemaType.STRING,
          },
        },
        focal_points: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 key focal points for visual representation",
          items: {
            type: SchemaType.STRING,
          },
        },
      },
      required: ["summary", "keywords", "topics", "focal_points"],
    };

    const model = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are an expert content analyzer that extracts key themes, topics, and insights from text.",
    });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `
Analyze this text and provide a detailed content assessment.
    
The analysis should include:
- A concise summary of the main points
- 4 key terms or phrases that capture the essence
- 3 main topics covered
- 3 key focal points for emphasis
        
Content to analyze: "${req.body.content}"
    
Please provide a structured response following the schema.`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2048,
        topP: 0.8,
        topK: 40,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const response = await result.response;
    const data = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!data) {
      throw new Error("No response generated from the model");
    }

    let jsonResponse;
    try {
      jsonResponse = JSON.parse(data);
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      jsonResponse = {};
    }

    res.status(200).send(jsonResponse);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send({ error: "An error occurred" });
  }
});

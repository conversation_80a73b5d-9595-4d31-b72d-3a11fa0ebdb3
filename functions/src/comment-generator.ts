import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

interface GeneratedComment {
  postBody: string;
  authorName: string;
  username: string;
  comment_like_count: number;
  metrics: {
    likes: number;
  };
}

export const commentGenerator = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        comments: {
          type: SchemaType.ARRAY,
          description: "Array of 25 generated comments",
          items: {
            type: SchemaType.OBJECT,
            properties: {
              postBody: {
                type: SchemaType.STRING,
                description:
                  "The generated comment text in Arabic about Qatar National Day",
              },
              authorName: {
                type: SchemaType.STRING,
                description:
                  "A realistic Arabic username with some special characters like dots and underscores",
              },
              username: {
                type: SchemaType.STRING,
                description:
                  "A different username format with underscores and numbers",
              },
              comment_like_count: {
                type: SchemaType.NUMBER,
                description:
                  "A realistic number of likes for the comment between 0-9",
              },
              metrics: {
                type: SchemaType.OBJECT,
                properties: {
                  likes: {
                    type: SchemaType.NUMBER,
                    description:
                      "The number of likes for the comment between 0-9",
                  },
                },
                required: ["likes"],
              },
            },
            required: [
              "postBody",
              "authorName",
              "username",
              "comment_like_count",
              "metrics",
            ],
          },
        },
      },
      required: ["comments"],
    };

    // Get parameters from request
    const { topic, sentiment, style } = req.body;

    // Create the prompt for generating comments
    const prompt = `
    Generate 25 unique and realistic comments in Arabic about Qatar National Day. 
    
    Each comment should:
    - Be written in Arabic
    - Express ${sentiment || "positive"} sentiment about Qatar National Day
    - Include references to ${
      topic || "celebrations, pride, and national identity"
    }
    - Be in a ${style || "casual, social media"} style
    - Be between 10-30 words in length
    - Sound authentic and natural, like a real social media comment
    - NOT include any hashtags
    - Be completely unique and different from other comments
    
    For emojis:
    - About 60% of comments should include 1-3 emojis
    - About 40% of comments should be text-only with NO emojis
    - For comments with emojis, use varied combinations (not just 🇶🇦 and ❤️ in every comment)
    - Use a wide variety of emojis related to celebration, national pride, etc. (🎉 🎊 🥳 🙏 ✨ 🌟 💫 🔥 etc.)
    - Place emojis in different positions (beginning, middle, end) in different comments
    
    For usernames:
    - Use a mix of Arabic and English characters
    - Include variations like: dots (.), underscores (_), numbers
    - Examples: "محمد.العبدالله", "سارة_القطري", "خليفة_الذكريات", "فاطمة.النعيمي", "عبدالرحمن_99"
    - Make sure each username is unique and different from others
    - Use different patterns of dots, underscores, and numbers
    
    For comment content:
    - Vary the sentence structure and length significantly between comments
    - Include different aspects of Qatar National Day celebrations in each comment
    - Mix formal and casual language across comments
    - Include personal experiences and feelings that differ between comments
    - Use different Arabic expressions and idioms in each comment
    - Some comments should be more formal, others more casual
    - Some should be short and direct, others more elaborate
    
    For each comment, also generate:
    - A realistic Arabic username with some special characters (dots, underscores)
    - A different username format with underscores and numbers
    - A realistic number of likes (0-9)
    
    Important: 
    - Make sure each comment feels authentic and like something a real person would write on social media
    - Ensure all usernames are unique across all comments
    - Vary the sentiment and style slightly between comments to make them feel more natural
    - Generate exactly 25 comments
    - Each comment should be completely different from the others in both content and style
    - Create a good mix of comments with emojis and comments with only text
    
    Respond in JSON format with the following structure:
    {
      "comments": [
        {
          "postBody": "first comment here with emojis 🇶🇦❤️",
          "authorName": "first author name",
          "username": "first username",
          "comment_like_count": 5,
          "metrics": {
            "likes": 5
          }
        },
        {
          "postBody": "second comment here with only text, no emojis",
          "authorName": "second author name",
          "username": "second username",
          "comment_like_count": 3,
          "metrics": {
            "likes": 3
          }
        },
        // ... repeat for all 25 comments
      ]
    }
    `;

    // Call Vertex AI
    const generativeModel = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      generationConfig: {
        temperature: 0.9,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 8192, // Increased token limit to handle 25 comments
      },
    });

    const result = await generativeModel.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig: {
        responseSchema,
        responseMimeType: "application/json",
      },
    });

    const response = result.response;
    const text = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!text) {
      throw new Error("Failed to generate comments: No response from model");
    }

    // Parse the response
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(text);
    } catch (error) {
      console.error("Error parsing response:", error);
      throw new Error("Failed to parse model response");
    }

    // Validate required fields
    if (
      !parsedResponse.comments ||
      !Array.isArray(parsedResponse.comments) ||
      parsedResponse.comments.length < 1
    ) {
      throw new Error("Invalid response: No comments returned");
    }

    // Add timestamp and default values to match the Instagram comments structure
    const commentsData = parsedResponse.comments.map(
      (comment: GeneratedComment) => ({
        ...comment,
        created_at: new Date(),
        has_liked: false,
        has_liked_comment: false,
        is_mentionable: false,
        is_pinned: false,
        is_verified: false,
        platform: "instagram",
        type: "comment",
      })
    );

    res.status(200).json({ comments: commentsData });
  } catch (error) {
    console.error("Error generating comments:", error);
    res.status(500).json({
      error: "Failed to generate comments",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

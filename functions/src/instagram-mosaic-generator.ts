import { onRequest } from "firebase-functions/v2/https";
import { GoogleAuth } from "google-auth-library";
import { Storage } from "@google-cloud/storage";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp } from "firebase-admin/app";
import { ResponseSchema, SchemaType, VertexAI } from "@google-cloud/vertexai";

// Initialize Firebase Admin
const app = initializeApp();
const db = getFirestore(app);

// Initialize Storage with credentials
const storage = new Storage({
  scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  projectId: "qnd-platform",
  credentials: {
    type: "service_account",
    project_id: "qnd-platform",
    private_key_id: "d7810aee3f03d54751d93e449fa3b3d8a1f657e5",
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      "<EMAIL>",
    client_id: "107042730709320593986",
    universe_domain: "googleapis.com",
  },
});
const bucketName = "qnd-platform.firebasestorage.app";

// Initialize Google Auth client with credentials
const auth = new GoogleAuth({
  scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  projectId: "qnd-platform",
  credentials: {
    type: "service_account",
    project_id: "qnd-platform",
    private_key_id: "82a9bae6fd7737fe7693d61ff77830bca08c3d6a",
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      "<EMAIL>",
    client_id: "107042730709320593986",
    universe_domain: "googleapis.com",
  },
});

// Initialize Vertex AI
const vertexAI = new VertexAI({
  project: process.env.FUNCTION_REGION || "qnd-platform",
  location: "us-central1",
});

interface MosaicGenerationResult {
  url: string;
  prompt: string;
  commentIds: string[];
  author?: string;
  created_at: string;
  platform: string;
}

interface Comment {
  id: string;
  authorName: string;
  username: string;
  postBody: string;
  imageUrl?: string;
  imageGenerated?: boolean;
  mosaic_generated?: boolean;
  mosaic_url?: string;
}

export const instagramMosaicGenerator = onRequest(
  { cors: true },
  async (req, res) => {
    try {
      const { count = 30 } = req.body;

      // Validate input
      if (!count || count < 1 || count > 100) {
        res.status(400).send({
          error: "Invalid count parameter. Must be between 1 and 100.",
        });
        return;
      }

      // Fetch comments from Firestore
      const commentsRef = db.collection("instagram_comments");
      let comments: Comment[] = [];

      // First try to find comments with imageGenerated=true
      const imageGeneratedSnapshot = await commentsRef
        .where("imageGenerated", "==", true)
        .limit(count)
        .get();

      console.log(
        `Found ${imageGeneratedSnapshot.size} comments with imageGenerated=true`
      );

      if (!imageGeneratedSnapshot.empty) {
        comments = imageGeneratedSnapshot.docs.map((doc) => ({
          ...(doc.data() as Comment),
          id: doc.id,
        }));
      } else {
        // If no comments with imageGenerated=true, try to find comments with non-empty imageUrl
        const imageUrlSnapshot = await commentsRef
          .where("imageUrl", "!=", "")
          .limit(count)
          .get();

        console.log(
          `Found ${imageUrlSnapshot.size} comments with non-empty imageUrl`
        );

        if (!imageUrlSnapshot.empty) {
          comments = imageUrlSnapshot.docs.map((doc) => ({
            ...(doc.data() as Comment),
            id: doc.id,
          }));
        }
      }

      // If no comments found with either condition
      if (comments.length === 0) {
        res.status(404).send({
          error:
            "No comments found with either imageGenerated=true or a non-empty imageUrl.",
        });
        return;
      }

      // Create a stories object for the art text generation
      const stories: { [key: string]: string } = {};
      comments.forEach((comment, index) => {
        if (comment.postBody) {
          stories[`${index + 1}`] = comment.postBody;
        }
      });

      // Generate art text using Gemini
      const artText = await generateArtText(stories);
      const artTextData = JSON.parse(artText);

      if (!artTextData.art_text) {
        throw new Error("Failed to generate art text");
      }

      // Generate mosaic image using Imagen
      const imageUrl = await generateMosaicImage(
        artTextData.art_text,
        comments.map((c) => c.postBody).filter(Boolean)
      );

      // Save mosaic to dedicated collection
      const mosaicData: MosaicGenerationResult = {
        url: imageUrl,
        prompt: artTextData.art_text,
        created_at: new Date().toISOString(),
        commentIds: comments.map((comment) => comment.id),
        platform: "instagram",
      };

      const mosaicRef = await db
        .collection("instagram_mosaics")
        .add(mosaicData);

      // Update comments as used in mosaic
      const batch = db.batch();
      comments.forEach((comment) => {
        const commentRef = db.collection("instagram_comments").doc(comment.id);
        batch.update(commentRef, {
          mosaic_generated: true,
          mosaic_url: imageUrl,
        });
      });

      await batch.commit();

      // Return success response
      res.status(200).send({
        success: true,
        mosaic: {
          id: mosaicRef.id,
          ...mosaicData,
        },
        commentsProcessed: comments.length,
      });
    } catch (error) {
      console.error("Error generating mosaic:", error);
      res.status(500).send({
        error: "Failed to generate mosaic",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
);

const responseSchema: ResponseSchema = {
  type: SchemaType.OBJECT,
  properties: {
    art_text: {
      type: SchemaType.STRING,
      description: "Detailed artistic prompt for the mosaic composition",
    },
  },
  required: ["art_text"],
};

async function generateArtText(stories: {
  [key: string]: string;
}): Promise<string> {
  try {
    // Create the prompt for Gemini
    const prompt = `
    Create a unique and innovative artistic prompt for a mosaic-style image generator. The final image should be composed of many smaller elements that combine to form a larger, cohesive artwork.

    Use these Instagram comments as inspiration for the individual elements:
    ${Object.entries(stories)
      .map(([key, value]) => `Story ${key}: "${value}"`)
      .join("\n")}

    IMPORTANT: Return a valid JSON object with:
    1. "art_text": Your detailed artistic prompt

    The mosaic composition should:
    1. Create a grand, unified image composed of hundreds of smaller story-inspired elements
    2. Each micro-element should:
       - Represent a fragment of the stories
       - Have its own unique color palette and style
       - Range in size from tiny to medium
       - Blend seamlessly with surrounding elements
    
    3. The overall composition should:
       - Form a larger, meaningful image when viewed from afar
       - Use the micro-elements as "pixels" to build the macro image
       - Create depth through layering of elements
       - Maintain visual harmony while celebrating diversity
    
    4. Visual Techniques:
       - Use pixel-art inspired placement of story elements
       - Create depth through element sizing and overlap
       - Employ micro-to-macro transitions
       - Build gradients through element clustering
       - Layer transparent elements for depth
    
    5. Artistic Elements:
       - Dynamic range of element sizes (tiny to medium)
       - Multiple focal points at different scales
       - Complex pattern formations
       - Innovative color transitions between clusters
       - Mixed media effects within elements
    
    6. Technical Requirements:
       - Ultra-high resolution for clear micro-details
       - Sharp element definition
       - Clean transitions between elements
       - Professional color grading
       - Rich contrast in both micro and macro scales
    
    7. Special Effects:
       - Subtle glow around key elements
       - Depth-based blur for background elements
       - Micro-shadows for depth
       - Element-based reflections
       - Atmospheric perspective between layers

    8. Color and Composition:
       - Use color theory to group related elements
       - Create visual flow through element placement
       - Balance density of elements across the image
       - Use negative space strategically
       - Build color harmony across the entire piece

    Remember: The final image should work on two levels:
    1. Micro: Individual story elements are clear and detailed
    2. Macro: Elements combine to form a larger, meaningful composition

    Make each generation unique and avoid:
    - Standard mosaic grid patterns
    - Basic geometric layouts
    - Repetitive element placement
    - Uniform element sizes
    - Predictable color schemes`;

    // Call Vertex AI Gemini with schema
    const generativeModel = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are a master mosaic artist specializing in creating complex, multi-layered artworks that combine micro-elements into macro compositions. Each creation must be unique, innovative, and work at multiple viewing distances.",
      generationConfig: {
        temperature: 0.95,
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 4096,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
        candidateCount: 1,
        stopSequences: ["```"],
      },
    });

    const result = await generativeModel.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
    });

    const response = result.response;
    const text = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!text) {
      throw new Error("Failed to generate art text: No response from model");
    }

    // Clean and parse the response
    const cleanedText = text.trim().replace(/^```json\s*|\s*```$/g, "");

    try {
      const parsedResponse = JSON.parse(cleanedText);
      if (!parsedResponse.art_text) {
        throw new Error("Invalid response format: missing art_text field");
      }
      return cleanedText;
    } catch (parseError) {
      // If parsing fails, try to extract the art text directly
      const artTextMatch = text.match(/"art_text"\s*:\s*"([^"]+)"/);
      if (artTextMatch) {
        return JSON.stringify({ art_text: artTextMatch[1] });
      }
      throw new Error("Failed to parse art text response");
    }
  } catch (error) {
    console.error("Error generating art text:", error);
    throw new Error(
      `Failed to generate art text: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

async function generateMosaicImage(
  prompt: string,
  captions: string[]
): Promise<string> {
  try {
    // Clean captions for backup use
    const cleanedCaptions = captions
      .map((caption) => caption.replace(/[^\w\s.,]/g, ""))
      .join(". ");

    // Create dynamic artistic styles for the mosaic
    const artStyles = [
      {
        style: "Pixel Mosaic",
        description:
          "Thousands of tiny pixel-like elements forming a larger image, each pixel containing a micro-story",
      },
      {
        style: "Floating Fragments",
        description:
          "Suspended story fragments at different depths, creating a dimensional collage effect",
      },
      {
        style: "Geometric Tessellation",
        description:
          "Precisely arranged geometric shapes containing story elements, forming intricate patterns",
      },
      {
        style: "Organic Flow",
        description:
          "Fluid, natural arrangements of story elements that flow and merge organically",
      },
      {
        style: "Digital Deconstruction",
        description:
          "Story elements breaking apart and reforming in a digital, glitch-art style",
      },
      {
        style: "Crystalline Structure",
        description:
          "Story fragments captured within crystal-like formations, refracting and reflecting",
      },
      {
        style: "Neural Network",
        description:
          "Interconnected nodes of story elements forming neural pathway-like patterns",
      },
      {
        style: "Quantum Field",
        description:
          "Story elements existing in multiple states, creating quantum-like interference patterns",
      },
    ];

    // Select a random art style
    const selectedArtStyle =
      artStyles[Math.floor(Math.random() * artStyles.length)];

    // Create composition types for the overall image
    const compositionTypes = [
      "Create a grand portrait composed entirely of smaller story fragments",
      "Form a sweeping landscape built from hundreds of micro-narratives",
      "Design an abstract concept visualization made of story elements",
      "Construct a symbolic representation using narrative building blocks",
      "Build a metaphorical scene where each detail is a story moment",
      "Generate a surreal dreamscape where stories float and merge",
      "Compose a dynamic action scene made of story fragments",
      "Craft an emotional journey map using story elements",
    ];

    // Select a random composition type
    const selectedComposition =
      compositionTypes[Math.floor(Math.random() * compositionTypes.length)];

    // Use the provided prompt, with enhanced artistic direction
    const finalPrompt = `${prompt}

    Primary Artistic Direction: ${selectedComposition}
    Using Style: ${selectedArtStyle.style} - ${selectedArtStyle.description}

    Core Composition Requirements:
    1. Story Integration:
       - Each story fragment should be visually represented
       - Stories should flow and connect naturally
       - Create visual links between related stories
       - Maintain story context while blending elements

    2. Visual Hierarchy:
       - Primary focal point formed by key story elements
       - Secondary story clusters creating depth
       - Background texture formed by micro-stories
       - Dynamic transitions between layers

    3. Technical Execution:
       - Ultra-high resolution rendering
       - Crisp detail in both micro and macro elements
       - Professional color grading and balance
       - Sophisticated lighting and shadows
       - Clean edge definition and anti-aliasing

    4. Artistic Elements:
       - Complex layering of story fragments
       - Varied scale and perspective
       - Rich texture and pattern work
       - Dynamic composition flow
       - Atmospheric depth and dimension

    5. Special Effects:
       - Story-based particle effects
       - Element-specific lighting
       - Depth-aware blur and focus
       - Subtle animation suggestions
       - Environmental atmospherics

    Story Context:
    ${cleanedCaptions}

    Additional Requirements:
    - Create clear visual relationships between stories
    - Maintain readability at multiple viewing distances
    - Use color theory to group related elements
    - Implement sophisticated transition effects
    - Add subtle environmental elements
    - Include micro-detail enhancements
    - Create depth through layering
    - Use advanced composition techniques

    Technical Specifications:
    - Resolution: Ultra-high 4K output
    - Color: Professional grade, HDR-ready
    - Detail: Maximum micro-detail preservation
    - Lighting: Cinema-quality, multi-source
    - Effects: Premium post-processing suite
    - Quality: Gallery-grade output

    Style Notes:
    - Avoid basic mosaic grids
    - Prevent repetitive patterns
    - Maintain organic flow
    - Create unique element arrangements
    - Use sophisticated color schemes
    `;

    // Get access token for authentication
    const client = await auth.getClient();
    const accessToken = (await client.getAccessToken()).token;

    if (!accessToken) {
      throw new Error("Failed to get access token");
    }

    // Set up the request to the Imagen API
    const projectId = process.env.GC_CLOUD_PROJECT || "qnd-platform";
    const location = "us-central1";
    const modelId = "imagen-3.0-generate-002";

    const endpoint = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/${modelId}:predict`;

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        instances: [{ prompt: finalPrompt }],
        parameters: {
          sampleCount: 1,
          enhancePrompt: true,
          aspectRatio: "16:9",
          personGeneration: "allow_all",
          safetySetting: "block_only_high",
          storageUri: `gs://${bucketName}/instagram_mosaics_from_vertex`,
          imageSize: "1920x1080",
          mode: "upscale",
          upscaleConfig: {
            upscaleFactor: "x4",
          },
          samplerParams: {
            guidanceScale: 15.0, // Increased for better prompt adherence
            numInferenceSteps: 75, // Increased for better quality
          },
        },
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vertex AI API error: ${errorText}`);
    }

    const data = await response.json();

    if (!data?.predictions?.[0]?.gcsUri) {
      throw new Error("Invalid response from Vertex AI");
    }

    const gcsUrl = data.predictions[0].gcsUri;

    // Extract the path from the GCS URI
    const gcsPath = gcsUrl.replace(`gs://${bucketName}/`, "");

    // Create a signed URL for the image
    const [url] = await storage
      .bucket(bucketName)
      .file(gcsPath)
      .getSignedUrl({
        version: "v4",
        action: "read",
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      });

    return url;
  } catch (error) {
    console.error("Error generating mosaic image:", error);
    throw new Error(
      `Failed to generate mosaic image: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

// import {onRequest} from "firebase-functions/v2/https";
// import * as logger from "firebase-functions/logger";

import { textAnalyzer } from "./text-analyzer";
import { sentimentAnalyzer } from "./sentiment-analyzer";
import { storyGenerator } from "./story-generator";
import { cultureFit } from "./culture-fit";
import { qualityCheck } from "./quality-check";
import { artDirector } from "./art-director";
import { imageGenerator } from "./image-generator";
import { commentGenerator } from "./comment-generator";
import { instagramMosaicGenerator } from "./instagram-mosaic-generator";

// Start writing functions
// https://firebase.google.com/docs/functions/typescript

// export const helloWorld = onRequest((request, response) => {
//   logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });

export { textAnalyzer, commentGenerator };
export { sentimentAnalyzer };
export { storyGenerator };
export { cultureFit };
export { qualityCheck };
export { artDirector };
export { imageGenerator };
export { instagramMosaicGenerator };

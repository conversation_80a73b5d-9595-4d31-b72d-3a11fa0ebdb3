import { onRequest } from "firebase-functions/v2/https";
import { Storage } from "@google-cloud/storage";
import { GoogleAuth } from "google-auth-library";

const storage = new Storage({
  scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  projectId: "qnd-platform",
  credentials: {
    type: "service_account",
    project_id: "qnd-platform",
    private_key_id: "d7810aee3f03d54751d93e449fa3b3d8a1f657e5",
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      "<EMAIL>",
    client_id: "107042730709320593986",
    universe_domain: "googleapis.com",
  },
});
const bucketName = "qnd-platform.firebasestorage.app";

const auth = new GoogleAuth({
  scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  projectId: "qnd-platform",
  credentials: {
    type: "service_account",
    project_id: "qnd-platform",
    private_key_id: "82a9bae6fd7737fe7693d61ff77830bca08c3d6a",
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      "<EMAIL>",
    client_id: "107042730709320593986",
    universe_domain: "googleapis.com",
  },
});

interface ImageGenerationResult {
  url: string;
  caption: string;
  style_used: string;
  mood: string;
  dimensions: {
    width: string;
    height: string;
  };
  color_palette: string[];
  focal_points: string[];
  visual_elements: string[];
  lighting: string;
  texture_style: string;
  atmosphere: string;
  emotional_impact: string;
  visual_flow: string;
  key_elements: string[];
  processed_at: string;
}

export const imageGenerator = onRequest({ cors: true }, async (req, res) => {
  try {
    const {
      content,
      artDirection,
      textAnalysis,
      sentimentAnalysis,
      storyAnalysis,
      cultureFitAnalysis,
      qualityCheck,
    } = req.body;

    if (!content) {
      res.status(400).send({ error: "Content is required" });
      return;
    }

    // Construct a detailed prompt using the art direction and content
    const enhancedPrompt = constructEnhancedPrompt(
      content,
      artDirection,
      textAnalysis,
      sentimentAnalysis,
      storyAnalysis,
      cultureFitAnalysis,
      qualityCheck
    );

    // Set up the request to the Imagen API
    const projectId = process.env.GC_CLOUD_PROJECT || "qnd-platform";
    const location = "us-central1";
    // Use the latest Imagen model for better quality
    const modelId = "imagen-3.0-generate-002";

    const endpoint = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/${modelId}:predict`;

    // Get access token for authentication
    const client = await auth.getClient();
    const accessToken = (await client.getAccessToken()).token;

    if (!accessToken) {
      throw new Error("Failed to get access token");
    }

    // Prepare the request body with improved parameters
    const requestBody = {
      instances: [
        {
          prompt: enhancedPrompt,
        },
      ],
      parameters: {
        sampleCount: 1, // Generate one high-quality image
        aspectRatio: "16:9", // Widescreen aspect ratio
        imageSize: "1920x1080", // High resolution
        enhancePrompt: true, // Enable LLM-based prompt enhancement for better results
        personGeneration: "allow_all", // Allow generating people in images
        safetySetting: "block_only_high", // Block only high-risk content
        systemInstruction:
          "You are a professional photographer. You are given a prompt and you need to generate a high-quality image based on the prompt.",
        guidanceScale: 15, // Increase guidance scale for better prompt adherence (default is 7)
        negativePrompt: `
          text, words, letters, numbers, watermark, signature, logo, 
          blurry, distorted, low quality, oversaturated, overexposed,
          bad anatomy, deformed features, disfigured, mutated,
          inappropriate content, offensive content, disturbing imagery,
          pixelated, low-resolution, amateur, unprofessional, poorly composed,
          out of focus, poorly lit, harsh shadows, flat lighting, Text,
          watermark, signature, logo, Arabic, Hebrew, Chinese, Japanese, Korean,
          political content, religious symbols, branded content,
          social media elements, user interface elements
        `
          .replace(/\s+/g, " ")
          .trim(),
      },
    };

    console.log(
      "Sending request to Imagen API with prompt:",
      enhancedPrompt.substring(0, 100) + "..."
    );

    // Call the Imagen API
    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error from Imagen API:", errorText);
      throw new Error(
        `Imagen API error: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();

    // Extract the base64 image data
    const base64Image = data.predictions?.[0]?.bytesBase64Encoded;

    if (!base64Image) {
      throw new Error("No image data returned from the API");
    }

    // Generate a unique filename
    const timestamp = Date.now();
    const uniqueId = Math.random().toString(36).substring(7);
    const filename = `generated_images/${timestamp}_${uniqueId}.png`;

    // Save the image to Cloud Storage
    const file = storage.bucket(bucketName).file(filename);
    const imageBuffer = Buffer.from(base64Image, "base64");

    // Extract art direction properties with better defaults
    const artStyle = getArtDirectionProperty(
      artDirection,
      "style",
      "visual_theme.style",
      "photorealistic"
    );
    const artMood = getArtDirectionProperty(
      artDirection,
      "mood",
      "visual_theme.mood",
      "professional"
    );
    const artAtmosphere = getArtDirectionProperty(
      artDirection,
      "atmosphere",
      "visual_theme.atmosphere",
      "dynamic"
    );
    const artEmotionalImpact = getArtDirectionProperty(
      artDirection,
      "emotional_impact",
      "storytelling.emotional_impact",
      "engaging"
    );
    const artVisualFlow = getArtDirectionProperty(
      artDirection,
      "visual_flow",
      "storytelling.visual_flow",
      "balanced and engaging"
    );
    const artLighting = getArtDirectionProperty(
      artDirection,
      "lighting",
      undefined,
      "dramatic"
    );
    const artTextureStyle = getArtDirectionProperty(
      artDirection,
      "texture_style",
      undefined,
      "detailed"
    );

    // Handle arrays with proper defaults
    const artColorPalette = getArrayProperty(artDirection, "color_palette", []);
    const artFocalPoints = getArrayProperty(artDirection, "focal_points", []);
    const artVisualElements = getArrayProperty(
      artDirection,
      "visual_elements",
      []
    );
    const artKeyElements = getArrayProperty(artDirection, "key_elements", []);

    // Add more detailed metadata
    await file.save(imageBuffer, {
      metadata: {
        contentType: "image/png",
        metadata: {
          prompt: enhancedPrompt.substring(0, 1000), // Store the actual prompt used
          originalContent: content,
          timestamp: new Date().toISOString(),
          style: artStyle,
          mood: artMood,
          atmosphere: artAtmosphere,
          emotionalImpact: artEmotionalImpact,
          visualFlow: artVisualFlow,
          lighting: artLighting,
          textureStyle: artTextureStyle,
        },
      },
    });

    // Get a signed URL for the image
    const [signedUrl] = await file.getSignedUrl({
      action: "read",
      expires: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Return the image URL and enhanced metadata
    const result: ImageGenerationResult = {
      url: signedUrl,
      caption: content,
      style_used: artStyle,
      mood: artMood,
      dimensions: {
        width: "1920",
        height: "1080",
      },
      color_palette: artColorPalette,
      focal_points: artFocalPoints,
      visual_elements: artVisualElements,
      lighting: artLighting,
      texture_style: artTextureStyle,
      atmosphere: artAtmosphere,
      emotional_impact: artEmotionalImpact,
      visual_flow: artVisualFlow,
      key_elements: artKeyElements,
      processed_at: new Date().toISOString(),
    };

    res.status(200).send(result);
  } catch (error) {
    console.error("Error generating image:", error);
    res.status(500).send({
      error: "Error generating image",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Helper function to get property from nested object with fallback
function getArtDirectionProperty(
  artDirection: any,
  directProp: string,
  nestedProp?: string,
  defaultValue: string = ""
): string {
  if (!artDirection) return defaultValue;

  // Try direct property first
  if (artDirection[directProp]) return artDirection[directProp];

  // Try nested property if provided
  if (nestedProp) {
    const parts = nestedProp.split(".");
    let value = artDirection;
    for (const part of parts) {
      value = value?.[part];
      if (value === undefined) break;
    }
    if (value) return value;
  }

  return defaultValue;
}

// Helper function to get array property with proper defaults
function getArrayProperty(
  artDirection: any,
  prop: string,
  defaultValue: any[] = []
): string[] {
  if (!artDirection) return defaultValue;

  const value = artDirection[prop];
  if (!value) return defaultValue;

  // Handle different array formats
  if (Array.isArray(value)) {
    // Handle objects with element/placement or color/purpose properties
    if (value.length > 0 && typeof value[0] === "object") {
      if ("element" in value[0] && "placement" in value[0]) {
        return value.map((item) => `${item.element} ${item.placement}`);
      }
      if ("color" in value[0] && "purpose" in value[0]) {
        return value.map((item) => `${item.color} for ${item.purpose}`);
      }
    }
    return value;
  }

  return defaultValue;
}

// Helper function to construct an enhanced prompt using art direction
function constructEnhancedPrompt(
  content: string,
  artDirection: any,
  textAnalysis: any,
  sentimentAnalysis: any,
  storyAnalysis: any,
  cultureFitAnalysis: any,
  qualityCheck: any
): string {
  if (!artDirection) {
    return `Create a professional, high-quality photorealistic artwork that captures the essence of this request: ${content}`;
  }

  // Extract key information from all analysis results
  const keyThemes = textAnalysis?.key_themes || [];
  const sentiment = sentimentAnalysis?.sentiment || "neutral";
  const storyElements = storyAnalysis?.narrative_elements || [];
  const culturalContext =
    cultureFitAnalysis?.cultural_context || "contemporary";
  const qualityRating = qualityCheck?.quality_rating || "high";

  // Extract art direction elements with better defaults
  const style = getArtDirectionProperty(
    artDirection,
    "style",
    "visual_theme.style",
    "photorealistic"
  );
  const mood = getArtDirectionProperty(
    artDirection,
    "mood",
    "visual_theme.mood",
    "professional"
  );
  const atmosphere = getArtDirectionProperty(
    artDirection,
    "atmosphere",
    "visual_theme.atmosphere",
    "dynamic"
  );
  const emotionalImpact = getArtDirectionProperty(
    artDirection,
    "emotional_impact",
    "storytelling.emotional_impact",
    sentiment === "positive"
      ? "uplifting and inspiring"
      : sentiment === "negative"
      ? "thought-provoking and dramatic"
      : "balanced and nuanced"
  );
  const visualFlow = getArtDirectionProperty(
    artDirection,
    "visual_flow",
    "storytelling.visual_flow",
    "dynamic and engaging"
  );

  // Handle arrays with proper defaults
  const visualElements = getArrayProperty(
    artDirection,
    "visual_elements",
    []
  ).join(", ");
  const focalPoints = getArrayProperty(artDirection, "focal_points", []).join(
    ", "
  );
  const colorPalette = getArrayProperty(artDirection, "color_palette", []).join(
    ", "
  );
  const keyElements = getArrayProperty(artDirection, "key_elements", []).join(
    ", "
  );

  // Get composition with fallback
  const composition =
    artDirection.composition?.layout ||
    "balanced composition with clear focal points";

  // Incorporate themes from text analysis
  const themeElements =
    keyThemes.length > 0
      ? `Thematic Elements:\n- Incorporate these key themes: ${keyThemes.join(
          ", "
        )}\n- Ensure the visual storytelling aligns with these themes\n`
      : "";

  // Incorporate story elements
  const narrativeElements =
    storyElements.length > 0
      ? `Narrative Elements:\n- Visual storytelling that conveys: ${storyElements.join(
          ", "
        )}\n- Create a sense of ${
          sentiment === "positive"
            ? "optimism and possibility"
            : sentiment === "negative"
            ? "tension and drama"
            : "balance and harmony"
        }\n`
      : "";

  // Build a more detailed and structured prompt
  return `Create a stunning, professional ${style} artwork with the following specifications:

Note: All flags should be Qatar flag, which is a white flag with a red and black diagonal cross from the top left to the bottom right, and don't include any other flags in the image.

Main Subject and Composition:
- Primary Focus: ${focalPoints || "balanced, visually interesting elements"}
- Layout: ${composition}
- Perspective: Dynamic and engaging viewpoint with perfect composition
- Scene Composition: ${visualFlow}
- Cultural Context: ${culturalContext}

Artistic Style:
- Primary Style: ${style} with exceptional attention to detail
- Mood: ${mood}
- Atmosphere: ${atmosphere}
- Visual Impact: ${emotionalImpact}
- Visual Elements: ${visualElements}

${themeElements}
${narrativeElements}

Technical Specifications:
- Ultra-high resolution with sharp, crisp details
- Professional color grading with perfect balance
- Cinematic lighting with dramatic highlights and subtle shadows
- Smooth anti-aliasing and clean edges
- Premium post-processing effects
- Photorealistic textures and materials

Visual Elements:
- Color Palette: ${
    colorPalette || "rich, harmonious colors with perfect balance"
  }
- Lighting: Dramatic and atmospheric with perfect exposure
- Textures: Rich, detailed, and realistic
- Depth: Multiple layers with foreground, midground, and background elements
- Composition: Rule of thirds with perfect balance

Key Elements:
${
  keyElements ||
  "- Balanced visual hierarchy\n- Strong focal point\n- Complementary color scheme"
}

Additional Requirements:
- No text or typography elements
- Avoid human faces or recognizable individuals
- Focus on abstract or artistic interpretation
- Create a gallery-quality aesthetic
- Maintain professional and safe content
- Quality level: ${qualityRating}

Style Enhancements:
- Add subtle light effects and atmospheric elements
- Ensure perfect perspective and proportions
- Create visual harmony and balance
- Incorporate subtle details that reward closer inspection
- Include delicate details and intricate patterns
- Create smooth transitions between elements
- Incorporate subtle texture overlays

Original Content: "${content}"

`;
}

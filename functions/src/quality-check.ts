import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

export const qualityCheck = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        overall_score: {
          type: SchemaType.STRING,
          description: "Overall quality score as a string from '1' to '10'",
        },
        readability_score: {
          type: SchemaType.STRING,
          description: "Readability score as a string from '1' to '10'",
        },
        engagement_score: {
          type: SchemaType.STRING,
          description: "Engagement level score as a string from '1' to '10'",
        },
        uniqueness_score: {
          type: SchemaType.STRING,
          description: "Uniqueness score as a string from '1' to '10'",
        },
        feedback: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 specific feedback points",
          items: {
            type: SchemaType.STRING,
          },
        },
        improvements: {
          type: SchemaType.ARRAY,
          description: "List of 3-5 actionable improvement suggestions",
          items: {
            type: SchemaType.STRING,
          },
        },
      },
      required: [
        "overall_score",
        "readability_score",
        "engagement_score",
        "uniqueness_score",
        "feedback",
        "improvements",
      ],
    };

    const model = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are an expert quality checker that analyzes the quality of this content.",
    });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `
Analyze the quality of this content.

The analysis should include:
- Overall quality score (as a string between '1' to '10')
- Readability score (as a string between '1' to '10')
- Engagement level score (as a string between '1' to '10')
- Uniqueness score (as a string between '1' to '10')
- List of 3-5 specific feedback points
- List of 3-5 actionable improvement suggestions

Content to analyze: "${req.body.content}"

Text analysis agent results: ${JSON.stringify(req.body.textAnalysis)}
Sentiment analysis agent results: ${JSON.stringify(req.body.sentimentAnalysis)}
Story analysis agent results: ${JSON.stringify(req.body.storyAnalysis)}
Culture fit analysis agent results: ${JSON.stringify(
                req.body.cultureFitAnalysis
              )}
Please provide a structured response following the schema.`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2048,
        topP: 0.8,
        topK: 40,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const response = await result.response;
    const data = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!data) {
      throw new Error("No response generated from the model");
    }

    let jsonResponse;
    try {
      jsonResponse = JSON.parse(data);
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      jsonResponse = {};
    }

    res.status(200).send(jsonResponse);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send({ error: "An error occurred" });
  }
});

import { onRequest } from "firebase-functions/v2/https";
import { vertexAI } from "./vertexai";
import { ResponseSchema, SchemaType } from "@google-cloud/vertexai";

export const storyGenerator = onRequest({ cors: true }, async (req, res) => {
  try {
    const responseSchema: ResponseSchema = {
      type: SchemaType.OBJECT,
      properties: {
        title: {
          type: SchemaType.STRING,
          description: "A compelling title for the story",
        },
        tone: {
          type: SchemaType.STRING,
          description:
            "The overall tone of the story (inspirational, dramatic, humorous, etc.)",
        },
        story: {
          type: SchemaType.STRING,
          description: "The generated story content (250-500 words)",
        },
        style: {
          type: SchemaType.STRING,
          description:
            "The narrative style used (descriptive, conversational, poetic, etc.)",
        },
      },
      required: ["title", "tone", "story", "style"],
    };

    const model = vertexAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction:
        "You are an expert story generator that generates a story based on the given prompt.",
    });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `
Generate a story based on the given prompt.

The story should include:
- A compelling title
- The overall tone of the story (inspirational, dramatic, humorous, etc.)
- The generated story content (250-500 words)
- The narrative style used (descriptive, conversational, poetic, etc.)

Content to analyze: "${req.body.content}"

Text analysis agent results: ${JSON.stringify(req.body.textAnalysis)}
Sentiment analysis agent results: ${JSON.stringify(req.body.sentimentAnalysis)}

Please provide a structured response following the schema.`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2048,
        topP: 0.8,
        topK: 40,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const response = await result.response;
    const data = response.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!data) {
      throw new Error("No response generated from the model");
    }

    let jsonResponse;
    try {
      jsonResponse = JSON.parse(data);
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      jsonResponse = {};
    }

    res.status(200).send(jsonResponse);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send({ error: "An error occurred" });
  }
});

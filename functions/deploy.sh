#!/bin/bash

# Build the functions
echo "Building functions..."
npm run build

# Create a temporary firebase.json that doesn't include the predeploy hooks
echo "Creating temporary deployment configuration..."
cat > temp-firebase.json << EOL
{
  "functions": {
    "source": "functions"
  }
}
EOL

# Deploy using the temporary configuration
echo "Deploying functions..."
firebase deploy --only functions --config temp-firebase.json

# Clean up
echo "Cleaning up..."
rm temp-firebase.json

echo "Deployment complete!" 
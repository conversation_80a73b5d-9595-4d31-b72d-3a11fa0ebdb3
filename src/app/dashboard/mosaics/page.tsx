"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { db } from "@/lib/firebase";
import { collection, query, getDocs, orderBy } from "firebase/firestore";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DownloadIcon,
  EnterFullScreenIcon,
  ExternalLinkIcon,
  CalendarIcon,
  DashIcon,
  ImageIcon,
  TextIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@radix-ui/react-icons";
import { Separator } from "@/components/ui/separator";

interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

interface MosaicData {
  id: string;
  url: string;
  hashtag: string;
  created_at: FirestoreTimestamp;
  prompt: string;
  postIds: string[];
  platform: string;
}

export default function MosaicsPage() {
  const [mosaics, setMosaics] = useState<MosaicData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMosaic, setSelectedMosaic] = useState<MosaicData | null>(null);
  const [scrollPositions, setScrollPositions] = useState<
    Record<string, number>
  >({});

  useEffect(() => {
    fetchMosaics();
  }, []);

  const fetchMosaics = async () => {
    try {
      const mosaicsRef = collection(db, "mosaics");
      const q = query(mosaicsRef, orderBy("created_at", "desc"));

      const querySnapshot = await getDocs(q);
      const mosaicDocs = querySnapshot.docs.map((doc) => ({
        ...(doc.data() as Omit<MosaicData, "id">),
        id: doc.id,
      }));

      setMosaics(mosaicDocs);
    } catch (err) {
      console.error("Error fetching mosaics:", err);
    } finally {
      setLoading(false);
    }
  };

  const groupMosaicsByHashtag = () => {
    const groups = mosaics.reduce((acc, mosaic) => {
      const hashtag = mosaic.hashtag || "Uncategorized";
      if (!acc[hashtag]) {
        acc[hashtag] = [];
      }
      acc[hashtag].push(mosaic);
      return acc;
    }, {} as Record<string, MosaicData[]>);

    return Object.entries(groups).sort(([a], [b]) => a.localeCompare(b));
  };

  const handleDownload = (url: string) => {
    window.open(url, "_blank");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-10rem)]">
        <div className="text-lg text-muted-foreground">Loading mosaics...</div>
      </div>
    );
  }

  const groupedMosaics = groupMosaicsByHashtag();

  const handleScroll = (hashtag: string, direction: "left" | "right") => {
    const container = document.getElementById(`slider-${hashtag}`);
    if (container) {
      const scrollAmount = 800; // Scroll two images at a time (400px * 2)
      const newPosition = scrollPositions[hashtag] || 0;
      const targetPosition =
        direction === "left"
          ? Math.max(0, newPosition - scrollAmount)
          : newPosition + scrollAmount;

      container.scrollTo({
        left: targetPosition,
        behavior: "smooth",
      });

      setScrollPositions((prev) => ({
        ...prev,
        [hashtag]: targetPosition,
      }));
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Generated Mosaics</CardTitle>
          <CardDescription>
            View all your generated mosaic artworks grouped by hashtag
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {groupedMosaics.map(([hashtag, mosaics]) => (
              <div key={hashtag} className="space-y-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">
                    {hashtag === "Uncategorized" ? hashtag : `#${hashtag}`}
                  </h3>
                  <Badge variant="secondary">{mosaics.length}</Badge>
                </div>
                <div className="relative">
                  <div className="relative group">
                    <div
                      id={`slider-${hashtag}`}
                      className="flex gap-4 overflow-x-hidden scroll-smooth"
                    >
                      {mosaics.map((mosaic) => (
                        <div
                          key={mosaic.id}
                          className="relative w-[400px] flex-none"
                        >
                          <div className="relative aspect-video w-full rounded-lg overflow-hidden border group">
                            {mosaic.url ? (
                              <Image
                                src={mosaic.url}
                                alt="Generated mosaic"
                                fill
                                className="object-cover transition-transform group-hover:scale-105"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full bg-muted">
                                <ImageIcon className="h-8 w-8 text-muted-foreground" />
                              </div>
                            )}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                            <div className="absolute bottom-0 left-0 right-0 p-3 flex justify-between items-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  className="bg-black/50 hover:bg-black/70"
                                  onClick={() => handleDownload(mosaic.url)}
                                >
                                  <DownloadIcon className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  className="bg-black/50 hover:bg-black/70"
                                  onClick={() => setSelectedMosaic(mosaic)}
                                >
                                  <EnterFullScreenIcon className="h-4 w-4" />
                                </Button>
                              </div>
                              <Badge
                                variant="secondary"
                                className="bg-black/50 text-white"
                              >
                                {mosaic.platform}
                              </Badge>
                            </div>
                          </div>
                          <div className="mt-2 text-sm text-muted-foreground line-clamp-2">
                            {new Date(
                              mosaic.created_at.seconds * 1000
                            ).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                    </div>
                    {mosaics.length > 2 && (
                      <>
                        <Button
                          variant="outline"
                          size="icon"
                          className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background"
                          onClick={() => handleScroll(hashtag, "left")}
                        >
                          <ChevronLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background"
                          onClick={() => handleScroll(hashtag, "right")}
                        >
                          <ChevronRightIcon className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {groupedMosaics.length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                No mosaics generated yet. Go to the Mosaic Generator to create
                some!
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog
        open={!!selectedMosaic}
        onOpenChange={() => setSelectedMosaic(null)}
      >
        <DialogContent className="max-w-7xl h-[90vh]">
          <DialogHeader>
            <DialogTitle>Mosaic Details</DialogTitle>
          </DialogHeader>
          <div className="flex gap-6 h-full overflow-hidden">
            <div className="relative flex-1 min-h-0">
              {selectedMosaic && (
                <Image
                  src={selectedMosaic.url}
                  alt="Generated mosaic"
                  fill
                  className="object-contain rounded-lg"
                />
              )}
            </div>
            <div className="w-80 flex flex-col">
              {selectedMosaic && (
                <>
                  <div className="flex justify-end gap-2 mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownload(selectedMosaic.url)}
                    >
                      <DownloadIcon className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(selectedMosaic.url, "_blank")}
                    >
                      <ExternalLinkIcon className="h-4 w-4 mr-2" />
                      Open
                    </Button>
                  </div>
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <CalendarIcon className="h-4 w-4" />
                        <span>
                          {new Date(
                            selectedMosaic.created_at.seconds * 1000
                          ).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <DashIcon className="h-4 w-4" />
                        <span>
                          #{selectedMosaic.hashtag || "Uncategorized"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <ImageIcon className="h-4 w-4" />
                        <span>
                          {selectedMosaic.postIds.length} source posts
                        </span>
                      </div>
                    </div>
                    <Separator />
                    <div className="space-y-2">
                      <h4 className="font-medium flex items-center gap-2">
                        <TextIcon className="h-4 w-4" />
                        Prompt
                      </h4>
                      <div className="max-h-[200px] overflow-y-auto text-sm text-muted-foreground">
                        {selectedMosaic.prompt}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  PlayIcon,
  PauseIcon,
  SettingsIcon,
  ActivityIcon,
  DatabaseIcon,
  AlertCircleIcon,
} from "lucide-react";
import { PlatformStatusCard } from "@/components/ingestion/platform-status-card";
import { IngestionMetrics } from "@/components/ingestion/ingestion-metrics";
import { PlatformConfiguration } from "@/components/ingestion/platform-configuration";
import { IngestionLogs } from "@/components/ingestion/ingestion-logs";
import { StreamMonitor } from "@/components/ingestion/stream-monitor";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

const initialPlatformData: PlatformStatus[] = [
  {
    platform: "X (Twitter)",
    status: "active",
    lastSync: "2024-01-15T10:30:00Z",
    recordsIngested: 15420,
    errorCount: 3,
    apiQuotaUsed: 75,
    apiQuotaLimit: 100,
    rateLimitRemaining: 850,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "healthy",
  },
  {
    platform: "Meta",
    status: "active",
    lastSync: "2024-01-15T10:28:00Z",
    recordsIngested: 8760,
    errorCount: 1,
    apiQuotaUsed: 60,
    apiQuotaLimit: 100,
    rateLimitRemaining: 1200,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "healthy",
  },
  {
    platform: "YouTube",
    status: "error",
    lastSync: "2024-01-15T09:45:00Z",
    recordsIngested: 3240,
    errorCount: 15,
    apiQuotaUsed: 95,
    apiQuotaLimit: 100,
    rateLimitRemaining: 50,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "critical",
  },
  {
    platform: "TikTok",
    status: "maintenance",
    lastSync: "2024-01-15T08:30:00Z",
    recordsIngested: 0,
    errorCount: 0,
    apiQuotaUsed: 0,
    apiQuotaLimit: 100,
    rateLimitRemaining: 1000,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "degraded",
  },
];

export default function IngestionDashboard() {
  const [platforms, setPlatforms] =
    useState<PlatformStatus[]>(initialPlatformData);
  const [selectedPlatform, setSelectedPlatform] = useState<string>("overview");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setPlatforms((prev) =>
        prev.map((platform) => ({
          ...platform,
          recordsIngested:
            platform.status === "active"
              ? platform.recordsIngested + Math.floor(Math.random() * 10)
              : platform.recordsIngested,
          lastSync:
            platform.status === "active"
              ? new Date().toISOString()
              : platform.lastSync,
        }))
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const totalRecords = platforms.reduce((sum, p) => sum + p.recordsIngested, 0);
  const totalErrors = platforms.reduce((sum, p) => sum + p.errorCount, 0);
  const activePlatforms = platforms.filter((p) => p.status === "active").length;
  const avgQuotaUsed =
    platforms.reduce((sum, p) => sum + p.apiQuotaUsed, 0) / platforms.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Ingestion</h1>
          <p className="text-muted-foreground">
            Monitor and manage social media platform integrations
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <ActivityIcon className="h-4 w-4 mr-2" />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
          <Button>
            <SettingsIcon className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
            <DatabaseIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalRecords.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              +12.5% from last hour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Platforms
            </CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePlatforms}/4</div>
            <p className="text-xs text-muted-foreground">
              {activePlatforms === 4
                ? "All systems operational"
                : "Some issues detected"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Count</CardTitle>
            <AlertTriangleIcon className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalErrors}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg API Usage</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgQuotaUsed.toFixed(0)}%</div>
            <Progress value={avgQuotaUsed} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
          <TabsTrigger value="monitoring">Stream Monitor</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Platform Status Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {platforms.map((platform) => (
                  <PlatformStatusCard
                    key={platform.platform}
                    platform={platform}
                  />
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Ingestion Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <IngestionMetrics platforms={platforms} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {platforms.map((platform) => (
              <Card key={platform.platform}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {platform.platform}
                      <Badge
                        variant={
                          platform.status === "active"
                            ? "default"
                            : platform.status === "error"
                            ? "destructive"
                            : platform.status === "maintenance"
                            ? "secondary"
                            : "outline"
                        }
                      >
                        {platform.status}
                      </Badge>
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <PlayIcon className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <PauseIcon className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <SettingsIcon className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Records Ingested</p>
                      <p className="font-medium">
                        {platform.recordsIngested.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Errors</p>
                      <p className="font-medium text-red-600">
                        {platform.errorCount}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">API Quota</p>
                      <p className="font-medium">{platform.apiQuotaUsed}%</p>
                      <Progress
                        value={platform.apiQuotaUsed}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <p className="text-muted-foreground">Rate Limit</p>
                      <p className="font-medium">
                        {platform.rateLimitRemaining}
                      </p>
                    </div>
                  </div>
                  <div className="pt-2 border-t">
                    <p className="text-xs text-muted-foreground">
                      Last sync: {new Date(platform.lastSync).toLocaleString()}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="configuration">
          <PlatformConfiguration platforms={platforms} />
        </TabsContent>

        <TabsContent value="logs">
          <IngestionLogs />
        </TabsContent>

        <TabsContent value="monitoring">
          <StreamMonitor platforms={platforms} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { StreamMonitor } from "@/components/ingestion/stream-monitor";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

const initialPlatformData: PlatformStatus[] = [
  {
    platform: "X (Twitter)",
    status: "active",
    lastSync: "2024-01-15T10:30:00Z",
    recordsIngested: 15420,
    errorCount: 3,
    apiQuotaUsed: 75,
    apiQuotaLimit: 100,
    rateLimitRemaining: 850,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "healthy",
  },
  {
    platform: "Meta",
    status: "active",
    lastSync: "2024-01-15T10:28:00Z",
    recordsIngested: 8760,
    errorCount: 1,
    apiQuotaUsed: 60,
    apiQuotaLimit: 100,
    rateLimitRemaining: 1200,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "healthy",
  },
  {
    platform: "YouTube",
    status: "error",
    lastSync: "2024-01-15T09:45:00Z",
    recordsIngested: 3240,
    errorCount: 15,
    apiQuotaUsed: 95,
    apiQuotaLimit: 100,
    rateLimitRemaining: 50,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "critical",
  },
  {
    platform: "TikTok",
    status: "maintenance",
    lastSync: "2024-01-15T08:30:00Z",
    recordsIngested: 0,
    errorCount: 0,
    apiQuotaUsed: 0,
    apiQuotaLimit: 100,
    rateLimitRemaining: 1000,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "degraded",
  },
];

export default function StreamMonitorPage() {
  const [platforms, setPlatforms] =
    useState<PlatformStatus[]>(initialPlatformData);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setPlatforms((prev) =>
        prev.map((platform) => ({
          ...platform,
          recordsIngested:
            platform.status === "active"
              ? platform.recordsIngested + Math.floor(Math.random() * 10)
              : platform.recordsIngested,
          lastSync:
            platform.status === "active"
              ? new Date().toISOString()
              : platform.lastSync,
        }))
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">Stream Monitor</h1>
        <p className="text-muted-foreground">
          Real-time monitoring and analysis of data ingestion streams
        </p>
      </div>

      {/* Stream Monitor Component */}
      <StreamMonitor platforms={platforms} />
    </div>
  );
}

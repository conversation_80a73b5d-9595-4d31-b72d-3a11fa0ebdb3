"use client";

import { useState } from "react";
import { PlatformConfiguration } from "@/components/ingestion/platform-configuration";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  ShieldIcon,
  KeyIcon,
  SaveIcon,
  TestTubeIcon,
} from "lucide-react";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

const initialPlatformData: PlatformStatus[] = [
  {
    platform: "X (Twitter)",
    status: "active",
    lastSync: "2024-01-15T10:30:00Z",
    recordsIngested: 15420,
    errorCount: 3,
    apiQuotaUsed: 75,
    apiQuotaLimit: 100,
    rateLimitRemaining: 850,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "healthy",
  },
  {
    platform: "Meta",
    status: "active",
    lastSync: "2024-01-15T10:28:00Z",
    recordsIngested: 8760,
    errorCount: 1,
    apiQuotaUsed: 60,
    apiQuotaLimit: 100,
    rateLimitRemaining: 1200,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "healthy",
  },
  {
    platform: "YouTube",
    status: "error",
    lastSync: "2024-01-15T09:45:00Z",
    recordsIngested: 3240,
    errorCount: 15,
    apiQuotaUsed: 95,
    apiQuotaLimit: 100,
    rateLimitRemaining: 50,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "critical",
  },
  {
    platform: "TikTok",
    status: "maintenance",
    lastSync: "2024-01-15T08:30:00Z",
    recordsIngested: 0,
    errorCount: 0,
    apiQuotaUsed: 0,
    apiQuotaLimit: 100,
    rateLimitRemaining: 1000,
    nextResetTime: "2024-01-15T11:00:00Z",
    streamHealth: "degraded",
  },
];

export default function ConfigurationPage() {
  const [platforms] = useState<PlatformStatus[]>(initialPlatformData);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600";
      case "error":
        return "text-red-600";
      case "maintenance":
        return "text-yellow-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case "error":
        return <AlertTriangleIcon className="h-4 w-4 text-red-600" />;
      default:
        return <AlertTriangleIcon className="h-4 w-4 text-yellow-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">Platform Configuration</h1>
        <p className="text-muted-foreground">
          Configure API credentials, settings, and security for social media
          platform integrations
        </p>
      </div>

      {/* Security Notice */}
      <Card className="border-l-4 border-l-yellow-500 bg-yellow-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <ShieldIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">Security Notice</h3>
              <p className="text-sm text-yellow-700 mt-1">
                All API credentials are encrypted at rest and in transit.
                Configuration changes require administrator approval in
                production environments.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <KeyIcon className="h-5 w-5" />
            Platform Status Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {platforms.map((platform) => (
              <div
                key={platform.platform}
                className="p-4 border rounded-lg space-y-2"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{platform.platform}</h4>
                  {getStatusIcon(platform.status)}
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Status</span>
                    <Badge
                      variant={
                        platform.status === "active" ? "default" : "secondary"
                      }
                      className={getStatusColor(platform.status)}
                    >
                      {platform.status}
                    </Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">API Quota</span>
                    <span className="font-medium">
                      {platform.apiQuotaUsed}%
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Health</span>
                    <span
                      className={`font-medium ${
                        platform.streamHealth === "healthy"
                          ? "text-green-600"
                          : platform.streamHealth === "degraded"
                          ? "text-yellow-600"
                          : "text-red-600"
                      }`}
                    >
                      {platform.streamHealth}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Configuration Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button>
              <TestTubeIcon className="h-4 w-4 mr-2" />
              Test All Connections
            </Button>
            <Button variant="outline">
              <SaveIcon className="h-4 w-4 mr-2" />
              Backup Configurations
            </Button>
            <Button variant="outline">
              <ShieldIcon className="h-4 w-4 mr-2" />
              Security Audit
            </Button>
            <Button variant="outline">
              <KeyIcon className="h-4 w-4 mr-2" />
              Rotate API Keys
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Configuration Component */}
      <PlatformConfiguration platforms={platforms} />

      {/* Configuration Help */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration Help</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Platform-Specific Guides</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    X (Twitter) API v2 Setup Guide
                  </a>
                </li>
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    Meta Graph API Configuration
                  </a>
                </li>
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    YouTube Data API v3 Setup
                  </a>
                </li>
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    TikTok Business API Integration
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Troubleshooting</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    Authentication Error Resolution
                  </a>
                </li>
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    Rate Limit Management
                  </a>
                </li>
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    Webhook Configuration Issues
                  </a>
                </li>
                <li>
                  <a href="#" className="text-blue-600 hover:underline">
                    Security Best Practices
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

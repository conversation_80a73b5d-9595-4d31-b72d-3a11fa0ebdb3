"use client";

import { IngestionLogs } from "@/components/ingestion/ingestion-logs";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  InfoIcon,
  XCircleIcon,
  DownloadIcon,
  SettingsIcon,
  FileTextIcon,
} from "lucide-react";

export default function LogsPage() {
  const logStats = {
    total: 15847,
    errors: 23,
    warnings: 156,
    info: 14892,
    success: 776,
  };

  const recentAlerts = [
    {
      id: 1,
      level: "error" as const,
      platform: "YouTube",
      message: "API quota exceeded - requests throttled",
      timestamp: "2024-01-15T10:45:00Z",
    },
    {
      id: 2,
      level: "warning" as const,
      platform: "X (Twitter)",
      message: "Rate limit approaching 80% threshold",
      timestamp: "2024-01-15T10:30:00Z",
    },
    {
      id: 3,
      level: "error" as const,
      platform: "TikTok",
      message: "Connection timeout after 30 seconds",
      timestamp: "2024-01-15T10:15:00Z",
    },
  ];

  const getLevelIcon = (level: string) => {
    switch (level) {
      case "error":
        return <XCircleIcon className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangleIcon className="h-4 w-4 text-yellow-600" />;
      case "success":
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      default:
        return <InfoIcon className="h-4 w-4 text-blue-600" />;
    }
  };

  const getLevelVariant = (level: string) => {
    switch (level) {
      case "error":
        return "destructive";
      case "warning":
        return "secondary";
      case "success":
        return "default";
      default:
        return "outline";
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">Ingestion Logs</h1>
        <p className="text-muted-foreground">
          Monitor and analyze real-time logs from all data ingestion processes
        </p>
      </div>

      {/* Log Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Logs</p>
                <p className="text-2xl font-bold">
                  {logStats.total.toLocaleString()}
                </p>
              </div>
              <FileTextIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Errors</p>
                <p className="text-2xl font-bold text-red-600">
                  {logStats.errors}
                </p>
              </div>
              <XCircleIcon className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Warnings</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {logStats.warnings}
                </p>
              </div>
              <AlertTriangleIcon className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success</p>
                <p className="text-2xl font-bold text-green-600">
                  {logStats.success}
                </p>
              </div>
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Info</p>
                <p className="text-2xl font-bold">
                  {logStats.info.toLocaleString()}
                </p>
              </div>
              <InfoIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Alerts */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <AlertTriangleIcon className="h-5 w-5" />
              Recent Alerts
            </CardTitle>
            <Button variant="outline" size="sm">
              View All Alerts
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentAlerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border-l-4 ${
                  alert.level === "error"
                    ? "border-red-500 bg-red-50/50"
                    : "border-yellow-500 bg-yellow-50/50"
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getLevelIcon(alert.level)}
                    <span className="font-medium">{alert.platform}</span>
                    <Badge variant={getLevelVariant(alert.level)}>
                      {alert.level}
                    </Badge>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {new Date(alert.timestamp).toLocaleString()}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {alert.message}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Log Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Log Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button>
              <DownloadIcon className="h-4 w-4 mr-2" />
              Export All Logs
            </Button>
            <Button variant="outline">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Export Errors Only
            </Button>
            <Button variant="outline">
              <SettingsIcon className="h-4 w-4 mr-2" />
              Log Retention Settings
            </Button>
            <Button variant="outline">
              <AlertTriangleIcon className="h-4 w-4 mr-2" />
              Alert Configuration
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Logs Component */}
      <IngestionLogs />

      {/* Log Analysis Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Log Analysis Tips</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <InfoIcon className="h-4 w-4" />
                Common Error Patterns
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  • <strong>Authentication errors:</strong> Check API
                  credentials and token expiration
                </li>
                <li>
                  • <strong>Rate limit exceeded:</strong> Review request
                  frequency and implement backoff strategies
                </li>
                <li>
                  • <strong>Connection timeouts:</strong> Verify network
                  connectivity and endpoint availability
                </li>
                <li>
                  • <strong>JSON parsing errors:</strong> Validate API response
                  format changes
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircleIcon className="h-4 w-4" />
                Monitoring Best Practices
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  • Set up alerts for error rate thresholds (&gt;5% errors/hour)
                </li>
                <li>
                  • Monitor API quota usage to prevent service interruptions
                </li>
                <li>• Review logs daily for emerging patterns or issues</li>
                <li>
                  • Archive logs older than 30 days to manage storage costs
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

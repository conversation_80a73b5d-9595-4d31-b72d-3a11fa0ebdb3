/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { InstagramPost } from "@/types";
import { db } from "@/lib/firebase";
import generateArtText from "@/lib/agents/generateArtText";
import {
  collection,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  orderBy,
  addDoc,
} from "firebase/firestore";
import { ReloadIcon, ImageIcon } from "@radix-ui/react-icons";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

interface HashtagCount {
  tag: string;
  count: number;
}

export default function MosaicGeneratorPage() {
  const [numberOfStories, setNumberOfStories] = useState<number>(5);
  const [selectedHashtag, setSelectedHashtag] = useState<string>("");
  const [availableHashtags, setAvailableHashtags] = useState<HashtagCount[]>(
    []
  );
  const [maxAvailablePosts, setMaxAvailablePosts] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPosts, setSelectedPosts] = useState<InstagramPost[]>([]);
  const [generatingMosaic, setGeneratingMosaic] = useState(false);
  const [mosaicUrl, setMosaicUrl] = useState<string | null>(null);
  const [generatedText, setGeneratedText] = useState<string | null>(null);
  const [progressStatus, setProgressStatus] = useState<string>("");
  const [progressValue, setProgressValue] = useState<number>(0);
  const [stats, setStats] = useState({
    totalPosts: 0,
    availablePosts: 0,
    usedInMosaics: 0,
    averageEngagement: 0,
  });

  useEffect(() => {
    fetchStats();
    fetchAvailableHashtags();
  }, []);

  const fetchAvailableHashtags = async () => {
    try {
      const postsRef = collection(db, "social_media_posts");
      const postsSnapshot = await getDocs(
        query(postsRef, where("agents.imageGenerator", "!=", null))
      );

      // Create a map to count posts per hashtag
      const hashtagCounts = new Map<string, number>();
      let totalAvailablePosts = 0;

      postsSnapshot.docs.forEach((doc) => {
        const post = doc.data() as InstagramPost;
        // Only count posts that have an image and haven't been used in mosaics
        if (!post.mosaic_generated && post.agents.imageGenerator?.storageUrl) {
          if (post.hashtag) {
            const currentCount = hashtagCounts.get(post.hashtag) || 0;
            hashtagCounts.set(post.hashtag, currentCount + 1);
          }
          totalAvailablePosts++;
        }
      });

      // Convert to array and sort by count
      const sortedHashtags = Array.from(hashtagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count); // Sort by count descending

      setAvailableHashtags(sortedHashtags);
      setMaxAvailablePosts(totalAvailablePosts);

      // Adjust number of stories if needed
      if (numberOfStories > totalAvailablePosts) {
        setNumberOfStories(Math.max(1, Math.min(totalAvailablePosts, 30)));
      }
    } catch (err) {
      console.error("Error fetching hashtags:", err);
    }
  };

  // Update number of stories when hashtag changes
  useEffect(() => {
    const availableCount = selectedHashtag
      ? availableHashtags.find((h) => h.tag === selectedHashtag)?.count || 0
      : maxAvailablePosts;

    if (numberOfStories > availableCount) {
      setNumberOfStories(Math.max(1, Math.min(availableCount, 30)));
    }
  }, [selectedHashtag, availableHashtags, maxAvailablePosts]);

  const fetchStats = async () => {
    try {
      const postsRef = collection(db, "social_media_posts");

      // Get all completed posts
      const allPostsSnapshot = await getDocs(
        query(postsRef, where("agents.imageGenerator", "!=", null))
      );

      // Convert to array for easier processing
      const allPosts = allPostsSnapshot.docs.map((doc) => ({
        ...(doc.data() as InstagramPost),
        id: doc.id,
      }));

      // Count posts used in mosaics (where mosaic_generated is true)
      const usedInMosaics = allPosts.filter(
        (post) => post.mosaic_generated === true
      ).length;

      // Calculate total engagement
      const totalEngagement = allPosts.reduce(
        (sum, post) => sum + (post.like_count || 0) + (post.comment_count || 0),
        0
      );

      // Filter for posts that have an image URL and are not used in mosaics
      const availablePosts = allPosts.filter(
        (post) =>
          !post.mosaic_generated && post.agents.imageGenerator?.storageUrl
      );

      setStats({
        totalPosts: allPosts.length,
        availablePosts: availablePosts.length,
        usedInMosaics,
        averageEngagement:
          allPosts.length > 0 ? totalEngagement / allPosts.length : 0,
      });
    } catch (err) {
      console.error("Error fetching stats:", err);
    }
  };

  const clearProgress = () => {
    setProgressStatus("");
    setProgressValue(0);
    setGeneratedText(null);
  };

  const generateMosaic = async (posts: InstagramPost[]): Promise<string> => {
    try {
      setProgressValue(10);
      setProgressStatus("Preparing posts data...");

      // Get all captions and create a stories object
      const stories: { [key: string]: string } = {};
      posts.forEach((post, index) => {
        if (post.caption) {
          stories[`${index + 1}`] = post.caption;
        }
      });

      setProgressValue(25);
      setProgressStatus("Generating art text from posts...");

      // Generate art text using the captions
      const artText = await generateArtText(stories);

      setProgressValue(40);
      setProgressStatus("Processing generated text...");

      // Parse the JSON response
      const artTextData = JSON.parse(artText);
      if (!artTextData.art_text) {
        throw new Error("Failed to generate art text");
      }

      setProgressValue(50);
      setGeneratedText(artTextData.art_text);
      setProgressStatus("Creating mosaic artwork...");

      // Call our API endpoint with the generated art text
      const response = await fetch("/api/mosaic", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: artTextData.art_text,
          captions: posts.map((post) => post.caption).filter(Boolean),
        }),
      });

      setProgressValue(75);
      setProgressStatus("Processing mosaic response...");

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to generate mosaic");
      }

      const data = await response.json();
      if (!data.success || !data.url) {
        throw new Error("Failed to generate mosaic");
      }

      setProgressValue(90);
      setProgressStatus("Saving mosaic data...");

      // Save mosaic to dedicated collection
      const mosaicsRef = collection(db, "mosaics");
      await addDoc(mosaicsRef, {
        url: data.url,
        prompt: artTextData.art_text,
        created_at: new Date(),
        hashtag: posts[0].hashtag || "Uncategorized",
        postIds: posts.map((post) => post.id),
        platform: posts[0].platform,
      });

      // Update posts as used in mosaic
      await Promise.all(
        posts.map((post) =>
          post.id
            ? updateDoc(doc(db, "social_media_posts", post.id), {
                mosaic_generated: true,
                mosaic_url: data.url,
              })
            : Promise.resolve()
        )
      );

      setProgressValue(100);
      setProgressStatus("Mosaic generated successfully!");

      // Refresh stats and available hashtags
      await Promise.all([fetchStats(), fetchAvailableHashtags()]);

      return data.url;
    } catch (error) {
      setProgressValue(0);
      setProgressStatus("Error generating mosaic");
      console.error("Error in generateMosaic:", error);
      throw error;
    }
  };

  const handleGenerateMosaic = async () => {
    if (numberOfStories < 1 || numberOfStories > 30) {
      setError("Please choose between 1 and 30 stories");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSelectedPosts([]);
      setMosaicUrl(null);
      clearProgress();

      // Query posts that haven't been used in a mosaic yet
      const postsRef = collection(db, "social_media_posts");
      const q = query(
        postsRef,
        where("agents.imageGenerator", "!=", null),
        orderBy("created_at", "desc")
      );

      const querySnapshot = await getDocs(q);
      let availablePosts = querySnapshot.docs
        .map((doc) => ({ ...(doc.data() as InstagramPost), id: doc.id }))
        .filter(
          (post) =>
            !post.mosaic_generated && post.agents.imageGenerator?.storageUrl
        );

      // Filter by hashtag if selected
      if (selectedHashtag) {
        availablePosts = availablePosts.filter(
          (post) => post.hashtag === selectedHashtag
        );
      }

      // Take the required number of posts
      availablePosts = availablePosts.slice(0, numberOfStories);

      if (availablePosts.length < numberOfStories) {
        setError(
          `Only found ${availablePosts.length} unused posts${
            selectedHashtag ? ` with hashtag #${selectedHashtag}` : ""
          }. Please try with a smaller number${
            selectedHashtag ? " or a different hashtag" : ""
          }.`
        );
        setLoading(false);
        return;
      }

      setSelectedPosts(availablePosts);
      setLoading(false);

      // Start generating mosaic
      setGeneratingMosaic(true);

      try {
        // Generate mosaic image
        const mosaicUrl = await generateMosaic(availablePosts);
        if (!mosaicUrl) {
          throw new Error("Failed to generate mosaic URL");
        }

        setMosaicUrl(mosaicUrl);
        setGeneratingMosaic(false);
      } catch (err) {
        console.error("Error generating or uploading mosaic:", err);
        setError(
          err instanceof Error ? err.message : "Error generating mosaic"
        );
        setGeneratingMosaic(false);
        // Refresh stats in case of partial success
        await Promise.all([fetchStats(), fetchAvailableHashtags()]);
      }
    } catch (err) {
      console.error("Error fetching posts:", err);
      setError(err instanceof Error ? err.message : "Error fetching posts");
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPosts}</div>
            <p className="text-xs text-muted-foreground">
              Total completed posts in database
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Available Posts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.availablePosts}</div>
            <p className="text-xs text-muted-foreground">
              Posts not used in mosaics yet
            </p>
            <Progress
              value={(stats.availablePosts / stats.totalPosts) * 100}
              className="mt-2"
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Used in Mosaics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.usedInMosaics}</div>
            <p className="text-xs text-muted-foreground">
              Posts already used in mosaics
            </p>
            <Progress
              value={(stats.usedInMosaics / stats.totalPosts) * 100}
              className="mt-2"
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Engagement
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(stats.averageEngagement).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Average likes & comments per post
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Mosaic Generator</CardTitle>
          <CardDescription>
            Create beautiful mosaics from your Meta posts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="grid w-full max-w-sm items-center gap-4">
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="hashtag">Filter by Hashtag (Optional)</Label>
                  <select
                    id="hashtag"
                    value={selectedHashtag}
                    onChange={(e) => setSelectedHashtag(e.target.value)}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="">
                      All Posts ({maxAvailablePosts} available)
                    </option>
                    {availableHashtags.map(
                      ({ tag, count }) =>
                        count >= 1 && (
                          <option key={tag} value={tag}>
                            #{tag} ({count} available)
                          </option>
                        )
                    )}
                  </select>
                  <p className="text-xs text-muted-foreground">
                    Showing all available hashtags
                  </p>
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="stories">Number of Stories</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      id="stories"
                      value={numberOfStories}
                      onChange={(e) => {
                        const value = Number(e.target.value);
                        const maxPosts = selectedHashtag
                          ? availableHashtags.find(
                              (h) => h.tag === selectedHashtag
                            )?.count || 0
                          : maxAvailablePosts;
                        setNumberOfStories(
                          Math.max(1, Math.min(value, Math.min(maxPosts, 30)))
                        );
                      }}
                      min={1}
                      max={Math.min(
                        selectedHashtag
                          ? availableHashtags.find(
                              (h) => h.tag === selectedHashtag
                            )?.count || 30
                          : maxAvailablePosts,
                        30
                      )}
                      className="w-32"
                    />
                    <Button
                      onClick={handleGenerateMosaic}
                      disabled={
                        loading ||
                        generatingMosaic ||
                        (selectedHashtag
                          ? (availableHashtags.find(
                              (h) => h.tag === selectedHashtag
                            )?.count || 0) < 1
                          : maxAvailablePosts < 1)
                      }
                    >
                      {(loading || generatingMosaic) && (
                        <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      {!loading && !generatingMosaic && (
                        <ImageIcon className="mr-2 h-4 w-4" />
                      )}
                      Generate Mosaic
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {selectedHashtag
                      ? `Choose between 1 and ${Math.min(
                          availableHashtags.find(
                            (h) => h.tag === selectedHashtag
                          )?.count || 30,
                          30
                        )} posts`
                      : `Choose between 1 and ${Math.min(
                          maxAvailablePosts,
                          30
                        )} posts`}
                  </p>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>

            <div className="space-y-4">
              {(loading || generatingMosaic) && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium">{progressStatus}</span>
                  </div>
                  <Progress value={progressValue} className="w-full" />
                  <p className="text-xs text-muted-foreground text-right">
                    {progressValue}%
                  </p>
                </div>
              )}

              {generatedText && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">
                      Generated Art Description
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[200px] w-full rounded-md border p-4">
                      <p className="text-sm text-muted-foreground">
                        {generatedText}
                      </p>
                    </ScrollArea>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </CardContent>
        <Separator />
        <CardFooter className="mt-4">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Badge variant="outline">{stats.availablePosts}</Badge>
              <span>Available posts</span>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2">
              <Badge variant="outline">{stats.usedInMosaics}</Badge>
              <span>Used in mosaics</span>
            </div>
          </div>
        </CardFooter>
      </Card>

      {mosaicUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Artwork</CardTitle>
            <CardDescription>
              Your artwork has been generated successfully
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative aspect-video rounded-lg overflow-hidden border">
              <Image
                src={mosaicUrl}
                alt="Generated mosaic"
                fill
                className="object-cover"
              />
            </div>
            <div className="mt-4 flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                Created from {selectedPosts.length} posts
              </div>
              <Button onClick={() => window.open(mosaicUrl, "_blank")}>
                Download Artwork
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      {selectedPosts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Selected Posts</CardTitle>
            <CardDescription>
              {selectedPosts.length} posts will be used to create your mosaic
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px]">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {selectedPosts.map((post, index) => (
                  <div
                    key={post.id}
                    className="relative aspect-square rounded-md overflow-hidden border group"
                  >
                    {post.agents.imageGenerator?.storageUrl && (
                      <Image
                        src={post.agents.imageGenerator.storageUrl}
                        alt={post.caption || "Post image"}
                        fill
                        className="object-cover transition-transform group-hover:scale-110"
                      />
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                    <div className="absolute bottom-2 right-2 left-2 flex justify-between items-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Badge
                        variant="secondary"
                        className="bg-black/50 text-white"
                      >
                        #{index + 1}
                      </Badge>
                      <Badge
                        variant="secondary"
                        className="bg-black/50 text-white"
                      >
                        {post.platform}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              Total Engagement:{" "}
              {selectedPosts
                .reduce(
                  (sum, post) => sum + post.like_count + post.comment_count,
                  0
                )
                .toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">
              Average Likes:{" "}
              {Math.round(
                selectedPosts.reduce((sum, post) => sum + post.like_count, 0) /
                  selectedPosts.length
              ).toLocaleString()}
            </div>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}

"use client";

import { useState, useRef, useEffect, useMemo } from "react";
import { db } from "@/lib/firebase";
import {
  collection,
  doc,
  getDocs,
  updateDoc,
  query,
  orderBy,
  writeBatch,
  where,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  DocumentData,
} from "firebase/firestore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ReloadIcon } from "@radix-ui/react-icons";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { LucideAirplay } from "lucide-react";
import { useAuth } from "@/hooks/auth-context";
import {
  type Comment,
  type ProcessingStatus,
  type CommentToImageClientProps,
  ImageGenerationStatus,
  isCommentInProgress,
  isCommentNotStarted,
} from "@/types/comment-to-image";
// Add styles for progress animation
const styles = `
@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.animate-progress {
  animation: progress 30s ease-in-out;
}
`;

// Add style tag to head
if (typeof document !== "undefined") {
  const styleTag = document.createElement("style");
  styleTag.innerHTML = styles;
  document.head.appendChild(styleTag);
}

// Add this near the top of the file, after imports
// const CACHE_KEY = "comment-to-image-stats";
// const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Add this type definition at the top of the file
// interface CommentStatus {
//   id: string;
//   imageUrl?: string;
//   imageGenerationStatus?: ImageGenerationStatus;
//   imageGenerating?: boolean;
// }

// Add these type definitions at the top of the file
interface ImageGenerationResult {
  imageUrl?: string;
  url?: string;
  caption?: string;
  style_used?: string;
  dimensions?: {
    width: string;
    height: string;
  };
  processed_at?: string;
  mood?: string;
  color_palette?: string[];
  focal_points?: string[];
  visual_elements?: string[];
  lighting?: string;
  texture_style?: string;
  atmosphere?: string;
  emotional_impact?: string;
  visual_flow?: string;
  key_elements?: string[];
}

// Update CommentUpdate to be a Record type
type CommentUpdate = Record<string, any> & {
  imageGenerated: boolean;
  imageGenerationStatus: ImageGenerationStatus;
  imageUrl?: string;
  imageCaption?: string;
  imageStyle?: string;
  imageDimensions?: {
    width: string;
    height: string;
  };
  imageProcessedAt?: string;
  mood?: string;
  color_palette?: string[];
  focal_points?: string[];
  visual_elements?: string[];
  lighting?: string;
  texture_style?: string;
  atmosphere?: string;
  emotional_impact?: string;
  visual_flow?: string;
  key_elements?: string[];
  textAnalysis?: any;
  sentimentAnalysis?: any;
  storyAnalysis?: any;
  cultureFitAnalysis?: any;
  qualityCheck?: any;
  artDirection?: any;
};

export default function CommentToImageClient({
  initialComments,
  initialStatus,
}: CommentToImageClientProps) {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedComment, setSelectedComment] = useState<Comment | null>(null);
  const [status, setStatus] = useState<ProcessingStatus>(initialStatus);
  const BATCH_PROCESS_SIZE = 25;
  const [hideCompleted, setHideCompleted] = useState(true);
  const [continuousProcessing, setContinuousProcessing] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Add terminal logs state
  const [terminalLogs, setTerminalLogs] = useState<string[]>([]);
  const terminalRef = useRef<HTMLDivElement>(null);
  const [showTerminal, setShowTerminal] = useState(true);

  // Function to add a log to the terminal
  const addLog = (
    message: string,
    type: "info" | "error" | "success" = "info"
  ) => {
    const timestamp = new Date().toLocaleTimeString();
    const formattedMessage = `[${timestamp}] ${
      type === "error" ? "❌ " : type === "success" ? "✅ " : "🔄 "
    }${message}`;

    // Limit terminal logs to 100 lines
    setTerminalLogs((prev) => {
      const newLogs = [...prev, formattedMessage];
      return newLogs.slice(-100); // Keep only the last 100 logs
    });

    // Scroll to bottom of terminal
    if (terminalRef.current) {
      setTimeout(() => {
        if (terminalRef.current) {
          terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
        }
      }, 100);
    }
  };

  // Clear terminal logs
  const clearTerminal = () => {
    setTerminalLogs([]);
  };

  // Effect to monitor processing state for continuous processing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    // If continuous processing is enabled and we're not currently processing
    if (continuousProcessing && !isProcessing) {
      // Start processing with a short delay
      timeoutId = setTimeout(() => {
        processNextBatch();
      }, 2000);
    }

    // Cleanup timeout on unmount or when dependencies change
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isProcessing, continuousProcessing]);

  // Add effect to load initial status
  useEffect(() => {
    updateStatus();
  }, []); // Run once on mount

  // Update the processNextBatch function
  const processNextBatch = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      addLog("Finding unprocessed comments directly from database...", "info");

      const commentsRef = collection(db, "instagram_comments");
      const batchSize = 25; // Process 25 comments at a time
      let commentsToProcess: Comment[] = [];
      let lastDoc: QueryDocumentSnapshot<DocumentData> | null = null;
      let hasMore = true;
      let totalChecked = 0;

      // First try: Query for comments where imageGenerated is false
      while (hasMore && commentsToProcess.length < batchSize) {
        let baseQuery = query(
          commentsRef,
          where("imageGenerated", "==", false),
          orderBy("created_at", "desc"),
          limit(50)
        );

        if (lastDoc) {
          baseQuery = query(baseQuery, startAfter(lastDoc));
        }

        const snapshot = await getDocs(baseQuery);
        console.log("First query results:", {
          empty: snapshot.empty,
          size: snapshot.size,
          totalChecked,
        });

        if (snapshot.empty) {
          hasMore = false;
          break;
        }

        lastDoc = snapshot.docs[snapshot.docs.length - 1];
        totalChecked += snapshot.docs.length;

        const validComments = snapshot.docs
          .map(
            (doc) =>
              ({
                id: doc.id,
                ...doc.data(),
              } as Comment)
          )
          .filter((comment) => !comment.imageUrl); // Only include comments without images

        commentsToProcess = [...commentsToProcess, ...validComments].slice(
          0,
          batchSize
        );

        if (snapshot.docs.length < 50) {
          hasMore = false;
        }
      }

      // If no comments found with imageGenerated=false, try second approach
      if (commentsToProcess.length === 0) {
        hasMore = true;
        lastDoc = null;
        totalChecked = 0;

        while (hasMore && commentsToProcess.length < batchSize) {
          let baseQuery = query(
            commentsRef,
            orderBy("created_at", "desc"),
            limit(100)
          );

          if (lastDoc) {
            baseQuery = query(baseQuery, startAfter(lastDoc));
          }

          const snapshot = await getDocs(baseQuery);
          console.log("Second query results:", {
            empty: snapshot.empty,
            size: snapshot.size,
            totalChecked,
          });

          if (snapshot.empty) {
            hasMore = false;
            break;
          }

          lastDoc = snapshot.docs[snapshot.docs.length - 1];
          totalChecked += snapshot.docs.length;

          const allComments = snapshot.docs.map(
            (doc) =>
              ({
                id: doc.id,
                ...doc.data(),
              } as Comment)
          );

          // Filter for comments that have neither imageUrl nor imageGenerated field
          const validComments = allComments.filter((comment) => {
            const hasNoImageUrl = !comment.imageUrl;
            const hasNoImageGenerated =
              comment.imageGenerated === undefined ||
              comment.imageGenerated === null;
            return hasNoImageUrl && hasNoImageGenerated;
          });

          commentsToProcess = [...commentsToProcess, ...validComments].slice(
            0,
            batchSize
          );

          if (snapshot.docs.length < 100) {
            hasMore = false;
          }
        }
      }

      // If still no comments found
      if (commentsToProcess.length === 0) {
        console.log("No comments to process - final check:", {
          totalChecked,
          hasComments: commentsToProcess.length > 0,
          continuousProcessing,
        });

        addLog("No unprocessed comments found in database.", "info");
        toast({
          title: "Processing Complete",
          description: "No more comments to process.",
        });
        setContinuousProcessing(false);
        setIsProcessing(false);
        return;
      }

      addLog(`Found ${commentsToProcess.length} comments to process.`, "info");
      await processCommentBatch(commentsToProcess);
    } catch (error) {
      console.error("Error processing next batch:", error);
      addLog(`Error processing batch: ${error}`, "error");
      toast({
        title: "Error",
        description: "Failed to process comments batch",
      });

      setContinuousProcessing(false);
    } finally {
      setIsProcessing(false);
    }
  };

  // Update the updateStatus function to use more comprehensive queries
  const updateStatus = async () => {
    try {
      const commentsRef = collection(db, "instagram_comments");

      // Query for processed comments (has imageUrl and completed status)
      const processedQuery = query(
        commentsRef,
        where("imageUrl", "!=", ""),
        where("imageGenerated", "==", true)
      );
      const processedSnapshot = await getDocs(processedQuery);
      const processed = processedSnapshot.size;

      // Query for failed comments
      const failedQuery = query(
        commentsRef,
        where("imageGenerationStatus", "==", ImageGenerationStatus.FAILED)
      );
      const failedSnapshot = await getDocs(failedQuery);
      const failed = failedSnapshot.size;

      // Query for in-progress comments
      const inProgressQuery = query(
        commentsRef,
        where("imageGenerationStatus", "==", ImageGenerationStatus.IN_PROGRESS)
      );
      const inProgressSnapshot = await getDocs(inProgressQuery);
      const inProgress = inProgressSnapshot.size;

      // Query for total comments
      const totalSnapshot = await getDocs(commentsRef);
      const total = totalSnapshot.size;

      // Calculate unprocessed (not started) comments
      const notInitialized = total - (processed + failed + inProgress);

      const newStatus = {
        total,
        processed,
        failed,
        inProgress,
        notInitialized,
        fetchedCount: comments.length,
      };

      // Cache the results
      // localStorage.setItem(
      //   CACHE_KEY,
      //   JSON.stringify({
      //     data: newStatus,
      //     timestamp: Date.now(),
      //   })
      // );

      setStatus(newStatus);
      console.log("Status Update:", newStatus);
    } catch (error) {
      console.error("Error getting all comments for status:", error);
      toast({
        title: "Error",
        description: "Failed to update status counts",
        variant: "destructive",
      });
    }
  };

  // Helper function to format Firestore timestamp
  const formatFirestoreTimestamp = (timestamp: {
    seconds: number;
    nanoseconds: number;
  }) => {
    return new Date(timestamp.seconds * 1000).toLocaleString();
  };

  // Add this effect to handle real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isProcessing) {
        updateStatus();
      }
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isProcessing]);

  // Helper function to process a batch of comments
  const processCommentBatch = async (commentsToProcess: Comment[]) => {
    // Update local state to mark comments as processing
    setComments((prevComments) => {
      const updatedComments = [...prevComments];
      commentsToProcess.forEach((comment) => {
        const index = updatedComments.findIndex((c) => c.id === comment.id);
        if (index !== -1) {
          updatedComments[index] = {
            ...updatedComments[index],
            imageGenerationStatus: ImageGenerationStatus.IN_PROGRESS,
          };
        } else {
          updatedComments.push({
            ...comment,
            imageGenerationStatus: ImageGenerationStatus.IN_PROGRESS,
          });
        }
      });
      return updatedComments;
    });

    // Process comments in parallel
    const results = await Promise.allSettled(
      commentsToProcess.map((comment) => processComment(comment))
    );

    // Log results
    const fulfilled = results.filter((r) => r.status === "fulfilled").length;
    const rejected = results.filter((r) => r.status === "rejected").length;

    addLog(
      `Batch processing completed: ${fulfilled} succeeded, ${rejected} failed`,
      fulfilled > 0 ? "success" : "error"
    );

    // Show toast with results
    toast({
      title: "Batch processing completed",
      description: `${fulfilled} succeeded, ${rejected} failed`,
    });

    // Update status after processing
    await updateStatus();
  };

  // Process a single comment through the image generation pipeline
  const processComment = async (comment: Comment) => {
    try {
      addLog(`Processing comment: ${comment.id}`, "info");
      console.log(`Processing comment: ${comment.id}`);

      // Update local state to mark as processing
      const updateInProgress = {
        imageGenerationStatus: ImageGenerationStatus.IN_PROGRESS,
        imageGenerated: false,
      };

      await updateDoc(
        doc(db, "instagram_comments", comment.id),
        updateInProgress
      );

      setComments((prev) =>
        prev.map((c) =>
          c.id === comment.id
            ? {
                ...c,
                ...updateInProgress,
              }
            : c
        )
      );

      // Update status counts
      await updateStatus();

      // Step 1: Text Analysis
      addLog(`Step 1: Analyzing text for comment: ${comment.id}`, "info");
      const textAnalysisResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/textAnalyzer",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ content: comment.postBody }),
        }
      );

      if (!textAnalysisResponse.ok) {
        throw new Error(
          `Text analysis failed: ${textAnalysisResponse.statusText}`
        );
      }

      const textAnalysis = await textAnalysisResponse.json();
      addLog(`Text analysis complete`, "success");

      // Step 2: Sentiment Analysis
      addLog(`Step 2: Analyzing sentiment for comment: ${comment.id}`, "info");
      const sentimentAnalysisResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/sentimentAnalyzer",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ content: comment.postBody }),
        }
      );

      if (!sentimentAnalysisResponse.ok) {
        throw new Error(
          `Sentiment analysis failed: ${sentimentAnalysisResponse.statusText}`
        );
      }

      const sentimentAnalysis = await sentimentAnalysisResponse.json();
      addLog(`Sentiment analysis complete`, "success");

      // Step 3: Story Analysis
      addLog(
        `Step 3: Analyzing story elements for comment: ${comment.id}`,
        "info"
      );
      const storyAnalysisResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/storyGenerator",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: comment.postBody,
            textAnalysis,
            sentimentAnalysis,
          }),
        }
      );

      if (!storyAnalysisResponse.ok) {
        throw new Error(
          `Story analysis failed: ${storyAnalysisResponse.statusText}`
        );
      }

      const storyAnalysis = await storyAnalysisResponse.json();
      addLog(`Story analysis complete`, "success");

      // Step 4: Culture Fit Analysis
      addLog(
        `Step 4: Analyzing cultural fit for comment: ${comment.id}`,
        "info"
      );
      const cultureFitAnalysisResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/cultureFit",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: comment.postBody,
            textAnalysis,
            sentimentAnalysis,
            storyAnalysis,
          }),
        }
      );

      if (!cultureFitAnalysisResponse.ok) {
        throw new Error(
          `Culture fit analysis failed: ${cultureFitAnalysisResponse.statusText}`
        );
      }

      const cultureFitAnalysis = await cultureFitAnalysisResponse.json();
      addLog(`Culture fit analysis complete`, "success");

      // Step 5: Quality Check
      addLog(
        `Step 5: Performing quality check for comment: ${comment.id}`,
        "info"
      );
      const qualityCheckResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/qualityCheck",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: comment.postBody,
            textAnalysis,
            sentimentAnalysis,
            storyAnalysis,
            cultureFitAnalysis,
          }),
        }
      );

      if (!qualityCheckResponse.ok) {
        throw new Error(
          `Quality check failed: ${qualityCheckResponse.statusText}`
        );
      }

      const qualityCheck = await qualityCheckResponse.json();
      addLog(`Quality check complete`, "success");

      // Step 6: Art Direction
      addLog(
        `Step 6: Generating art direction for comment: ${comment.id}`,
        "info"
      );
      const artDirectionResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/artDirector",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: comment.postBody,
            textAnalysis,
            sentimentAnalysis,
            storyAnalysis,
            cultureFitAnalysis,
            qualityCheck,
          }),
        }
      );

      if (!artDirectionResponse.ok) {
        throw new Error(
          `Art direction failed: ${artDirectionResponse.statusText}`
        );
      }

      const artDirection = await artDirectionResponse.json();
      addLog(`Art direction complete`, "success");

      // Step 7: Generate Image
      addLog(`Step 7: Generating image for comment: ${comment.id}`, "info");
      const imageGeneratorResponse = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/imageGenerator",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: comment.postBody,
            textAnalysis,
            sentimentAnalysis,
            storyAnalysis,
            cultureFitAnalysis,
            qualityCheck,
            artDirection,
          }),
        }
      );

      const imageResult =
        (await imageGeneratorResponse.json()) as ImageGenerationResult;

      // Final step: Update the comment with the image URL and results
      addLog(`Step 8: Updating comment with generated image...`, "info");

      // Create the update object with proper typing
      const updateData: CommentUpdate = {
        imageGenerated: true,
        imageGenerationStatus: ImageGenerationStatus.COMPLETED,
        textAnalysis: textAnalysis || null,
        sentimentAnalysis: sentimentAnalysis || null,
        storyAnalysis: storyAnalysis || null,
        cultureFitAnalysis: cultureFitAnalysis || null,
        qualityCheck: qualityCheck || null,
        artDirection: artDirection || null,
      };

      // Handle different response formats with proper typing
      if (imageResult.imageUrl || imageResult.url) {
        updateData.imageUrl = imageResult.imageUrl || imageResult.url;
        updateData.imageCaption = imageResult.caption;
        updateData.imageStyle = imageResult.style_used;
        updateData.imageDimensions = imageResult.dimensions;
        updateData.imageProcessedAt = imageResult.processed_at;
        updateData.mood = imageResult.mood;
        updateData.color_palette = imageResult.color_palette;
        updateData.focal_points = imageResult.focal_points;
        updateData.visual_elements = imageResult.visual_elements;
        updateData.lighting = imageResult.lighting;
        updateData.texture_style = imageResult.texture_style;
        updateData.atmosphere = imageResult.atmosphere;
        updateData.emotional_impact = imageResult.emotional_impact;
        updateData.visual_flow = imageResult.visual_flow;
        updateData.key_elements = imageResult.key_elements;
      }

      // Log the update data for debugging
      addLog(
        `Updating comment with data: ${JSON.stringify(updateData)}`,
        "info"
      );

      await updateDoc(doc(db, "instagram_comments", comment.id), updateData);

      addLog(`Image generation complete!`, "success");

      // Update local state
      setComments((prev) =>
        prev.map((c) =>
          c.id === comment.id
            ? {
                ...c,
                ...updateData,
              }
            : c
        )
      );

      // Update status counts
      await updateStatus();

      return comment.id;
    } catch (error) {
      console.error("Error processing comment:", error);
      addLog(
        `Error: ${error instanceof Error ? error.message : String(error)}`,
        "error"
      );

      // Update comment status to failed
      const failedUpdate = {
        imageGenerationStatus: ImageGenerationStatus.FAILED,
        imageGenerated: false,
      };

      try {
        await updateDoc(
          doc(db, "instagram_comments", comment.id),
          failedUpdate
        );

        // Update local state
        setComments((prev) =>
          prev.map((c) =>
            c.id === comment.id
              ? {
                  ...c,
                  ...failedUpdate,
                }
              : c
          )
        );

        // Update status counts
        await updateStatus();
      } catch (updateError) {
        console.error("Error updating comment status:", updateError);
      }

      throw error;
    }
  };

  // Reset image generation status for a comment
  const resetImageGeneration = async (comment: Comment) => {
    try {
      addLog(`Resetting image generation for comment: ${comment.id}`, "info");
      await updateDoc(doc(db, "instagram_comments", comment.id), {
        imageGenerationStatus: ImageGenerationStatus.NOT_STARTED,
        imageUrl: "",
        imageCaption: "",
        imageStyle: "",
        imageDimensions: null,
        imageProcessedAt: null,
        textAnalysis: null,
        sentimentAnalysis: null,
        storyAnalysis: null,
        cultureFitAnalysis: null,
        qualityCheck: null,
        artDirection: null,
      });

      // Update local state
      setComments((prev) =>
        prev.map((c) =>
          c.id === comment.id
            ? {
                ...c,
                imageGenerationStatus: ImageGenerationStatus.NOT_STARTED,
                imageUrl: "",
                imageCaption: "",
                imageStyle: "",
                imageDimensions: undefined,
                imageProcessedAt: undefined,
                textAnalysis: undefined,
                sentimentAnalysis: undefined,
                storyAnalysis: undefined,
                cultureFitAnalysis: undefined,
                qualityCheck: undefined,
                artDirection: undefined,
              }
            : c
        )
      );

      // Update status
      await updateStatus();

      toast({
        title: "Reset Complete",
        description: "Image generation has been reset for this comment",
      });
    } catch (error) {
      console.error("Error resetting image generation:", error);
      toast({
        title: "Error",
        description: "Failed to reset image generation",
      });
    }
  };

  // Toggle continuous processing
  const toggleContinuousProcessing = () => {
    const newValue = !continuousProcessing;
    setContinuousProcessing(newValue);

    if (newValue) {
      // If enabling continuous processing
      toast({
        title: "Continuous Processing Enabled",
        description:
          "Comments will be processed continuously until all are done",
      });

      // If not already processing, start the first batch
      if (!isProcessing) {
        addLog("Starting continuous processing mode", "info");
        processNextBatch();
      }
    } else {
      // If disabling continuous processing
      toast({
        title: "Continuous Processing Disabled",
        description:
          "Current batch will complete, but no new batches will start.",
      });
      addLog("Continuous processing mode disabled", "info");
    }
  };

  // Fix comments with inconsistent state
  const fixInconsistentComments = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      addLog("Checking for comments with inconsistent state...", "info");

      // Get all comments
      const commentsRef = collection(db, "instagram_comments");
      const snapshot = await getDocs(commentsRef);

      const allComments = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Comment[];

      // Find comments with inconsistent state
      const stuckComments = allComments.filter(
        (c) =>
          c.imageGenerationStatus === ImageGenerationStatus.IN_PROGRESS &&
          !c.imageUrl
      );

      if (stuckComments.length === 0) {
        addLog("No comments with inconsistent state found", "info");
        toast({
          title: "No Action Needed",
          description: "No comments with inconsistent state found",
        });
        setIsProcessing(false);
        return;
      }

      addLog(
        `Found ${stuckComments.length} comments with inconsistent state`,
        "info"
      );

      // Use batched writes for better performance
      const batchSize = 500; // Firestore batch limit
      let successCount = 0;

      for (let i = 0; i < stuckComments.length; i += batchSize) {
        const batch = writeBatch(db);
        const currentBatch = stuckComments.slice(
          i,
          Math.min(i + batchSize, stuckComments.length)
        );

        currentBatch.forEach((comment) => {
          const docRef = doc(db, "instagram_comments", comment.id);
          batch.update(docRef, {
            imageGenerationStatus: ImageGenerationStatus.NOT_STARTED,
          });
        });

        await batch.commit();
        successCount += currentBatch.length;
        addLog(
          `Fixed batch ${i / batchSize + 1}: ${currentBatch.length} comments`,
          "success"
        );
      }

      // Update local state
      setComments((prev) =>
        prev.map((comment) => {
          if (
            comment.imageGenerationStatus ===
              ImageGenerationStatus.IN_PROGRESS &&
            !comment.imageUrl
          ) {
            return {
              ...comment,
              imageGenerationStatus: ImageGenerationStatus.NOT_STARTED,
            };
          }
          return comment;
        })
      );

      // Update status
      await updateStatus();

      addLog(`Fix complete: ${successCount} comments`, "success");
      toast({
        title: "Fix Complete",
        description: `${successCount} comments have been fixed`,
      });
    } catch (error) {
      console.error("Error fixing comments:", error);
      addLog(`Error fixing comments: ${error}`, "error");
      toast({
        title: "Error",
        description: "Failed to fix comments",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get processing comments
  const processingComments = useMemo(() => {
    return comments.filter(isCommentInProgress);
  }, [comments]);

  // Get latest generated images
  const getLatestGeneratedImages = async () => {
    try {
      const commentsRef = collection(db, "instagram_comments");
      const q = query(
        commentsRef,
        where("imageGenerationStatus", "==", ImageGenerationStatus.COMPLETED),
        where("imageUrl", "!=", ""),
        orderBy("imageProcessedAt", "desc"),
        limit(5)
      );

      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const generatedImages = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Comment[];
        return generatedImages;
      }
    } catch (error) {
      console.error("Error getting latest generated images:", error);
    }
  };

  // Fetch latest generated images on component mount and when status changes
  useEffect(() => {
    getLatestGeneratedImages();
  }, [status]);

  // Filter comments based on hideCompleted
  const filteredComments = useMemo(() => {
    if (hideCompleted) {
      return comments.filter(isCommentNotStarted);
    }
    return comments;
  }, [comments, hideCompleted]);

  // Add a debug effect to log filtered comments
  useEffect(() => {
    console.log(
      `Total comments: ${comments.length}, Filtered comments: ${filteredComments.length}, Processing comments: ${processingComments.length}, Hide completed: ${hideCompleted}`
    );
  }, [
    comments.length,
    filteredComments.length,
    processingComments.length,
    hideCompleted,
  ]);

  // Image Details Dialog component
  const ImageDetailsDialog = () => {
    if (!selectedComment) return null;

    return (
      <Dialog
        open={!!selectedComment}
        onOpenChange={(open) => !open && setSelectedComment(null)}
      >
        <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              Image Analysis Report
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Image Column */}
            <div className="lg:col-span-1">
              {selectedComment.imageUrl ? (
                <div className="relative aspect-square rounded-lg overflow-hidden border shadow-md">
                  <Image
                    src={selectedComment.imageUrl}
                    alt="Generated image"
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="aspect-square rounded-lg bg-muted flex items-center justify-center">
                  <p className="text-muted-foreground">No image available</p>
                </div>
              )}

              {/* Author & Post Info */}
              <div className="mt-4 p-4 bg-muted/30 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    {selectedComment.authorName?.charAt(0) ||
                      selectedComment.username?.charAt(0) ||
                      "U"}
                  </div>
                  <div>
                    <p className="font-medium text-sm">
                      {selectedComment.authorName || "Unknown Author"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      @{selectedComment.username || "unknown"}
                    </p>
                  </div>
                </div>
                <p className="text-sm mb-3 p-2 bg-background rounded border">
                  {selectedComment.postBody}
                </p>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <span className="text-red-500">❤️</span>
                    <span>
                      {selectedComment.comment_like_count ||
                        selectedComment.metrics?.likes ||
                        0}{" "}
                      likes
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-blue-500">📅</span>
                    <span>
                      {
                        formatFirestoreTimestamp(
                          selectedComment.created_at
                        ).split(",")[0]
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Analysis Column 1 */}
            <div className="lg:col-span-1">
              <Accordion
                type="multiple"
                className="w-full"
                defaultValue={["image-info", "sentiment"]}
              >
                <AccordionItem value="image-info">
                  <AccordionTrigger>Image Information</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      <div>
                        <span className="font-semibold">Style:</span>{" "}
                        {selectedComment.imageStyle ||
                          (selectedComment.artDirection as any)?.style ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Mood:</span>{" "}
                        {selectedComment.mood ||
                          (selectedComment.artDirection as any)?.mood ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Dimensions:</span>{" "}
                        {selectedComment.imageDimensions
                          ? `${selectedComment.imageDimensions.width}x${selectedComment.imageDimensions.height}`
                          : "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Generated at:</span>{" "}
                        {selectedComment.imageProcessedAt
                          ? new Date(
                              selectedComment.imageProcessedAt
                            ).toLocaleString()
                          : "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Atmosphere:</span>{" "}
                        {selectedComment.atmosphere ||
                          (selectedComment.artDirection as any)?.atmosphere ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Lighting:</span>{" "}
                        {selectedComment.lighting ||
                          (selectedComment.artDirection as any)?.lighting ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Texture Style:</span>{" "}
                        {selectedComment.texture_style ||
                          (selectedComment.artDirection as any)
                            ?.texture_style ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Visual Flow:</span>{" "}
                        {selectedComment.visual_flow ||
                          (selectedComment.artDirection as any)?.visual_flow ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Emotional Impact:</span>{" "}
                        {selectedComment.emotional_impact ||
                          (selectedComment.artDirection as any)
                            ?.emotional_impact ||
                          "Not specified"}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="color-palette">
                  <AccordionTrigger>Color Palette</AccordionTrigger>
                  <AccordionContent>
                    <div className="flex flex-wrap gap-2">
                      {(selectedComment.color_palette ||
                        (selectedComment.artDirection as any)?.color_palette) &&
                      (
                        selectedComment.color_palette ||
                        (selectedComment.artDirection as any)?.color_palette
                      ).length > 0 ? (
                        (
                          selectedComment.color_palette ||
                          (selectedComment.artDirection as any)?.color_palette
                        ).map((color: string, index: number) => {
                          // Extract hex code if it exists in the format "color (#hex)"
                          const hexMatch = color.match(/#[0-9A-Fa-f]{6}/);
                          const hexColor = hexMatch
                            ? hexMatch[0]
                            : color.startsWith("#")
                            ? color
                            : "#CCCCCC";

                          return (
                            <div
                              key={index}
                              className="flex flex-col items-center"
                            >
                              <div
                                className="w-10 h-10 rounded-full border border-gray-200"
                                style={{ backgroundColor: hexColor }}
                              />
                              <span className="text-xs mt-1">{color}</span>
                            </div>
                          );
                        })
                      ) : (
                        <p className="text-sm text-gray-500">
                          No color palette specified
                        </p>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="elements">
                  <AccordionTrigger>Visual Elements</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-1">Focal Points</h4>
                        {(selectedComment.focal_points ||
                          (selectedComment.artDirection as any)
                            ?.focal_points) &&
                        (
                          selectedComment.focal_points ||
                          (selectedComment.artDirection as any)?.focal_points
                        ).length > 0 ? (
                          <ul className="list-disc pl-5 text-sm">
                            {(
                              selectedComment.focal_points ||
                              (selectedComment.artDirection as any)
                                ?.focal_points
                            ).map((point: string, index: number) => (
                              <li key={index}>{point}</li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No focal points specified
                          </p>
                        )}
                      </div>

                      <div>
                        <h4 className="font-semibold mb-1">Visual Elements</h4>
                        {(selectedComment.visual_elements ||
                          (selectedComment.artDirection as any)
                            ?.visual_elements) &&
                        (
                          selectedComment.visual_elements ||
                          (selectedComment.artDirection as any)?.visual_elements
                        ).length > 0 ? (
                          <ul className="list-disc pl-5 text-sm">
                            {(
                              selectedComment.visual_elements ||
                              (selectedComment.artDirection as any)
                                ?.visual_elements
                            ).map((element: string, index: number) => (
                              <li key={index}>{element}</li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No visual elements specified
                          </p>
                        )}
                      </div>

                      <div>
                        <h4 className="font-semibold mb-1">Key Elements</h4>
                        {(selectedComment.key_elements ||
                          (selectedComment.artDirection as any)
                            ?.key_elements) &&
                        (
                          selectedComment.key_elements ||
                          (selectedComment.artDirection as any)?.key_elements
                        ).length > 0 ? (
                          <ul className="list-disc pl-5 text-sm">
                            {(
                              selectedComment.key_elements ||
                              (selectedComment.artDirection as any)
                                ?.key_elements
                            ).map((element: string, index: number) => (
                              <li key={index}>{element}</li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No key elements specified
                          </p>
                        )}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="sentiment">
                  <AccordionTrigger className="text-green-700">
                    <div className="flex items-center gap-2">
                      <span>😊</span>
                      Sentiment Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(selectedComment.sentimentAnalysis as any) && (
                        <>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">Sentiment:</span>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${
                                (selectedComment.sentimentAnalysis as any)
                                  .sentiment === "positive"
                                  ? "bg-green-100 text-green-800"
                                  : (selectedComment.sentimentAnalysis as any)
                                      .sentiment === "negative"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {
                                (selectedComment.sentimentAnalysis as any)
                                  .sentiment
                              }
                            </span>
                            <span className="text-sm text-muted-foreground">
                              (
                              {
                                (selectedComment.sentimentAnalysis as any)
                                  .sentiment_score
                              }
                              /10)
                            </span>
                          </div>

                          <div>
                            <span className="font-semibold">Intensity:</span>{" "}
                            <span
                              className={`px-2 py-1 rounded text-xs ${
                                (selectedComment.sentimentAnalysis as any)
                                  .intensity === "high"
                                  ? "bg-red-50 text-red-700"
                                  : (selectedComment.sentimentAnalysis as any)
                                      .intensity === "low"
                                  ? "bg-blue-50 text-blue-700"
                                  : "bg-orange-50 text-orange-700"
                              }`}
                            >
                              {
                                (selectedComment.sentimentAnalysis as any)
                                  .intensity
                              }
                            </span>
                          </div>

                          {(selectedComment.sentimentAnalysis as any)
                            .emotions && (
                            <div>
                              <h4 className="font-semibold mb-1">Emotions</h4>
                              <div className="flex flex-wrap gap-1">
                                {(
                                  selectedComment.sentimentAnalysis as any
                                ).emotions.map(
                                  (emotion: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                                    >
                                      {emotion}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                          {(selectedComment.sentimentAnalysis as any)
                            .summary && (
                            <div>
                              <h4 className="font-semibold mb-1">Summary</h4>
                              <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                                {
                                  (selectedComment.sentimentAnalysis as any)
                                    .summary
                                }
                              </p>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="text-analysis">
                  <AccordionTrigger className="text-blue-700">
                    <div className="flex items-center gap-2">
                      <span>📝</span>
                      Text Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(selectedComment.textAnalysis as any) && (
                        <>
                          {(selectedComment.textAnalysis as any).summary && (
                            <div>
                              <h4 className="font-semibold mb-1">Summary</h4>
                              <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                                {(selectedComment.textAnalysis as any).summary}
                              </p>
                            </div>
                          )}

                          {(selectedComment.textAnalysis as any).topics && (
                            <div>
                              <h4 className="font-semibold mb-1">Topics</h4>
                              <div className="flex flex-wrap gap-1">
                                {(
                                  selectedComment.textAnalysis as any
                                ).topics.map((topic: string, index: number) => (
                                  <span
                                    key={index}
                                    className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs"
                                  >
                                    {topic}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {(selectedComment.textAnalysis as any).keywords && (
                            <div>
                              <h4 className="font-semibold mb-1">Keywords</h4>
                              <div className="flex flex-wrap gap-1">
                                {(
                                  selectedComment.textAnalysis as any
                                ).keywords.map(
                                  (keyword: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs"
                                    >
                                      {keyword}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            {/* Analysis Column 2 */}
            <div className="lg:col-span-1">
              <Accordion
                type="multiple"
                className="w-full"
                defaultValue={["quality", "culture-fit"]}
              >
                <AccordionItem value="quality">
                  <AccordionTrigger className="text-orange-700">
                    <div className="flex items-center gap-2">
                      <span>⭐</span>
                      Quality Assessment
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(selectedComment.qualityCheck as any) && (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-green-600">
                                {
                                  (selectedComment.qualityCheck as any)
                                    .overall_score
                                }
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Overall
                              </div>
                            </div>
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-blue-600">
                                {
                                  (selectedComment.qualityCheck as any)
                                    .engagement_score
                                }
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Engagement
                              </div>
                            </div>
                          </div>

                          {(selectedComment.qualityCheck as any).feedback && (
                            <div>
                              <h4 className="font-semibold mb-1">Feedback</h4>
                              <ul className="space-y-1 text-sm">
                                {(
                                  selectedComment.qualityCheck as any
                                ).feedback.map(
                                  (item: string, index: number) => (
                                    <li
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-green-500 mt-1">
                                        •
                                      </span>
                                      <span>{item}</span>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}

                          {(selectedComment.qualityCheck as any)
                            .improvements && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Improvements
                              </h4>
                              <ul className="space-y-1 text-sm">
                                {(
                                  selectedComment.qualityCheck as any
                                ).improvements.map(
                                  (item: string, index: number) => (
                                    <li
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-orange-500 mt-1">
                                        →
                                      </span>
                                      <span>{item}</span>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="culture-fit">
                  <AccordionTrigger className="text-purple-700">
                    <div className="flex items-center gap-2">
                      <span>🎯</span>
                      Culture Fit Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(selectedComment.cultureFitAnalysis as any) && (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-purple-600">
                                {
                                  (selectedComment.cultureFitAnalysis as any)
                                    .alignment_score
                                }
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Alignment
                              </div>
                            </div>
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-green-600">
                                {
                                  (selectedComment.cultureFitAnalysis as any)
                                    .market_fit
                                }
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Market Fit
                              </div>
                            </div>
                          </div>

                          {(selectedComment.cultureFitAnalysis as any)
                            .brand_values && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Brand Values
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {(
                                  selectedComment.cultureFitAnalysis as any
                                ).brand_values.map(
                                  (value: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs"
                                    >
                                      {value}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                          {(selectedComment.cultureFitAnalysis as any)
                            .recommendations && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Recommendations
                              </h4>
                              <ul className="space-y-1 text-sm">
                                {(
                                  selectedComment.cultureFitAnalysis as any
                                ).recommendations.map(
                                  (item: string, index: number) => (
                                    <li
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-green-500 mt-1">
                                        ✓
                                      </span>
                                      <span>{item}</span>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="story">
                  <AccordionTrigger className="text-indigo-700">
                    <div className="flex items-center gap-2">
                      <span>📚</span>
                      Story Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(selectedComment.storyAnalysis as any) && (
                        <>
                          {(selectedComment.storyAnalysis as any).title && (
                            <div>
                              <h4 className="font-semibold mb-1">Title</h4>
                              <p className="font-medium text-indigo-700">
                                {(selectedComment.storyAnalysis as any).title}
                              </p>
                            </div>
                          )}

                          <div className="grid grid-cols-2 gap-2">
                            {(selectedComment.storyAnalysis as any).style && (
                              <div>
                                <span className="font-semibold">Style:</span>{" "}
                                <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs">
                                  {(selectedComment.storyAnalysis as any).style}
                                </span>
                              </div>
                            )}
                            {(selectedComment.storyAnalysis as any).tone && (
                              <div>
                                <span className="font-semibold">Tone:</span>{" "}
                                <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs">
                                  {(selectedComment.storyAnalysis as any).tone}
                                </span>
                              </div>
                            )}
                          </div>

                          {(selectedComment.storyAnalysis as any).story && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Generated Story
                              </h4>
                              <div className="max-h-32 overflow-y-auto bg-muted/50 p-3 rounded text-sm">
                                {(selectedComment.storyAnalysis as any).story}
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="art-direction">
                  <AccordionTrigger className="text-gray-700">
                    <div className="flex items-center gap-2">
                      <span>🎨</span>
                      Art Direction (Technical)
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(selectedComment.artDirection as any)?.composition && (
                        <div>
                          <h4 className="font-semibold mb-1">Composition</h4>
                          <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                            {(selectedComment.artDirection as any).composition}
                          </p>
                        </div>
                      )}
                      <details className="border rounded p-2">
                        <summary className="cursor-pointer font-medium text-sm">
                          View Full Art Direction JSON
                        </summary>
                        <pre className="bg-muted p-2 rounded-md text-xs overflow-x-auto whitespace-pre-wrap mt-2">
                          {JSON.stringify(
                            selectedComment.artDirection,
                            null,
                            2
                          )}
                        </pre>
                      </details>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              onClick={() => resetImageGeneration(selectedComment)}
              disabled={isProcessing}
            >
              Reset Image Generation
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Function to clear failed status for all failed posts
  const clearFailedStatus = async () => {
    try {
      setIsProcessing(true);
      addLog("Clearing failed status for all failed posts...", "info");

      const commentsRef = collection(db, "instagram_comments");
      const failedQuery = query(
        commentsRef,
        where("imageGenerationStatus", "==", ImageGenerationStatus.FAILED)
      );

      const snapshot = await getDocs(failedQuery);

      if (snapshot.empty) {
        toast({
          title: "No Failed Posts",
          description: "No failed posts found to clear.",
        });
        return;
      }

      // Use batched writes for better performance
      const batchSize = 500; // Firestore batch limit
      let processed = 0;
      const total = snapshot.size;

      for (let i = 0; i < snapshot.docs.length; i += batchSize) {
        const batch = writeBatch(db);
        const currentBatch = snapshot.docs.slice(i, i + batchSize);

        currentBatch.forEach((doc) => {
          batch.update(doc.ref, {
            imageGenerationStatus: ImageGenerationStatus.NOT_STARTED,
          });
        });

        await batch.commit();
        processed += currentBatch.length;
        addLog(`Cleared ${processed}/${total} failed posts`, "info");
      }

      // Update local state
      setComments((prev) =>
        prev.map((comment) => {
          if (comment.imageGenerationStatus === ImageGenerationStatus.FAILED) {
            return {
              ...comment,
              imageGenerationStatus: ImageGenerationStatus.NOT_STARTED,
            };
          }
          return comment;
        })
      );

      // Update status
      await updateStatus();

      toast({
        title: "Success",
        description: `Cleared failed status for ${processed} posts`,
      });
      addLog(
        `Successfully cleared failed status for ${processed} posts`,
        "success"
      );
    } catch (error) {
      console.error("Error clearing failed status:", error);
      addLog(`Error clearing failed status: ${error}`, "error");
      toast({
        title: "Error",
        description: "Failed to clear failed status",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Process a batch of comments
  const processComments = async (count: number = BATCH_PROCESS_SIZE) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      clearTerminal();
      addLog(`Starting to process up to ${count} comments`, "info");

      // Query for unprocessed comments directly
      const commentsRef = collection(db, "instagram_comments");

      // First try to find comments with empty imageUrl
      const q = query(
        commentsRef,
        where("imageUrl", "==", ""),
        orderBy("created_at", "desc")
      );

      const snapshot = await getDocs(q);
      let allComments = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Comment[];

      // If no comments found with empty imageUrl, try finding comments with specific statuses
      if (allComments.length === 0) {
        const q2 = query(
          commentsRef,
          where("imageGenerationStatus", "in", [
            ImageGenerationStatus.NOT_STARTED,
            ImageGenerationStatus.FAILED,
            null,
          ]),
          orderBy("created_at", "desc")
        );

        const snapshot2 = await getDocs(q2);
        allComments = snapshot2.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Comment[];
      }

      // Filter for unprocessed comments
      const unprocessedComments = allComments.filter(isCommentNotStarted);

      if (unprocessedComments.length === 0) {
        addLog("No unprocessed comments found in database.", "info");
        toast({
          title: "No Comments to Process",
          description: "No unprocessed comments found in the database.",
        });
        setIsProcessing(false);
        return;
      }

      // Take only the requested count
      const commentsToProcess = unprocessedComments.slice(0, count);
      await processCommentBatch(commentsToProcess);
    } catch (error) {
      console.error("Error processing comments:", error);
      addLog(`Error processing batch: ${error}`, "error");
      toast({
        title: "Error",
        description: "Failed to process comments batch",
      });

      // If there was an error and we're in continuous mode, stop it
      if (continuousProcessing) {
        setContinuousProcessing(false);
        toast({
          title: "Continuous Processing Stopped",
          description: "Stopped due to an error in processing.",
        });
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Dashboard Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 bg-card rounded-lg p-6 shadow-sm border">
        <div>
          <h1 className="text-2xl font-bold">Image Generator</h1>
          <p className="text-muted-foreground">
            Transform comments into stunning AI-generated images
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={() => processComments()}
            disabled={isProcessing || comments.length === 0}
            className="gap-2"
          >
            {isProcessing ? (
              <ReloadIcon className="h-4 w-4 animate-spin" />
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M12 22C6.5 22 2 17.5 2 12S6.5 2 12 2s10 4.5 10 10-4.5 10-10 10z"></path>
                <path d="M12 18v-6"></path>
                <path d="M12 8V7"></path>
              </svg>
            )}
            Process 25 Stories
          </Button>
          <Button
            onClick={toggleContinuousProcessing}
            disabled={isProcessing && !continuousProcessing}
            variant={continuousProcessing ? "destructive" : "outline"}
            className="gap-2"
          >
            {continuousProcessing ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <rect width="4" height="16" x="6" y="4"></rect>
                  <rect width="4" height="16" x="14" y="4"></rect>
                </svg>
                Stop Processing
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <polygon points="5 3 19 12 5 21 5 3"></polygon>
                </svg>
                Start Continuous
              </>
            )}
          </Button>
          {user?.email === "<EMAIL>" && (
            <Button
              onClick={clearFailedStatus}
              disabled={isProcessing}
              variant="secondary"
              className="gap-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                <path d="M3 3v5h5"></path>
                <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path>
                <path d="M16 16h5v5"></path>
              </svg>
              Clear Failed
            </Button>
          )}
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-600/10"></div>
          <CardHeader className="pb-2 relative">
            <CardTitle className="text-sm font-medium text-blue-500 dark:text-blue-400 flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-message-square"
              >
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
              Total Comments
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">{status.total || 0}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {status.fetchedCount || 0} comments loaded
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-green-600/10"></div>
          <CardHeader className="pb-2 relative">
            <CardTitle className="text-sm font-medium text-green-500 dark:text-green-400 flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-check-circle"
              >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              Processed
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <div className="">
              {status.processed === 50 ? (
                <span className="text-xs text-muted-foreground">
                  Calculating...
                </span>
              ) : (
                <div className="text-2xl font-bold">
                  {status.processed || 0}
                </div>
              )}
            </div>
            <div className="w-full h-1.5 bg-green-200 dark:bg-green-950 rounded-full mt-2 overflow-hidden">
              <div
                className="h-full bg-green-500 rounded-full transition-all duration-500"
                style={{
                  width: `${
                    ((status.processed || 0) / (status.total || 1)) * 100
                  }%`,
                }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {(((status.processed || 0) / (status.total || 1)) * 100).toFixed(
                1
              )}
              % complete
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10"></div>
          <CardHeader className="pb-2 relative">
            <CardTitle className="text-sm font-medium text-yellow-500 dark:text-yellow-400 flex items-center gap-2">
              <LucideAirplay className="h-4 w-4" />
              In Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">
              {processingComments.length}
            </div>
            {processingComments.length > 0 && (
              <div className="mt-2">
                <div className="h-1.5 bg-yellow-200 dark:bg-yellow-950 rounded-full overflow-hidden">
                  <div className="h-full bg-yellow-500 rounded-full animate-progress"></div>
                </div>
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              Currently being processed
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-red-600/10"></div>
          <CardHeader className="pb-2 relative">
            <CardTitle className="text-sm font-medium text-red-500 dark:text-red-400 flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-alert-circle"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" x2="12" y1="8" y2="12"></line>
                <line x1="12" x2="12" y1="16" y2="16"></line>
              </svg>
              Failed
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">{status.failed || 0}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Failed to generate images
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/10"></div>
          <CardHeader className="pb-2 relative">
            <CardTitle className="text-sm font-medium text-purple-500 dark:text-purple-400 flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-hourglass"
              >
                <path d="M5 22h14"></path>
                <path d="M5 2h14"></path>
                <path d="M17 22v-4.172a2 2 0 0 0-.586-1.414L12 12l-4.414 4.414A2 2 0 0 0 7 17.828V22"></path>
                <path d="M7 2v4.172a2 2 0 0 0 .586 1.414L12 12l4.414-4.414A2 2 0 0 0 17 6.172V2"></path>
              </svg>
              Unprocessed
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">
              {status.notInitialized || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Waiting to be processed
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        {/* Comments List */}
        <Card className="">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Comments List</CardTitle>
              <div className="flex items-center gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hideCompleted"
                    checked={hideCompleted}
                    onCheckedChange={(checked) => {
                      setHideCompleted(checked === true);
                    }}
                  />
                  <label
                    htmlFor="hideCompleted"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    Hide Completed
                  </label>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fixInconsistentComments}
                  disabled={isProcessing}
                  className="gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-3 w-3"
                  >
                    <path d="M10 2h4"></path>
                    <path d="M12 14v-4"></path>
                    <path d="M4 13a8 8 0 0 1 8-7 8 8 0 0 1 8 7 8 8 0 0 1-8 7 8 8 0 0 1-8-7z"></path>
                  </svg>
                  Fix Stuck
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Comments Queue Section */}
            <div className="h-[600px] flex flex-col">
              <h3 className="text-lg font-medium mb-3 flex items-center sticky top-0 bg-background z-10 py-2 justify-between">
                <span className="mr-2">Comments Queue</span>
                {processingComments.length > 0 && (
                  <span className="text-xs bg-blue-800 text-blue-200 px-2 py-0.5 rounded-full flex items-center gap-1">
                    <svg
                      className="animate-spin h-3 w-3 text-blue-200 mr-1"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    7 Agents Working on {processingComments.length} Stories
                  </span>
                )}
              </h3>

              <div className="flex-1 overflow-y-auto pr-2 space-y-4">
                {processingComments.length > 0 ? (
                  processingComments.map((comment) => {
                    const sentimentData = comment.sentimentAnalysis as any;
                    const qualityData = comment.qualityCheck as any;
                    const artDirection = comment.artDirection as any;

                    return (
                      <div
                        key={comment.id}
                        className="group p-4 rounded-lg border border-yellow-500 dark:border-yellow-600 bg-gradient-to-br from-yellow-50/50 to-amber-50/50 dark:from-yellow-950/30 dark:to-amber-950/30 animate-pulse cursor-pointer hover:shadow-lg transition-all duration-300"
                        onClick={() => setSelectedComment(comment)}
                      >
                        <div className="flex flex-col md:flex-row gap-4">
                          <div className="flex-grow space-y-3">
                            {/* Header with ID and status */}
                            <div className="flex items-center gap-2 flex-wrap">
                              <span className="text-sm font-medium">
                                Comment ID: {comment.id.substring(0, 8)}...
                              </span>
                              <span className="text-xs bg-yellow-500 text-yellow-50 px-2 py-0.5 rounded-full flex items-center gap-1 shadow-sm">
                                <svg
                                  className="animate-spin h-3 w-3"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                  ></circle>
                                  <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                  ></path>
                                </svg>
                                Processing
                              </span>
                            </div>

                            {/* Author info */}
                            <div className="flex items-center gap-2">
                              <div className="h-6 w-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                {comment.authorName?.charAt(0) ||
                                  comment.username?.charAt(0) ||
                                  "U"}
                              </div>
                              <div>
                                <p className="text-sm font-medium">
                                  {comment.authorName || "Unknown Author"}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  @{comment.username || "unknown"}
                                </p>
                              </div>
                            </div>

                            {/* Post content */}
                            <p className="text-sm line-clamp-3 bg-white/70 dark:bg-gray-800/70 p-2 rounded border">
                              {comment.postBody}
                            </p>

                            {/* Analysis badges (if available) */}
                            {(sentimentData || qualityData || artDirection) && (
                              <div className="flex items-center gap-1 flex-wrap">
                                {sentimentData?.sentiment && (
                                  <span
                                    className={`text-xs px-2 py-1 rounded-full ${
                                      sentimentData.sentiment === "positive"
                                        ? "bg-green-100 text-green-800"
                                        : sentimentData.sentiment === "negative"
                                        ? "bg-red-100 text-red-800"
                                        : "bg-yellow-100 text-yellow-800"
                                    }`}
                                  >
                                    😊 {sentimentData.sentiment_score}/10
                                  </span>
                                )}
                                {qualityData?.overall_score && (
                                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                    ⭐ {qualityData.overall_score}/10
                                  </span>
                                )}
                                {artDirection?.style && (
                                  <span className="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">
                                    🎨 {artDirection.style}
                                  </span>
                                )}
                              </div>
                            )}

                            {/* Meta info */}
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span>
                                {formatFirestoreTimestamp(comment.created_at)}
                              </span>
                              <span>•</span>
                              <span className="flex items-center gap-1">
                                <span>❤️</span>
                                <span>{comment.metrics?.likes || 0}</span>
                              </span>
                            </div>
                          </div>

                          {/* Image preview or processing indicator */}
                          <div className="flex-shrink-0">
                            {comment.imageUrl ? (
                              <div className="relative w-20 h-20 rounded-md overflow-hidden border">
                                <Image
                                  src={comment.imageUrl}
                                  alt="Generated image"
                                  fill
                                  className="object-cover"
                                />
                                <div className="absolute top-1 right-1">
                                  <div className="bg-green-500 text-white text-xs px-1 py-0.5 rounded-full">
                                    ✓
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="w-20 h-20 rounded-md border-2 border-dashed border-yellow-400 flex items-center justify-center bg-yellow-50/50 dark:bg-yellow-950/50">
                                <svg
                                  className="animate-spin h-8 w-8 text-yellow-600"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                  ></circle>
                                  <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                  ></path>
                                </svg>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Click hint */}
                        <div className="mt-3 pt-3 border-t border-yellow-200 dark:border-yellow-800">
                          <p className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            Click to view analysis details
                          </p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-8 w-8 text-muted-foreground"
                      >
                        <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                        <path d="M9 8h7"></path>
                        <path d="M8 12h6"></path>
                        <path d="M11 16h4"></path>
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium">
                      No Active Processing
                    </h3>
                    <p className="text-muted-foreground mt-1">
                      Start processing comments to see them in the queue
                    </p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Terminal Logs */}
        <Card className="h-full">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle>Processing Logs</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearTerminal}
                  className="h-8 px-2"
                >
                  Clear
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTerminal(!showTerminal)}
                  className="h-8 px-2"
                >
                  {showTerminal ? "Hide" : "Show"}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {showTerminal && (
              <div
                ref={terminalRef}
                className="bg-black text-green-400 font-mono text-xs p-4 rounded-md h-[600px] overflow-y-auto"
              >
                {terminalLogs.length > 0 ? (
                  terminalLogs.map((log, index) => (
                    <div key={index} className="whitespace-pre-wrap mb-1">
                      {log}
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 italic">
                    No logs yet. Start processing to see logs here.
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Image Details Dialog */}
      <ImageDetailsDialog />
    </div>
  );
}

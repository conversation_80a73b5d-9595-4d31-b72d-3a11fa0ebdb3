"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { BatchResetResult } from "@/types/reset";

export default function TestReset() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<BatchResetResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs((prev) => [
      ...prev,
      `[${new Date().toLocaleTimeString()}] ${message}`,
    ]);
  };

  const handleReset = async (filter: string) => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      addLog(`Starting test reset with filter: ${filter}`);
      console.log(`Test component: Starting reset with filter: ${filter}`);

      // Use a hardcoded string to avoid any issues with the filter
      const actualFilter =
        filter === "generated"
          ? "generated"
          : filter === "generating"
          ? "generating"
          : "failed";

      addLog(`Using filter: ${actualFilter}`);
      console.log(`Test component: Using filter: ${actualFilter}`);

      const response = await fetch("/api/comments/reset/batch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          filter: actualFilter,
          limit: 10,
        }),
      });

      const data = await response.json();
      console.log("Test component: Result:", data);
      addLog(`Reset completed: ${JSON.stringify(data)}`);
      setResult(data);

      if (!response.ok) {
        throw new Error(data.error || "Failed to reset comments");
      }
    } catch (err) {
      console.error("Test component: Error:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      addLog(`Error: ${errorMessage}`);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Test Reset Functionality</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex space-x-2">
            <Button
              onClick={() => handleReset("generated")}
              disabled={isLoading}
              variant="default"
            >
              Test Reset Generated
            </Button>
            <Button
              onClick={() => handleReset("generating")}
              disabled={isLoading}
              variant="default"
            >
              Test Reset Generating
            </Button>
            <Button
              onClick={() => handleReset("failed")}
              disabled={isLoading}
              variant="default"
            >
              Test Reset Failed
            </Button>
          </div>

          {isLoading && <div className="text-center">Loading...</div>}

          {error && (
            <div className="p-4 bg-red-100 text-red-800 rounded-md">
              <h3 className="font-bold">Error:</h3>
              <p>{error}</p>
            </div>
          )}

          {result && (
            <div className="p-4 bg-green-100 text-green-800 rounded-md">
              <h3 className="font-bold">Result:</h3>
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}

          <div className="mt-4">
            <h3 className="font-bold mb-2">Logs:</h3>
            <div className="bg-black text-green-400 p-4 rounded-md h-[200px] overflow-y-auto font-mono text-sm">
              {logs.length === 0 ? (
                <div className="text-gray-500">No logs yet</div>
              ) : (
                logs.map((log, i) => <div key={i}>{log}</div>)
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

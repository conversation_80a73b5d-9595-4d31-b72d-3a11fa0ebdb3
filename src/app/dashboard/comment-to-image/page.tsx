import { adminDb } from "@/lib/firebase-admin";
import { Suspense } from "react";
import CommentToImageClient from "./client";
import { Skeleton } from "@/components/ui/skeleton";
import {
  type Comment,
  type ProcessingStatus,
  ImageGenerationStatus,
} from "@/types/comment-to-image";

// Server component to fetch initial data with pagination
export default async function CommentToImagePage() {
  try {
    // Get comments from Firestore
    const commentsRef = adminDb.collection("instagram_comments");
    const INITIAL_LIMIT = 50; // Limit to 50 comments initially

    // Get total count
    const countSnapshot = await commentsRef.count().get();
    const total = countSnapshot.data().count;

    // Get processed comments count (has imageUrl and completed)
    const processedSnapshot = await commentsRef
      .where("imageUrl", "!=", "")
      .where("imageGenerated", "==", true)
      .count()
      .get();
    const processed = processedSnapshot.data().count;

    // Get failed comments count
    const failedSnapshot = await commentsRef
      .where("imageGenerationStatus", "==", ImageGenerationStatus.FAILED)
      .count()
      .get();
    const failed = failedSnapshot.data().count;

    // Get in-progress comments count
    const inProgressSnapshot = await commentsRef
      .where("imageGenerationStatus", "==", ImageGenerationStatus.IN_PROGRESS)
      .count()
      .get();
    const inProgress = inProgressSnapshot.data().count;

    // Calculate unprocessed (not started) comments
    const notInitialized = total - (processed + failed + inProgress);

    // Get initial comments for display
    const snapshot = await commentsRef
      .orderBy("created_at", "desc")
      .limit(INITIAL_LIMIT)
      .get();

    // Process the comments with proper type handling
    const comments: Comment[] = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        authorName: data.authorName || "",
        username: data.username || "",
        postBody: data.postBody || "",
        platform: data.platform || "instagram",
        created_at:
          data.created_at?.toDate?.()?.toISOString() ||
          new Date().toISOString(),
        comment_like_count: data.comment_like_count || 0,
        metrics: data.metrics || { likes: 0 },
        imageUrl: data.imageUrl || "",
        imageGenerated: data.imageGenerated || false,
        imageGenerationStatus:
          data.imageGenerationStatus || ImageGenerationStatus.NOT_STARTED,
        imageGenerating: data.imageGenerating || false,
        imageCaption: data.imageCaption || "",
        imageStyle: data.imageStyle || "",
        imageDimensions: data.imageDimensions || null,
        imageProcessedAt: data.imageProcessedAt || null,
        mood: data.mood || "",
        color_palette: data.color_palette || [],
        focal_points: data.focal_points || [],
        visual_elements: data.visual_elements || [],
        lighting: data.lighting || "",
        texture_style: data.texture_style || "",
        atmosphere: data.atmosphere || "",
        emotional_impact: data.emotional_impact || "",
        visual_flow: data.visual_flow || "",
        key_elements: data.key_elements || [],
        textAnalysis: data.textAnalysis || null,
        sentimentAnalysis: data.sentimentAnalysis || null,
        storyAnalysis: data.storyAnalysis || null,
        cultureFitAnalysis: data.cultureFitAnalysis || null,
        qualityCheck: data.qualityCheck || null,
        artDirection: data.artDirection || null,
      };
    });

    // Create initial status object
    const status: ProcessingStatus = {
      total,
      processed,
      failed,
      inProgress,
      notInitialized,
      fetchedCount: comments.length,
    };

    return (
      <Suspense fallback={<Skeleton className="h-[500px] w-full" />}>
        <CommentToImageClient
          initialComments={comments}
          initialStatus={status}
        />
      </Suspense>
    );
  } catch (error) {
    console.error("Error fetching comments:", error);
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-2xl font-bold mb-4">Error</h1>
        <p className="text-red-500">
          Failed to load comments. Please try again later.
        </p>
        <pre className="bg-gray-100 p-4 mt-4 rounded">
          {error instanceof Error ? error.message : String(error)}
        </pre>
      </div>
    );
  }
}

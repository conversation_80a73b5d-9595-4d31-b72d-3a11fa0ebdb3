"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  getDocs,
  deleteDoc,
  updateDoc,
  doc,
} from "firebase/firestore";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ReloadIcon, TrashIcon } from "@radix-ui/react-icons";

export default function SettingsPage() {
  const [loading, setLoading] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const clearCollection = async (collectionName: string) => {
    setLoading(collectionName);
    setError(null);
    setSuccess(null);

    try {
      const collectionRef = collection(db, collectionName);
      const snapshot = await getDocs(query(collectionRef));

      await Promise.all(snapshot.docs.map((doc) => deleteDoc(doc.ref)));

      setSuccess(`Successfully cleared ${collectionName} collection`);
    } catch (err) {
      console.error(`Error clearing ${collectionName}:`, err);
      setError(`Failed to clear ${collectionName} collection`);
    } finally {
      setLoading("");
    }
  };

  const clearPostMosaicFields = async () => {
    setLoading("posts-mosaic");
    setError(null);
    setSuccess(null);

    try {
      const postsRef = collection(db, "social_media_posts");
      const snapshot = await getDocs(query(postsRef));

      await Promise.all(
        snapshot.docs.map((docSnapshot) =>
          updateDoc(doc(db, "social_media_posts", docSnapshot.id), {
            mosaic_generated: false,
            mosaic_url: null,
          })
        )
      );

      setSuccess("Successfully cleared mosaic fields from all posts");
    } catch (err) {
      console.error("Error clearing mosaic fields:", err);
      setError("Failed to clear mosaic fields from posts");
    } finally {
      setLoading("");
    }
  };

  const clearGeneratedImages = async () => {
    setLoading("posts-images");
    setError(null);
    setSuccess(null);

    try {
      const postsRef = collection(db, "social_media_posts");
      const snapshot = await getDocs(query(postsRef));

      await Promise.all(
        snapshot.docs.map((docSnapshot) =>
          updateDoc(doc(db, "social_media_posts", docSnapshot.id), {
            "agents.imageGenerator": null,
          })
        )
      );

      setSuccess("Successfully cleared generated images from all posts");
    } catch (err) {
      console.error("Error clearing generated images:", err);
      setError("Failed to clear generated images from posts");
    } finally {
      setLoading("");
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Data Management</CardTitle>
          <CardDescription>
            Manage your application data and collections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Clear Posts Collection</h3>
                <p className="text-sm text-muted-foreground">
                  Delete all posts from the social_media_posts collection
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    {loading === "social_media_posts" ? (
                      <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <TrashIcon className="mr-2 h-4 w-4" />
                    )}
                    Clear Posts
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      all posts from your database.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => clearCollection("social_media_posts")}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete All Posts
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Clear Mosaics Collection</h3>
                <p className="text-sm text-muted-foreground">
                  Delete all generated mosaics from the mosaics collection
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    {loading === "mosaics" ? (
                      <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <TrashIcon className="mr-2 h-4 w-4" />
                    )}
                    Clear Mosaics
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      all generated mosaics from your database.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => clearCollection("mosaics")}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete All Mosaics
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Reset Mosaic Fields</h3>
                <p className="text-sm text-muted-foreground">
                  Clear mosaic-related fields from all posts to allow
                  regeneration
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    {loading === "posts-mosaic" ? (
                      <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <TrashIcon className="mr-2 h-4 w-4" />
                    )}
                    Reset Fields
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This will reset all mosaic-related fields in posts,
                      allowing them to be used for mosaic generation again.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={clearPostMosaicFields}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Reset Fields
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Clear Generated Images</h3>
                <p className="text-sm text-muted-foreground">
                  Remove all AI-generated images from posts to allow
                  regeneration
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    {loading === "posts-images" ? (
                      <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <TrashIcon className="mr-2 h-4 w-4" />
                    )}
                    Clear Images
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This will remove all AI-generated images from posts. You
                      will need to regenerate images for these posts.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={clearGeneratedImages}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Clear Images
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

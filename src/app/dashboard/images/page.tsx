"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { db } from "@/lib/firebase";
import { collection, query, getDocs, orderBy, where } from "firebase/firestore";
import Image from "next/image";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DownloadIcon,
  EnterFullScreenIcon,
  ExternalLinkIcon,
  CalendarIcon,
  DashIcon,
  ImageIcon,
  TextIcon,
  ChatBubbleIcon,
  HeartIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@radix-ui/react-icons";
import { Separator } from "@/components/ui/separator";

interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

interface PostImage {
  id: string;
  caption: string;
  hashtag: string;
  created_at: FirestoreTimestamp;
  like_count: number;
  comment_count: number;
  platform: string;
  agents: {
    imageGenerator: {
      storageUrl: string;
      caption: string;
    };
    artGenerator: {
      visual_theme: {
        style: string;
        mood: string;
        atmosphere: string;
      };
      storytelling: {
        visual_flow: string;
        emotional_impact: string;
      };
    };
  };
}

export default function ImagesPage() {
  const [posts, setPosts] = useState<PostImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPost, setSelectedPost] = useState<PostImage | null>(null);
  const [scrollPositions, setScrollPositions] = useState<
    Record<string, number>
  >({});

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async () => {
    try {
      const postsRef = collection(db, "social_media_posts");
      const q = query(
        postsRef,
        where("agents.imageGenerator", "!=", null),
        orderBy("created_at", "desc")
      );

      const querySnapshot = await getDocs(q);
      const postDocs = querySnapshot.docs.map((doc) => ({
        ...(doc.data() as Omit<PostImage, "id">),
        id: doc.id,
      }));

      setPosts(postDocs);
    } catch (err) {
      console.error("Error fetching posts:", err);
    } finally {
      setLoading(false);
    }
  };

  const groupPostsByHashtag = () => {
    const groups = posts.reduce((acc, post) => {
      const hashtag = post.hashtag || "Uncategorized";
      if (!acc[hashtag]) {
        acc[hashtag] = [];
      }
      acc[hashtag].push(post);
      return acc;
    }, {} as Record<string, PostImage[]>);

    return Object.entries(groups).sort(([a], [b]) => a.localeCompare(b));
  };

  const handleDownload = (url: string) => {
    window.open(url, "_blank");
  };

  const handleScroll = (hashtag: string, direction: "left" | "right") => {
    const container = document.getElementById(`slider-${hashtag}`);
    if (container) {
      const scrollAmount = 800; // Scroll two images at a time (400px * 2)
      const newPosition = scrollPositions[hashtag] || 0;
      const targetPosition =
        direction === "left"
          ? Math.max(0, newPosition - scrollAmount)
          : newPosition + scrollAmount;

      container.scrollTo({
        left: targetPosition,
        behavior: "smooth",
      });

      setScrollPositions((prev) => ({
        ...prev,
        [hashtag]: targetPosition,
      }));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-10rem)]">
        <div className="text-lg text-muted-foreground">Loading images...</div>
      </div>
    );
  }

  const groupedPosts = groupPostsByHashtag();

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Generated Post Images</CardTitle>
          <CardDescription>
            View all your AI-generated post images grouped by hashtag
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {groupedPosts.map(([hashtag, posts]) => (
              <div key={hashtag} className="space-y-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">
                    {hashtag === "Uncategorized" ? hashtag : `#${hashtag}`}
                  </h3>
                  <Badge variant="secondary">{posts.length}</Badge>
                </div>
                <div className="relative">
                  <div className="relative group">
                    <div
                      id={`slider-${hashtag}`}
                      className="flex gap-4 overflow-x-hidden scroll-smooth"
                    >
                      {posts.map((post) => (
                        <div
                          key={post.id}
                          className="relative w-[400px] flex-none"
                        >
                          <div className="relative aspect-video w-full rounded-lg overflow-hidden border group">
                            {post.agents.imageGenerator.storageUrl ? (
                              <Image
                                src={post.agents.imageGenerator.storageUrl}
                                alt="Generated image"
                                fill
                                className="object-cover transition-transform group-hover:scale-105"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full bg-muted">
                                <ImageIcon className="h-8 w-8 text-muted-foreground" />
                              </div>
                            )}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                            <div className="absolute bottom-0 left-0 right-0 p-3 flex justify-between items-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  className="bg-black/50 hover:bg-black/70"
                                  onClick={() =>
                                    handleDownload(
                                      post.agents.imageGenerator.storageUrl
                                    )
                                  }
                                >
                                  <DownloadIcon className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  className="bg-black/50 hover:bg-black/70"
                                  onClick={() => setSelectedPost(post)}
                                >
                                  <EnterFullScreenIcon className="h-4 w-4" />
                                </Button>
                              </div>
                              <Badge
                                variant="secondary"
                                className="bg-black/50 text-white"
                              >
                                {post.platform}
                              </Badge>
                            </div>
                          </div>
                          <div className="mt-2 space-y-1">
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>
                                {new Date(
                                  post.created_at.seconds * 1000
                                ).toLocaleDateString()}
                              </span>
                              <div className="flex items-center gap-4">
                                <div className="flex items-center gap-1">
                                  <HeartIcon className="h-4 w-4" />
                                  <span>{post.like_count}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <ChatBubbleIcon className="h-4 w-4" />
                                  <span>{post.comment_count}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    {posts.length > 2 && (
                      <>
                        <Button
                          variant="outline"
                          size="icon"
                          className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background"
                          onClick={() => handleScroll(hashtag, "left")}
                        >
                          <ChevronLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background"
                          onClick={() => handleScroll(hashtag, "right")}
                        >
                          <ChevronRightIcon className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {groupedPosts.length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                No generated images yet. Go to the Workers page to generate
                some!
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={!!selectedPost} onOpenChange={() => setSelectedPost(null)}>
        <DialogContent className="max-w-7xl h-[90vh]">
          <DialogHeader>
            <DialogTitle>Image Details</DialogTitle>
          </DialogHeader>
          <div className="flex gap-6 h-full overflow-hidden">
            <div className="relative flex-1 min-h-0">
              {selectedPost && selectedPost.agents.imageGenerator.storageUrl ? (
                <Image
                  src={selectedPost.agents.imageGenerator.storageUrl}
                  alt="Generated image"
                  fill
                  className="object-contain rounded-lg"
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-muted">
                  <ImageIcon className="h-12 w-12 text-muted-foreground" />
                </div>
              )}
            </div>
            <div className="w-80 flex flex-col">
              {selectedPost && (
                <>
                  <div className="flex justify-end gap-2 mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleDownload(
                          selectedPost.agents.imageGenerator.storageUrl
                        )
                      }
                    >
                      <DownloadIcon className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        window.open(
                          selectedPost.agents.imageGenerator.storageUrl,
                          "_blank"
                        )
                      }
                    >
                      <ExternalLinkIcon className="h-4 w-4 mr-2" />
                      Open
                    </Button>
                  </div>
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <CalendarIcon className="h-4 w-4" />
                        <span>
                          {new Date(
                            selectedPost.created_at.seconds * 1000
                          ).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <DashIcon className="h-4 w-4" />
                        <span>#{selectedPost.hashtag || "Uncategorized"}</span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <HeartIcon className="h-4 w-4" />
                          <span>{selectedPost.like_count} likes</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ChatBubbleIcon className="h-4 w-4" />
                          <span>{selectedPost.comment_count} comments</span>
                        </div>
                      </div>
                    </div>
                    <Separator />
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <ImageIcon className="h-4 w-4" />
                        <span className="font-medium">Art Direction</span>
                      </div>
                      <ScrollArea className="h-[100px] w-full rounded-md border p-4">
                        <div className="space-y-2 text-sm text-muted-foreground">
                          <p>
                            Style:{" "}
                            {
                              selectedPost.agents.artGenerator.visual_theme
                                .style
                            }
                          </p>
                          <p>
                            Mood:{" "}
                            {selectedPost.agents.artGenerator.visual_theme.mood}
                          </p>
                          <p>
                            Atmosphere:{" "}
                            {
                              selectedPost.agents.artGenerator.visual_theme
                                .atmosphere
                            }
                          </p>
                        </div>
                      </ScrollArea>
                    </div>
                    <Separator />
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <TextIcon className="h-4 w-4" />
                        <span className="font-medium">Generated Caption</span>
                      </div>
                      <ScrollArea className="h-[200px] w-full rounded-md border p-4">
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                          {selectedPost.agents.imageGenerator.caption}
                        </p>
                      </ScrollArea>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

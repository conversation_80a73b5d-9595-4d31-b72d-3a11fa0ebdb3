"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlayIcon, StopIcon, GearIcon } from "@radix-ui/react-icons";
import { useAgents } from "@/hooks/agents-context";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
} from "firebase/firestore";
import storyGenerator from "@/lib/agents/storyGenerator";
import Link from "next/link";
import { InstagramPost, StoryGeneratorResult } from "@/types";
import { Console } from "@/components/ui/console";
import { useConsole } from "@/hooks/use-console";

export default function StoryGeneratorPage() {
  const { agents } = useAgents();
  const [processing, setProcessing] = useState(false);
  const [remainingCount, setRemainingCount] = useState(0);
  const shouldStopRef = useRef(false);
  const [hasUnanalyzedPosts, setHasUnanalyzedPosts] = useState(false);
  const [progress, setProgress] = useState({
    completed: 0,
    inProgress: 0,
  });
  const { logs, addLog, clearLogs } = useConsole();

  const agent = agents["storyGenerator"];

  useEffect(() => {
    updateRemainingCount();
  }, []);

  async function updateRemainingCount() {
    const postsRef = collection(db, "social_media_posts");
    const q = query(
      postsRef,
      where("agents.sentimentAnalyzer", "!=", null),
      where("agents.storyGenerator", "==", null)
    );
    const snapshot = await getDocs(q);
    const count = snapshot.size;
    setRemainingCount(count);
    setHasUnanalyzedPosts(count > 0);
    return count;
  }

  const processPost = async (post: InstagramPost & { docId: string }) => {
    try {
      addLog(`Processing post ${post.id}...`, "info");
      addLog(`Caption: ${post.caption.slice(0, 100)}...`, "info");

      // Get story from Gemini
      const response = await storyGenerator(agent, post);
      const result = JSON.parse(response) as StoryGeneratorResult;

      addLog(
        `Received story from Gemini for post ${post.id}`,
        "success",
        "Story generated successfully",
        result
      );

      if (result) {
        // Update the post in Firestore using the document ID
        const postRef = doc(db, "social_media_posts", post.docId);
        await updateDoc(postRef, {
          status: "story_generating",
          agents: {
            ...post.agents,
            storyGenerator: {
              ...result,
              processed_at: new Date().toISOString(),
            },
          },
        });
        addLog(`Updated post ${post.id} in Firestore`, "success");
        await updateRemainingCount();
        return true;
      } else {
        throw new Error("Failed to get story generation results");
      }
    } catch (err) {
      console.error("Error processing post:", err);
      addLog(
        `Error processing post ${post.id}: ${
          err instanceof Error ? err.message : "Unknown error"
        }`,
        "error"
      );

      // Mark as failed if there's an error, using document ID
      const postRef = doc(db, "social_media_posts", post.docId);
      await updateDoc(postRef, {
        status: "failed",
        agents: {
          ...post.agents,
          storyGenerator: null,
        },
      });
      return false;
    }
  };

  async function processPosts() {
    if (!agent?.enabled) return;

    setProcessing(true);
    shouldStopRef.current = false;

    try {
      while (!shouldStopRef.current) {
        const postsRef = collection(db, "social_media_posts");
        const q = query(
          postsRef,
          where("agents.storyGenerator", "==", null),
          where("agents.sentimentAnalyzer", "!=", null)
        );

        const snapshot = await getDocs(q);
        if (snapshot.empty) {
          addLog("No more posts to process", "info");
          break;
        }

        setProgress((prev) => ({
          ...prev,
          inProgress: snapshot.size,
        }));

        addLog(`Found ${snapshot.size} posts to process`, "info");

        for (const docSnapshot of snapshot.docs) {
          if (shouldStopRef.current) {
            addLog("Processing stopped", "warning");
            break;
          }

          const post = {
            docId: docSnapshot.id,
            id: docSnapshot.data().id,
            ...docSnapshot.data(),
          } as InstagramPost & { docId: string };

          try {
            const success = await processPost(post);
            if (success) {
              setProgress((prev) => ({
                ...prev,
                completed: prev.completed + 1,
                inProgress: prev.inProgress - 1,
              }));
            } else {
              setProgress((prev) => ({
                ...prev,
                inProgress: prev.inProgress - 1,
              }));
            }
          } catch (error) {
            if (shouldStopRef.current) break;
            console.error("Error processing post:", error);
            setProgress((prev) => ({
              ...prev,
              inProgress: prev.inProgress - 1,
            }));
          }
        }

        if (shouldStopRef.current) break;
        await updateRemainingCount();
      }
    } catch (error) {
      console.error("Error in processing loop:", error);
      addLog(
        `Error in processing loop`,
        "error",
        `${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setProcessing(false);
      setProgress((prev) => ({
        ...prev,
        inProgress: 0,
      }));
      addLog(
        "Processing stopped",
        shouldStopRef.current ? "warning" : "info",
        shouldStopRef.current ? "Stopped by user" : "Completed processing"
      );
      await updateRemainingCount();
    }
  }

  const handleProcessingToggle = async () => {
    if (processing) {
      shouldStopRef.current = true;
      addLog(
        "Stopping process...",
        "warning",
        "Waiting for current task to complete"
      );
    } else {
      processPosts();
    }
  };

  if (!agent) return null;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Story Generator</h2>
              <p className="text-sm text-muted-foreground">
                Generate engaging stories from analyzed social media posts
              </p>
            </div>
            <Link href="/dashboard/agents/storyGenerator">
              <Button variant="outline" size="icon">
                <GearIcon className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="text-sm font-medium">
                  Story Generator Status:{" "}
                  {agent.enabled ? (
                    <Badge>Enabled</Badge>
                  ) : (
                    <Badge variant="secondary">Disabled</Badge>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  {remainingCount} posts remaining to process
                </div>
              </div>
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleProcessingToggle}
                  disabled={
                    !agent.enabled || (!processing && !hasUnanalyzedPosts)
                  }
                >
                  {processing ? (
                    <>
                      <StopIcon className="mr-2 h-4 w-4" />
                      {shouldStopRef.current
                        ? "Stopping..."
                        : "Stop Processing"}
                    </>
                  ) : (
                    <>
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Start Processing
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Progress Section */}
            {(progress.completed > 0 || progress.inProgress > 0) && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Current Progress</h3>
                <div className="grid gap-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Completed:</span>
                    <Badge variant="secondary">{progress.completed}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>In Progress:</span>
                    <Badge variant="secondary">{progress.inProgress}</Badge>
                  </div>
                </div>
              </div>
            )}

            {/* Console Section */}
            <Console
              logs={logs}
              onClearLogs={clearLogs}
              title="Process Console"
              showDataPreview={true}
            />

            {/* Settings Preview */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Current Settings</h3>
              <div className="grid gap-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Model:</span>
                  <Badge variant="outline">{agent.model}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Temperature:</span>
                  <Badge variant="outline">{agent.temperature}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Max Tokens:</span>
                  <Badge variant="outline">{agent.maxTokens}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>System Prompt:</span>
                  <Badge variant="outline" className="max-w-[400px] truncate">
                    {agent.systemPrompt}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Instructions:</span>
                  <Badge variant="outline" className="max-w-[400px] truncate">
                    {agent.instructions || "None"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Custom Instructions:</span>
                  <Badge variant="outline" className="max-w-[400px] truncate">
                    {agent.customInstructions || "None"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

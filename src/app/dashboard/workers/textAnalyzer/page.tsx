/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  PlayIcon,
  StopIcon,
  GearIcon,
  ReloadIcon,
} from "@radix-ui/react-icons";
import { useAgents } from "@/hooks/agents-context";
import { db } from "@/lib/firebase";
import { collection, query, where, getDocs, limit } from "firebase/firestore";
import textAnalyzer from "@/lib/agents/textAnalyzer";
import Link from "next/link";
import { InstagramPost, TextAnalyzerResult } from "@/types";
import { AGENTS } from "@/lib/config";
import { updatePost } from "@/lib/firestore";
import { Console } from "@/components/ui/console";
import { useConsole } from "@/hooks/use-console";

export default function TextAnalyzerPage() {
  const { agents, loading } = useAgents();
  const [processing, setProcessing] = useState(false);
  const [remainingCount, setRemainingCount] = useState(0);
  const shouldStopRef = useRef(false);
  const [hasUnanalyzedPosts, setHasUnanalyzedPosts] = useState(false);
  const [batchSize, setBatchSize] = useState(10);
  const [processedCount, setProcessedCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const { logs, addLog, clearLogs } = useConsole();

  const settings = agents[AGENTS.textAnalyzer];

  useEffect(() => {
    updateRemainingCount();
  }, [processing]);

  const handleBatchSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setBatchSize(value < 1 ? 1 : value);
  };

  const updateRemainingCount = async () => {
    try {
      const q = query(
        collection(db, "social_media_posts"),
        where("status", "==", "processing"),
        where("agents.textAnalyzer", "==", null)
      );
      const snapshot = await getDocs(q);
      const count = snapshot.size;
      setRemainingCount(count);
      setHasUnanalyzedPosts(count > 0);
      return count;
    } catch (error) {
      console.error("Error updating remaining count:", error);
      addLog("Error updating remaining count", "error");
      return 0;
    }
  };

  const processPost = async (post: InstagramPost) => {
    if (shouldStopRef.current) {
      return false;
    }

    try {
      addLog(`Processing post ${post.id}...`, "info");
      addLog(`Caption: ${post.caption.slice(0, 100)}...`, "info");

      // Get analysis from Gemini
      const result = await textAnalyzer(settings, post);
      addLog(`Received analysis from Gemini for post ${post.id}`, "info");

      // Parse the JSON response
      const analysis = JSON.parse(result) as TextAnalyzerResult;

      // Log the analysis results
      addLog("Analysis Results:", "success", undefined, analysis);

      // Update the post in Firestore
      await updatePost(post.id, {
        status: "text_analyzing",
        agents: {
          ...post.agents,
          textAnalyzer: {
            ...analysis,
            processed_at: new Date().toISOString(),
          } as TextAnalyzerResult,
        },
      });

      addLog(`Updated post ${post.id} in Firestore`, "success");
      setProcessedCount((prev: number) => {
        // If we've reached the batch size, stop processing
        if (prev + 1 >= batchSize) {
          shouldStopRef.current = true;
          addLog(
            `Reached batch limit of ${batchSize} posts. Stopping...`,
            "warning"
          );
        }
        return prev + 1;
      });
      setRemainingCount((prev) => prev - 1);
      return true;
    } catch (err) {
      console.error("Error processing post:", err);
      addLog(
        `Error processing post ${post.id}: ${
          err instanceof Error ? err.message : "Unknown error"
        }`,
        "error"
      );

      // Mark as failed if there's an error
      await updatePost(post.id, {
        status: "failed",
      });
      return false;
    }
  };

  const handleStartProcessing = async () => {
    if (!settings.enabled) return;

    try {
      setProcessing(true);
      setError(null);
      setProcessedCount(0);
      shouldStopRef.current = false;
      addLog("Starting processing...", "info");

      while (!shouldStopRef.current) {
        const remainingCount = await updateRemainingCount();

        if (remainingCount === 0) {
          addLog("No more posts to process", "success");
          break;
        }

        // Calculate how many posts to process in this batch
        const postsToProcess = Math.min(
          batchSize - processedCount,
          remainingCount
        );

        if (postsToProcess <= 0) {
          addLog(`Batch limit of ${batchSize} reached. Stopping...`, "success");
          break;
        }

        addLog(`Querying for ${postsToProcess} unprocessed posts...`, "info");

        // Query for unprocessed posts with the calculated limit
        const q = query(
          collection(db, "social_media_posts"),
          where("status", "==", "processing"),
          where("agents.textAnalyzer", "==", null),
          limit(postsToProcess)
        );

        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          addLog("No more posts to process", "success");
          break;
        }

        const posts = snapshot.docs.map((doc) => ({
          ...doc.data(),
          id: doc.id,
        })) as InstagramPost[];

        addLog(`Found ${posts.length} posts to process`, "info");

        // Process posts sequentially to avoid rate limits
        for (const post of posts) {
          if (shouldStopRef.current) {
            addLog("Processing stopped", "warning");
            break;
          }
          await processPost(post);
          // Small delay between posts to prevent rate limiting
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        // Check if we should continue
        if (processedCount >= batchSize || shouldStopRef.current) {
          addLog(`Processed ${processedCount} posts. Stopping...`, "success");
          break;
        }

        // Update remaining count after batch
        await updateRemainingCount();
      }
    } catch (err) {
      console.error("Processing error:", err);
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      addLog(`Processing error: ${errorMessage}`, "error");
    } finally {
      setProcessing(false);
      shouldStopRef.current = false;
      addLog(
        `Processing completed. Total posts processed: ${processedCount}`,
        "success"
      );
      await updateRemainingCount();
    }
  };

  const handleStopProcessing = () => {
    shouldStopRef.current = true;
    addLog(
      "Stopping process...",
      "warning",
      "Waiting for current task to complete"
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <ReloadIcon className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="space-y-6">
        <Card>
          <div className="p-4 text-muted-foreground">
            Agent settings not found
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <h2 className="text-2xl font-semibold">Text Analyzer</h2>
              <Badge variant={settings.enabled ? "default" : "secondary"}>
                {settings.enabled ? "Enabled" : "Disabled"}
              </Badge>
              {processing && (
                <Badge variant="outline" className="ml-2">
                  Processed: {processedCount}
                </Badge>
              )}
              <Badge variant="secondary" className="ml-2">
                Remaining: {remainingCount}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {error ? (
                <span className="text-red-500">Error: {error}</span>
              ) : settings.enabled ? (
                processing ? (
                  "Processing posts..."
                ) : hasUnanalyzedPosts ? (
                  "Ready to analyze and process text content"
                ) : (
                  "No posts to analyze"
                )
              ) : (
                "Agent is currently disabled. Enable it in settings to start processing."
              )}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm">Batch Size</span>
              <Input
                type="number"
                value={batchSize}
                onChange={handleBatchSizeChange}
                className="w-24"
                min={1}
                disabled={processing}
                placeholder="Batch size"
              />
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon" asChild>
                <Link href="/dashboard/agents/textAnalyzer">
                  <GearIcon className="h-4 w-4" />
                </Link>
              </Button>
              <Button
                onClick={
                  processing ? handleStopProcessing : handleStartProcessing
                }
                disabled={
                  !settings.enabled || (!processing && !hasUnanalyzedPosts)
                }
                variant={processing ? "destructive" : "default"}
              >
                {processing ? (
                  <>
                    <StopIcon className="mr-2 h-4 w-4" />
                    Stop
                  </>
                ) : (
                  <>
                    <PlayIcon className="mr-2 h-4 w-4" />
                    Start
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Console Panel */}
      <Console
        logs={logs}
        onClearLogs={clearLogs}
        title="Process Console"
        showDataPreview={true}
      />

      {/* Settings Overview */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Current Settings</h3>
              <div className="grid gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Model
                  </label>
                  <p className="mt-1">{settings.model}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Temperature
                  </label>
                  <p className="mt-1">{settings.temperature}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Max Tokens
                  </label>
                  <p className="mt-1">{settings.maxTokens}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    System Prompt
                  </label>
                  <p className="mt-1 font-mono text-sm whitespace-pre-wrap">
                    {settings.systemPrompt || "No system prompt set"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Instructions
                  </label>
                  <p className="mt-1 font-mono text-sm whitespace-pre-wrap">
                    {settings.instructions || "No instructions set"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Custom Instructions
                  </label>
                  <p className="mt-1 font-mono text-sm whitespace-pre-wrap">
                    {settings.customInstructions ||
                      "No custom instructions set"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

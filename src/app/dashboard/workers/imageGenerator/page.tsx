/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  PlayIcon,
  StopIcon,
  GearIcon,
  DownloadIcon,
  ZoomInIcon,
  ZoomOutIcon,
} from "@radix-ui/react-icons";
import { useAgents } from "@/hooks/agents-context";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
  limit,
} from "firebase/firestore";
import imageGenerator from "@/lib/agents/imageGenerator";
import Link from "next/link";
import { InstagramPost } from "@/types";
import Image from "next/image";
import { Console } from "@/components/ui/console";
import { useConsole } from "@/hooks/use-console";

export default function ImageGeneratorPage() {
  const { agents } = useAgents();
  const [processing, setProcessing] = useState(false);
  const [remainingCount, setRemainingCount] = useState(0);
  const shouldStopRef = useRef(false);
  const [hasUnanalyzedPosts, setHasUnanalyzedPosts] = useState(false);
  const [processedCount, setProcessedCount] = useState(0);
  const [batchSize, setBatchSize] = useState(10);
  const [currentImage, setCurrentImage] = useState<{
    storageUrl: string;
    caption: string;
  } | null>(null);
  const [imageZoom, setImageZoom] = useState(false);
  const { logs, addLog, clearLogs } = useConsole();

  const agent = agents["imageGenerator"];

  useEffect(() => {
    updateRemainingCount();
  }, []);

  const handleBatchSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setBatchSize(value < 1 ? 1 : value);
  };

  async function updateRemainingCount() {
    const postsRef = collection(db, "social_media_posts");
    const q = query(
      postsRef,
      where("agents.imageGenerator", "==", null),
      where("agents.artGenerator", "!=", null)
    );

    try {
      const snapshot = await getDocs(q);
      const readyPosts = snapshot.docs.filter((doc) => {
        const post = doc.data();
        return post.agents?.artGenerator?.processed_at;
      });

      const count = readyPosts.length;
      setRemainingCount(count);
      setHasUnanalyzedPosts(count > 0);

      if (count > 0) {
        addLog(`Found ${count} posts ready for image generation`, "info");
      }

      return count;
    } catch (error) {
      console.error("Error getting remaining count:", error);
      addLog("Error getting remaining count", "error");
      return 0;
    }
  }

  const processPost = async (post: InstagramPost & { docId: string }) => {
    const MAX_RETRIES = 2;
    const RETRY_DELAY = 1000; // 2 seconds

    // Track if we need to modify the prompt
    let shouldModifyPrompt = false;
    let shouldModifyStory = false;
    let lastError = "";
    let currentStory = post.agents.storyGenerator?.story || post.caption;
    let storyAttempt = 1;

    // Update post status to processing
    const postRef = doc(db, "social_media_posts", post.docId);
    await updateDoc(postRef, {
      status: "image_generating",
      updated_at: new Date().toISOString(),
    });

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        if (!post.agents?.artGenerator?.processed_at) {
          addLog(
            `Skipping post ${post.id}: Missing required agent results`,
            "warning"
          );
          await updateDoc(postRef, {
            status: "failed",
            agents: {
              ...post.agents,
              imageGenerator: {
                error: "Missing required art generator results",
                failed_at: new Date().toISOString(),
              },
            },
          });
          return false;
        }

        if (attempt > 1) {
          addLog(
            `Retry attempt ${attempt}/${MAX_RETRIES} for post ${post.id}`,
            "warning"
          );

          // Update status to show retry attempt
          await updateDoc(postRef, {
            status: "image_generating",
            "agents.imageGenerator.retry_attempt": attempt,
            updated_at: new Date().toISOString(),
          });

          // If previous attempt failed, try with a different story or modify the prompt
          if (shouldModifyStory && post.agents.storyGenerator?.story) {
            // Get a different part of the story each time
            const story = post.agents.storyGenerator?.story;
            if (typeof story !== "string") {
              currentStory = post.caption;
              continue;
            }
            const sentences = story.split(/[.!?]+/).filter(Boolean);
            const startIdx = Math.min(storyAttempt - 1, sentences.length - 1);
            const endIdx = Math.min(startIdx + 2, sentences.length);
            currentStory = sentences.slice(startIdx, endIdx).join(". ") + ".";
            storyAttempt++;

            addLog(
              "Trying with a different part of the story",
              "info",
              `New story segment: ${currentStory}`
            );

            // Update status to show story modification
            await updateDoc(postRef, {
              "agents.imageGenerator.current_story": currentStory,
              "agents.imageGenerator.story_attempt": storyAttempt,
              updated_at: new Date().toISOString(),
            });
          } else if (shouldModifyPrompt) {
            addLog(
              "Adjusting prompt for safety and generation compatibility",
              "info",
              `Previous error: ${lastError}`
            );

            // Update status to show prompt modification
            await updateDoc(postRef, {
              "agents.imageGenerator.modified_prompt": true,
              updated_at: new Date().toISOString(),
            });
          }

          // Wait before retrying
          await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
        }

        addLog(`Processing post ${post.id}...`, "info");
        addLog(`Caption: ${post.caption.slice(0, 100)}...`, "info");

        // Log art direction details
        const artDirection = post.agents.artGenerator;
        addLog(
          "Art Direction Details",
          "info",
          `Style: ${artDirection?.visual_theme?.style}`
        );

        // Create a modified post object if needed
        const postToProcess = {
          ...post,
          caption: currentStory || "",
          agents: {
            ...post.agents,
            artGenerator: shouldModifyPrompt
              ? {
                  ...post.agents.artGenerator,
                  visual_theme: {
                    ...post.agents.artGenerator.visual_theme,
                    style:
                      post.agents.artGenerator.visual_theme?.style?.includes(
                        "photograph"
                      )
                        ? "digital art"
                        : post.agents.artGenerator.visual_theme?.style,
                    mood: post.agents.artGenerator.visual_theme?.mood
                      ?.replace(/intense|dramatic|dark|moody/g, "gentle")
                      .replace(/vibrant|energetic/g, "balanced"),
                  },
                  composition: {
                    ...post.agents.artGenerator.composition,
                    layout: "centered composition",
                    focal_points:
                      post.agents.artGenerator.composition?.focal_points?.map(
                        (fp) => fp.replace(/person|face|body/g, "subject")
                      ),
                  },
                }
              : post.agents.artGenerator,
          },
        } as unknown as InstagramPost;

        const result = await imageGenerator(agent, postToProcess);

        // Validate the result
        if (!result || !result.storageUrl) {
          throw new Error("Invalid result: Missing storage URL");
        }

        addLog(
          `Generated image for post ${post.id}`,
          "success",
          `Image saved to: ${result.storageUrl}`,
          result
        );

        if (result.storageUrl) {
          setCurrentImage({
            storageUrl: result.storageUrl,
            caption: result.caption,
          });
        }

        // Update post with success status
        await updateDoc(postRef, {
          status: "completed",
          "agents.imageGenerator": {
            processed_at: new Date().toISOString(),
            caption: result.caption,
            storageUrl: result.storageUrl,
            modified_prompt: shouldModifyPrompt,
            modified_story: shouldModifyStory,
            story_attempt: storyAttempt,
            retry_attempt: attempt,
            current_story: currentStory,
          },
          updated_at: new Date().toISOString(),
        });

        addLog(`Updated post ${post.id} in Firestore`, "success");
        setProcessedCount((prev) => prev + 1);
        addLog(`Processed ${processedCount} posts`, "info");
        await updateRemainingCount();
        return true;
      } catch (err) {
        console.error(
          `Error processing post (attempt ${attempt}/${MAX_RETRIES}):`,
          err
        );
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";
        lastError = errorMessage;

        // Check for safety or generation-related errors
        const isSafetyError =
          errorMessage.toLowerCase().includes("safety") ||
          errorMessage.toLowerCase().includes("content policy") ||
          errorMessage.toLowerCase().includes("inappropriate");

        const isGenerationError =
          errorMessage.includes("Invalid response from Vertex AI") ||
          errorMessage.includes("No predictions returned") ||
          errorMessage.includes("Failed to generate");

        // First try with a different story segment, then modify prompt if that doesn't work
        if ((isSafetyError || isGenerationError) && attempt < MAX_RETRIES) {
          if (!shouldModifyStory && post.agents.storyGenerator?.story) {
            shouldModifyStory = true;
            addLog(
              "Will try with a different part of the story",
              "warning",
              `Error: ${errorMessage}. Attempt ${attempt}/${MAX_RETRIES}`
            );
          } else {
            shouldModifyPrompt = true;
            addLog(
              `Will modify prompt for next attempt due to ${
                isSafetyError ? "safety" : "generation"
              } issues`,
              "warning",
              `Error: ${errorMessage}. Attempt ${attempt}/${MAX_RETRIES}`
            );
          }

          // Update status to show retry strategy
          await updateDoc(postRef, {
            status: "image_generating",
            "agents.imageGenerator": {
              error: errorMessage,
              retry_attempt: attempt,
              modified_prompt: shouldModifyPrompt,
              modified_story: shouldModifyStory,
              story_attempt: storyAttempt,
              current_story: currentStory,
              last_error: lastError,
            },
            updated_at: new Date().toISOString(),
          });

          continue;
        }

        // If we've exhausted retries or it's a different error
        addLog(
          `Error processing post ${post.id}`,
          "error",
          `${errorMessage}${attempt > 1 ? ` (after ${attempt} attempts)` : ""}${
            shouldModifyPrompt ? " (with modified prompt)" : ""
          }${shouldModifyStory ? ` (with modified story)` : ""}`
        );

        // Update post with final failure status
        await updateDoc(postRef, {
          status: "failed",
          agents: {
            ...post.agents,
            imageGenerator: {
              error: errorMessage,
              failed_at: new Date().toISOString(),
              attempts: attempt,
              modified_prompt: shouldModifyPrompt,
              modified_story: shouldModifyStory,
              story_attempt: storyAttempt,
              current_story: currentStory,
              last_error: lastError,
              retry_attempt: attempt,
            },
          },
        });
        return false;
      }
    }
    return false;
  };

  async function processPosts() {
    if (!agent?.enabled) {
      addLog("Image Generator is disabled", "warning");
      return;
    }

    setProcessing(true);
    shouldStopRef.current = false;
    setProcessedCount(0);

    try {
      while (!shouldStopRef.current) {
        const postsRef = collection(db, "social_media_posts");
        const q = query(
          postsRef,
          where("agents.imageGenerator", "==", null),
          where("agents.artGenerator", "!=", null),
          limit(batchSize)
        );

        const snapshot = await getDocs(q);
        if (snapshot.empty) {
          addLog("No more posts to process", "info");
          break;
        }

        const readyPosts = snapshot.docs.filter((doc) => {
          const post = doc.data();
          return post.agents?.artGenerator?.processed_at;
        });

        if (readyPosts.length === 0) {
          addLog("No posts ready for processing", "info");
          break;
        }

        addLog(`Found ${readyPosts.length} posts to process`, "info");

        for (const docSnapshot of readyPosts) {
          if (shouldStopRef.current) {
            addLog("Processing stopped", "warning");
            break;
          }

          const post = {
            docId: docSnapshot.id,
            id: docSnapshot.data().id,
            ...docSnapshot.data(),
          } as InstagramPost & { docId: string };

          try {
            await processPost(post);
            // Small delay between posts to prevent rate limiting
            await new Promise((resolve) => setTimeout(resolve, 1000));
          } catch (error) {
            if (shouldStopRef.current) break;
            console.error("Error processing post:", error);
            addLog(
              `Error processing post: ${
                error instanceof Error ? error.message : "Unknown error"
              }`,
              "error"
            );
          }
        }

        if (shouldStopRef.current) break;
        await updateRemainingCount();
      }
    } catch (error) {
      console.error("Error in processing loop:", error);
      addLog(
        `Error in processing loop`,
        "error",
        `${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setProcessing(false);
      shouldStopRef.current = false;
      addLog(
        "Processing stopped",
        shouldStopRef.current ? "warning" : "info",
        shouldStopRef.current ? "Stopped by user" : "Completed processing"
      );
      await updateRemainingCount();
    }
  }

  const handleProcessingToggle = async () => {
    if (processing) {
      shouldStopRef.current = true;
      addLog(
        "Stopping process...",
        "warning",
        "Waiting for current task to complete"
      );
    } else {
      processPosts();
    }
  };

  const handleDownloadImage = () => {
    if (currentImage?.storageUrl) {
      window.open(currentImage.storageUrl, "_blank");
    }
  };

  if (!agent) return null;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Image Generator</h2>
              <p className="text-sm text-muted-foreground">
                Generate images from art direction and visual guidelines
              </p>
            </div>
            <Link href="/dashboard/agents/imageGenerator">
              <Button variant="outline" size="icon">
                <GearIcon className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status and Controls */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="text-sm font-medium">
                  Image Generator Status:{" "}
                  {agent.enabled ? (
                    <Badge>Enabled</Badge>
                  ) : (
                    <Badge variant="secondary">Disabled</Badge>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  {remainingCount} posts remaining to process
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Batch Size</span>
                  <Input
                    type="number"
                    value={batchSize}
                    onChange={handleBatchSizeChange}
                    className="w-24"
                    min={1}
                    disabled={processing}
                    placeholder="Batch size"
                  />
                </div>
                <Button
                  onClick={handleProcessingToggle}
                  disabled={
                    !agent.enabled || (!processing && !hasUnanalyzedPosts)
                  }
                >
                  {processing ? (
                    <>
                      <StopIcon className="mr-2 h-4 w-4" />
                      {shouldStopRef.current
                        ? "Stopping..."
                        : "Stop Processing"}
                    </>
                  ) : (
                    <>
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Start Processing
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Progress Section */}
            {processedCount > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Current Progress</h3>
                <div className="grid gap-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Completed:</span>
                    <Badge variant="secondary">{processedCount}</Badge>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content Grid */}
            <div className="grid grid-cols-2 gap-6">
              {/* Left Column: Console */}
              <div className="space-y-4">
                <Console
                  logs={logs}
                  onClearLogs={clearLogs}
                  title="Process Console"
                  showDataPreview={true}
                />
              </div>

              {/* Right Column: Image Preview and Settings */}
              <div className="space-y-4">
                {/* Image Preview */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">Generated Image</h3>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setImageZoom(!imageZoom)}
                        disabled={!currentImage}
                      >
                        {imageZoom ? (
                          <ZoomOutIcon className="h-4 w-4" />
                        ) : (
                          <ZoomInIcon className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDownloadImage}
                        disabled={!currentImage}
                      >
                        <DownloadIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div
                    className={`relative rounded-lg border bg-muted ${
                      imageZoom ? "h-[600px]" : "h-[300px]"
                    } transition-all duration-200 group`}
                  >
                    {currentImage ? (
                      <>
                        <div className="relative h-full">
                          <Image
                            src={currentImage.storageUrl}
                            alt="Generated image"
                            className="object-contain"
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                        </div>
                        <div className="absolute bottom-0 left-0 right-0 bg-background/90 p-3 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 backdrop-blur-sm max-h-[50%] overflow-y-auto">
                          {currentImage.caption}
                        </div>
                      </>
                    ) : (
                      <div className="flex h-full items-center justify-center text-sm text-muted-foreground">
                        No image generated yet
                      </div>
                    )}
                  </div>
                </div>

                {/* Settings Preview */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Generation Settings</h3>
                  <div className="grid gap-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Model:</span>
                      <Badge variant="outline">{agent.model}</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Sample Count:</span>
                      <Badge variant="outline">{agent.sampleCount || 1}</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Aspect Ratio:</span>
                      <Badge variant="outline">
                        {agent.aspectRatio || "1:1"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Sample Image Style:</span>
                      <Badge variant="outline">
                        {agent.sampleImageStyle || "photograph"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Person Generation:</span>
                      <Badge variant="outline">
                        {agent.personGeneration || "allow_adult"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Safety Setting:</span>
                      <Badge variant="outline">
                        {agent.safetySetting || "block_medium_and_above"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

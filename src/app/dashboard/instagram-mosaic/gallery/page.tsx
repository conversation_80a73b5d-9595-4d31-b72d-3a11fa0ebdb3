"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  getDocs,
  orderBy,
  limit,
  startAfter,
  DocumentData,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { ReloadIcon, ImageIcon, CalendarIcon } from "@radix-ui/react-icons";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface Mosaic {
  id: string;
  url: string;
  prompt: string;
  created_at: string;
  commentIds: string[];
  platform: string;
}

const ITEMS_PER_PAGE = 12;

export default function MosaicGalleryPage() {
  const { toast } = useToast();
  const [mosaics, setMosaics] = useState<Mosaic[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedMosaic, setSelectedMosaic] = useState<Mosaic | null>(null);
  const [lastDoc, setLastDoc] =
    useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchMosaics = async (isInitial: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      const mosaicsRef = collection(db, "instagram_mosaics");
      let q = query(
        mosaicsRef,
        orderBy("created_at", "desc"),
        limit(ITEMS_PER_PAGE)
      );

      if (!isInitial && lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        if (isInitial) {
          setError("No mosaics found");
          setMosaics([]);
        }
        setHasMore(false);
        return;
      }

      const newMosaics = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Mosaic[];

      setMosaics((prev) => (isInitial ? newMosaics : [...prev, ...newMosaics]));
      setLastDoc(snapshot.docs[snapshot.docs.length - 1]);
      setHasMore(snapshot.docs.length === ITEMS_PER_PAGE);
    } catch (err) {
      console.error("Error fetching mosaics:", err);
      setError("Failed to fetch mosaics");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch mosaics. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMosaics(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const filteredMosaics = mosaics.filter((mosaic) =>
    mosaic.prompt.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Mosaic Gallery</h1>
          <p className="text-muted-foreground">
            Browse and explore generated mosaics
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Input
            placeholder="Search by prompt..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Button
            variant="outline"
            onClick={() => fetchMosaics(true)}
            disabled={loading}
            className="gap-2"
          >
            <ReloadIcon className={loading ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>
      </div>

      {error ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-6">
            <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">{error}</h3>
            <p className="text-muted-foreground mt-2">
              Try refreshing or generating some mosaics first.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredMosaics.map((mosaic) => (
              <Card
                key={mosaic.id}
                className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group"
                onClick={() => setSelectedMosaic(mosaic)}
              >
                <div className="relative aspect-[4/3] w-full">
                  <Image
                    src={mosaic.url.split("?")[0]}
                    alt="Mosaic"
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {format(new Date(mosaic.created_at), "MMM d, yyyy")}
                      </span>
                    </div>
                    <Badge variant="secondary">
                      {mosaic.commentIds.length}% of Stories
                    </Badge>
                  </div>
                  <p className="text-sm line-clamp-2">{mosaic.prompt}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {hasMore && (
            <div className="mt-6 text-center">
              <Button onClick={() => fetchMosaics(false)} disabled={loading}>
                {loading ? (
                  <>
                    <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Load More"
                )}
              </Button>
            </div>
          )}
        </>
      )}

      {selectedMosaic && (
        <Dialog
          open={!!selectedMosaic}
          onOpenChange={() => setSelectedMosaic(null)}
        >
          <DialogContent className="w-full max-w-[80%] overflow-y-scroll max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Mosaic Details</DialogTitle>
              <DialogDescription>
                Created on{" "}
                {format(new Date(selectedMosaic.created_at), "MMMM d, yyyy")}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="relative aspect-video w-full max-h-[400px] rounded-lg overflow-hidden">
                <Image
                  src={selectedMosaic.url.split("?")[0]}
                  alt="Mosaic"
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">Prompt</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedMosaic.prompt}
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">Details</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">
                      Percentage of Stories Used:
                    </span>{" "}
                    {selectedMosaic.commentIds.length}%
                  </div>
                  <div>
                    <span className="text-muted-foreground">Platform:</span>{" "}
                    {selectedMosaic.platform}
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { db } from "@/lib/firebase";
import { collection, query, where, getDocs } from "firebase/firestore";
import { ReloadIcon, ImageIcon } from "@radix-ui/react-icons";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";

interface Comment {
  id: string;
  authorName: string;
  username: string;
  postBody: string;
  created_at: string;
  comment_like_count: number;
  metrics: {
    likes: number;
  };
  imageUrl?: string;
  imageGenerated?: boolean;
  imageGenerating?: boolean;
  imageCaption?: string;
  imageStyle?: string;
  imageDimensions?: {
    width: string;
    height: string;
  };
  imageProcessedAt?: string;
  mood?: string;
  color_palette?: string[];
  focal_points?: string[];
  visual_elements?: string[];
  lighting?: string;
  texture_style?: string;
  atmosphere?: string;
  emotional_impact?: string;
  visual_flow?: string;
  key_elements?: string[];
  mosaic_generated?: boolean;
  mosaic_url?: string;
}

export default function InstagramMosaicGeneratorPage() {
  const { toast } = useToast();
  const [numberOfComments, setNumberOfComments] = useState<number>(30);
  const [maxAvailableComments, setMaxAvailableComments] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedComments, setSelectedComments] = useState<Comment[]>([]);
  const [generatingMosaic, setGeneratingMosaic] = useState(false);
  const [mosaicUrl, setMosaicUrl] = useState<string | null>(null);
  const [generatedText, setGeneratedText] = useState<string | null>(null);
  const [progressStatus, setProgressStatus] = useState<string>("");
  const [progressValue, setProgressValue] = useState<number>(0);
  const [stats, setStats] = useState({
    totalComments: 0,
    availableComments: 0,
    usedInMosaics: 0,
    averageEngagement: 0,
  });

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setError(null);
      const commentsRef = collection(db, "instagram_comments");

      // Get all comments with generated images
      const allCommentsSnapshot = await getDocs(
        query(commentsRef, where("imageGenerated", "==", true))
      );

      // If no comments with imageGenerated=true, try looking for comments with imageUrl
      let allComments = allCommentsSnapshot.docs.map((doc) => ({
        ...(doc.data() as Comment),
        id: doc.id,
      }));

      if (allComments.length === 0) {
        console.log(
          "No comments found with imageGenerated=true, checking for imageUrl"
        );
        const imageUrlSnapshot = await getDocs(
          query(commentsRef, where("imageUrl", "!=", ""))
        );

        allComments = imageUrlSnapshot.docs.map((doc) => ({
          ...(doc.data() as Comment),
          id: doc.id,
        }));

        console.log(`Found ${allComments.length} comments with imageUrl`);
      } else {
        console.log(
          `Found ${allComments.length} comments with imageGenerated=true`
        );
      }

      // Count comments used in mosaics
      const usedInMosaics = allComments.filter(
        (comment) => comment.mosaic_generated === true
      ).length;

      // Calculate total engagement
      const totalEngagement = allComments.reduce(
        (sum, comment) => sum + (comment.comment_like_count || 0),
        0
      );

      // Filter for available comments (not used in mosaics)
      const availableComments = allComments.filter(
        (comment) => !comment.mosaic_generated
      );

      setStats({
        totalComments: allComments.length,
        availableComments: availableComments.length,
        usedInMosaics,
        averageEngagement:
          allComments.length > 0 ? totalEngagement / allComments.length : 0,
      });

      setMaxAvailableComments(availableComments.length);

      // Adjust number of comments if needed
      if (numberOfComments > availableComments.length) {
        setNumberOfComments(
          Math.max(1, Math.min(availableComments.length, 50))
        );
      }

      if (availableComments.length === 0) {
        setError(
          "No unprocessed comments available. Please generate images for some comments first."
        );
      } else {
        setError(null);
      }
    } catch (err) {
      console.error("Error fetching stats:", err);
      setError("Failed to fetch statistics");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch statistics. Please try again.",
      });
    }
  };

  const clearProgress = () => {
    setProgressStatus("");
    setProgressValue(0);
    setGeneratedText(null);
  };

  const handleGenerateMosaic = async () => {
    try {
      setError(null);
      setLoading(true);
      setGeneratingMosaic(true);
      clearProgress();

      setProgressValue(10);
      setProgressStatus("Sending request to generate mosaic...");

      // Call our Firebase function to generate the mosaic
      const response = await fetch(
        "https://us-central1-qnd-platform.cloudfunctions.net/instagramMosaicGenerator",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            count: numberOfComments,
          }),
        }
      );

      setProgressValue(25);
      setProgressStatus("Processing response...");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate mosaic");
      }

      const data = await response.json();

      if (!data.success || !data.mosaic?.url) {
        throw new Error("Failed to generate mosaic");
      }

      setProgressValue(75);
      setProgressStatus("Mosaic generated successfully!");

      // Set the mosaic URL and fetch the processed comments
      setMosaicUrl(data.mosaic.url);
      setGeneratedText(data.mosaic.prompt);

      // Fetch the comments that were used
      const commentsRef = collection(db, "instagram_comments");
      const commentsSnapshot = await getDocs(
        query(commentsRef, where("mosaic_url", "==", data.mosaic.url))
      );

      const comments = commentsSnapshot.docs.map((doc) => ({
        ...(doc.data() as Comment),
        id: doc.id,
      }));

      setSelectedComments(comments);
      setProgressValue(100);

      // Refresh stats
      await fetchStats();

      toast({
        title: "Success",
        description: `Mosaic generated successfully with ${data.commentsProcessed} comments!`,
      });
    } catch (err) {
      console.error("Error generating mosaic:", err);
      setError(
        `Failed to generate mosaic: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
      toast({
        variant: "destructive",
        title: "Error",
        description: `Failed to generate mosaic: ${
          err instanceof Error ? err.message : String(err)
        }`,
      });
    } finally {
      setLoading(false);
      setGeneratingMosaic(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">QND Mosaic Generator</h1>
          <p className="text-muted-foreground">
            Create beautiful mosaics from Instagram comments with generated
            images
          </p>
        </div>
        <Button
          variant="outline"
          onClick={fetchStats}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <ReloadIcon className={loading ? "animate-spin" : ""} />
          Refresh Stats
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 min-h-[600px]">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Mosaic Settings</CardTitle>
            <CardDescription>
              Configure how your mosaic will be generated
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="numberOfComments">
                Percentage of Stories to use {numberOfComments}%
              </Label>
              <Input
                id="numberOfComments"
                type="range"
                min="1"
                max={Math.min(maxAvailableComments, 100)}
                value={numberOfComments}
                onChange={(e) => setNumberOfComments(parseInt(e.target.value))}
                disabled={loading || generatingMosaic}
              />
              <p className="text-sm text-muted-foreground">
                Using more comments will create a more diverse and rich mosaic
              </p>
            </div>

            <Button
              onClick={handleGenerateMosaic}
              disabled={
                loading || generatingMosaic || stats.availableComments === 0
              }
              className="w-full"
            >
              {(loading || generatingMosaic) && (
                <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
              )}
              Generate Mosaic
            </Button>

            {error && (
              <Alert variant="destructive">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Mosaic Preview</CardTitle>
            <CardDescription>
              {generatingMosaic || loading
                ? "Generating your mosaic..."
                : mosaicUrl
                ? "Your generated mosaic"
                : "Select settings and generate a mosaic"}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center w-full h-full">
            {generatingMosaic || loading ? (
              <div className="w-full space-y-4">
                <Progress value={progressValue} className="w-full" />
                <p className="text-center text-muted-foreground">
                  {progressStatus}
                </p>
              </div>
            ) : mosaicUrl ? (
              <div className="flex flex-col items-center w-full h-full">
                <div className="relative w-full h-[500px] mb-4">
                  <Image
                    src={mosaicUrl}
                    alt="Generated Mosaic"
                    fill
                    className="object-contain"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setMosaicUrl(null);
                    setSelectedComments([]);
                    clearProgress();
                  }}
                >
                  Clear & Start Over
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center text-center p-6">
                <ImageIcon className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No Mosaic Generated Yet</h3>
                <p className="text-muted-foreground mt-2">
                  Configure your settings and click Generate Mosaic to create a
                  beautiful artwork from Instagram comments.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {generatedText && mosaicUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Prompt</CardTitle>
            <CardDescription>
              The prompt used to generate this mosaic
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">{generatedText}</p>
          </CardContent>
        </Card>
      )}

      {selectedComments.length > 0 && !generatingMosaic && !loading && (
        <Card>
          <CardHeader>
            <CardTitle>Percentage of Stories Used</CardTitle>
            <CardDescription>
              {selectedComments.length}% of stories were used to generate this
              mosaic
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px]">
              <div className="space-y-4">
                {selectedComments.map((comment) => (
                  <div
                    key={comment.id}
                    className="flex items-start space-x-4 p-4 border rounded-md hover:bg-accent/50 transition-colors"
                  >
                    {comment.imageUrl && (
                      <div className="relative w-16 h-16 flex-shrink-0">
                        <Image
                          src={comment.imageUrl}
                          alt="Generated Image"
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge variant="secondary">
                          {comment.comment_like_count} likes
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {comment.postBody}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

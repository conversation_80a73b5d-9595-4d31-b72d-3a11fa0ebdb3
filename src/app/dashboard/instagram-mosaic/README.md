# Instagram Comments Mosaic Generator

This feature allows you to generate beautiful mosaic artwork from Instagram comments that have already had images generated for them.

## How It Works

1. The system fetches Instagram comments that have:

   - Successfully generated images (`imageGenerated: true`)
   - Not been used in mosaics yet (`mosaic_generated: false`)

2. You can select how many comments to include in the mosaic (up to 100)

3. When you click "Generate Mosaic":

   - The system sends a request to a Firebase Cloud Function
   - The function uses <PERSON> to analyze the comments and create an artistic prompt
   - The prompt is sent to Google's Imagen API to generate a high-quality mosaic image
   - The result is stored in Firebase Storage and linked to the comments

4. The generated mosaic and the comments used are displayed in the UI

## Technical Implementation

### Frontend (Next.js)

- Located in `src/app/dashboard/instagram-mosaic/page.tsx`
- Uses React hooks for state management
- Communicates with Firebase Firestore for data
- Calls the Firebase Cloud Function for mosaic generation

### Backend (Firebase Cloud Functions)

- Located in `functions/src/instagram-mosaic-generator.ts`
- Uses Gemini 1.5 Flash for text analysis and prompt generation
- Uses Imagen 3.0 for image generation
- Updates Firestore documents to track which comments have been used

## Data Structure

### Instagram Comments Collection

- Each comment document is updated with:
  - `mosaic_generated: true` when used in a mosaic
  - `mosaic_url: "url"` pointing to the generated image

### Instagram Mosaics Collection

- Each mosaic document contains:
  - `url`: The URL to the generated image
  - `prompt`: The artistic prompt used to generate the image
  - `commentIds`: Array of comment IDs used in the mosaic
  - `created_at`: Timestamp of creation
  - `platform`: Always "instagram"

## Usage Tips

- Using more comments (30-50) typically results in more diverse and interesting mosaics
- The system prioritizes comments with higher engagement (likes)
- Each comment can only be used in one mosaic
- The generation process may take 30-60 seconds to complete

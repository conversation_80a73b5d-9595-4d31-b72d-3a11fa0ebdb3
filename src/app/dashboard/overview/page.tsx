"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
} from "firebase/firestore";
import {
  ImageIcon,
  MessagesSquare,
  Layers,
  Clock,
  CheckCircle2,
  BarChart3,
  Sparkles,
  Star,
  ThumbsUp,
  Users,
  Loader2,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

interface Stats {
  comments: {
    total: number;
    processed: number;
    failed: number;
    inProgress: number;
    notInitialized: number;
  };
  mosaics: {
    total: number;
    fromInstagram: number;
    fromComments: number;
    averageSize: number;
  };
  posts: {
    total: number;
    withImages: number;
    usedInMosaics: number;
  };
}

interface RecentItemsState {
  comments: any[];
  mosaics: any[];
}

export default function OverviewPage() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<Stats>({
    comments: {
      total: 0,
      processed: 0,
      failed: 0,
      inProgress: 0,
      notInitialized: 0,
    },
    mosaics: {
      total: 0,
      fromInstagram: 0,
      fromComments: 0,
      averageSize: 0,
    },
    posts: {
      total: 0,
      withImages: 0,
      usedInMosaics: 0,
    },
  });

  const [recentItems, setRecentItems] = useState<RecentItemsState>({
    comments: [],
    mosaics: [],
  });

  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      try {
        await Promise.all([fetchStats(), fetchRecentItems()]);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  const fetchRecentItems = async () => {
    try {
      // Fetch recent processed comments with images
      const commentsRef = collection(db, "instagram_comments");
      const recentCommentsQuery = query(
        commentsRef,
        where("imageGenerated", "==", true),
        where("imageUrl", "!=", ""),
        orderBy("imageUrl"),
        orderBy("created_at", "desc"),
        limit(5)
      );

      const recentCommentsSnapshot = await getDocs(recentCommentsQuery);
      const recentComments = recentCommentsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Fetch recent mosaics
      const mosaicsRef = collection(db, "instagram_mosaics");
      const recentMosaicsQuery = query(
        mosaicsRef,
        orderBy("created_at", "desc"),
        limit(6)
      );

      const recentMosaicsSnapshot = await getDocs(recentMosaicsQuery);
      const recentMosaics = recentMosaicsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      setRecentItems({
        comments: recentComments,
        mosaics: recentMosaics,
      });

      console.log("Recent comments:", recentComments);
      console.log("Recent mosaics:", recentMosaics);
    } catch (error) {
      console.error("Error fetching recent items:", error);
    }
  };

  const fetchStats = async () => {
    try {
      console.log("Fetching stats...");
      const commentsRef = collection(db, "instagram_comments");

      // Get total comments
      const totalCommentsSnapshot = await getDocs(commentsRef);
      const totalComments = totalCommentsSnapshot.size;
      console.log("Total comments:", totalComments);

      // Get processed comments (has imageUrl AND imageGenerated true)
      const processedCommentsQuery = query(
        commentsRef,
        where("imageGenerated", "==", true)
      );
      const processedCommentsSnapshot = await getDocs(processedCommentsQuery);
      const processedComments = processedCommentsSnapshot.size;
      console.log("Processed comments:", processedComments);

      // Get failed comments
      const failedCommentsQuery = query(
        commentsRef,
        where("imageGenerationStatus", "==", "FAILED")
      );
      const failedCommentsSnapshot = await getDocs(failedCommentsQuery);
      const failedComments = failedCommentsSnapshot.size;
      console.log("Failed comments:", failedComments);

      // Get in-progress comments
      const inProgressCommentsQuery = query(
        commentsRef,
        where("imageGenerationStatus", "==", "IN_PROGRESS")
      );
      const inProgressCommentsSnapshot = await getDocs(inProgressCommentsQuery);
      const inProgressComments = inProgressCommentsSnapshot.size;
      console.log("In-progress comments:", inProgressComments);

      // Fetch mosaics stats
      const mosaicsRef = collection(db, "instagram_mosaics");
      const mosaicsSnapshot = await getDocs(mosaicsRef);
      const totalMosaics = mosaicsSnapshot.size;
      console.log("Total mosaics:", totalMosaics);

      // Get Instagram mosaics
      const instagramMosaicsQuery = query(
        mosaicsRef,
        where("platform", "==", "instagram")
      );
      const instagramMosaicsSnapshot = await getDocs(instagramMosaicsQuery);
      const instagramMosaics = instagramMosaicsSnapshot.size;
      console.log("Instagram mosaics:", instagramMosaics);

      // Get comment mosaics
      const commentMosaicsQuery = query(
        mosaicsRef,
        where("platform", "==", "comment")
      );
      const commentMosaicsSnapshot = await getDocs(commentMosaicsQuery);
      const commentMosaics = commentMosaicsSnapshot.size;
      console.log("Comment mosaics:", commentMosaics);

      // Calculate average mosaic size
      let totalElements = 0;
      mosaicsSnapshot.docs.forEach((doc) => {
        const data = doc.data();
        if (data.commentIds && Array.isArray(data.commentIds)) {
          totalElements += data.commentIds.length;
        }
      });
      const averageSize = totalMosaics > 0 ? totalElements / totalMosaics : 0;
      console.log("Average mosaic size:", averageSize);

      // Fetch posts stats
      const postsRef = collection(db, "social_media_posts");
      const totalPostsSnapshot = await getDocs(postsRef);
      const totalPosts = totalPostsSnapshot.size;
      console.log("Total posts:", totalPosts);

      // Get posts with images
      const postsWithImagesQuery = query(
        postsRef,
        where("agents.imageGenerator.storageUrl", "!=", null)
      );
      const postsWithImagesSnapshot = await getDocs(postsWithImagesQuery);
      const postsWithImages = postsWithImagesSnapshot.size;
      console.log("Posts with images:", postsWithImages);

      // Get posts used in mosaics
      const postsInMosaicsQuery = query(
        postsRef,
        where("mosaic_generated", "==", true)
      );
      const postsInMosaicsSnapshot = await getDocs(postsInMosaicsQuery);
      const postsInMosaics = postsInMosaicsSnapshot.size;
      console.log("Posts in mosaics:", postsInMosaics);

      // Calculate unprocessed comments
      const notInitialized =
        totalComments -
        (processedComments + failedComments + inProgressComments);
      console.log("Unprocessed comments:", notInitialized);

      setStats({
        comments: {
          total: totalComments,
          processed: processedComments,
          failed: failedComments,
          inProgress: inProgressComments,
          notInitialized,
        },
        mosaics: {
          total: totalMosaics,
          fromInstagram: instagramMosaics,
          fromComments: commentMosaics,
          averageSize,
        },
        posts: {
          total: totalPosts,
          withImages: postsWithImages,
          usedInMosaics: postsInMosaics,
        },
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  // Format timestamp from Firestore
  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return "Unknown date";

    if (timestamp.seconds) {
      return format(new Date(timestamp.seconds * 1000), "MMM d, yyyy h:mm a");
    }

    if (timestamp.toDate) {
      return format(timestamp.toDate(), "MMM d, yyyy h:mm a");
    }

    return format(new Date(timestamp), "MMM d, yyyy h:mm a");
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">QND Platform Overview</h1>
          <p className="text-muted-foreground">
            Comprehensive overview of platform statistics and performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          {loading ? (
            <Loader2 className="h-5 w-5 text-muted-foreground animate-spin" />
          ) : (
            <Clock className="h-5 w-5 text-muted-foreground" />
          )}
          <span className="text-sm text-muted-foreground">
            {loading
              ? "Loading data..."
              : `Last updated: ${new Date().toLocaleTimeString()}`}
          </span>
        </div>
      </div>

      {/* Dashboard Stats */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {loading ? (
          // Stat Card Skeletons
          Array(4)
            .fill(0)
            .map((_, index) => (
              <Card key={`stat-skeleton-${index}`} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-5 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-3/4 mt-2" />
                </CardContent>
              </Card>
            ))
        ) : (
          <>
            <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 hover:from-blue-500/20 hover:to-blue-600/10 transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Comments
                </CardTitle>
                <MessagesSquare className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.comments.total}</div>
                <p className="text-xs text-muted-foreground">
                  Comments in database
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500/10 to-green-600/5 hover:from-green-500/20 hover:to-green-600/10 transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Processed Comments
                </CardTitle>
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.comments.processed}
                </div>
                <Progress
                  value={
                    (stats.comments.processed / stats.comments.total) * 100
                  }
                  className="mt-2 bg-green-100 dark:bg-green-950"
                />
                <p className="text-xs text-muted-foreground mt-2">
                  {stats.comments.total
                    ? (
                        (stats.comments.processed / stats.comments.total) *
                        100
                      ).toFixed(1)
                    : "0"}
                  % complete
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-amber-500/10 to-amber-600/5 hover:from-amber-500/20 hover:to-amber-600/10 transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Mosaics
                </CardTitle>
                <Layers className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.mosaics.total}</div>
                <p className="text-xs text-muted-foreground">
                  Mosaics generated
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 hover:from-purple-500/20 hover:to-purple-600/10 transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Posts
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.posts.total}</div>
                <p className="text-xs text-muted-foreground">
                  Posts in database
                </p>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Detailed Stats and Examples */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Stats Details */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Quality Insights</CardTitle>
            <CardDescription>
              Performance and quality metrics for generated content
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-6">
                <div>
                  <Skeleton className="h-4 w-40 mb-3" />
                  <div className="grid grid-cols-3 gap-3">
                    {Array(3)
                      .fill(0)
                      .map((_, index) => (
                        <Skeleton
                          key={`quality-skeleton-${index}`}
                          className="h-24 w-full rounded-lg"
                        />
                      ))}
                  </div>
                </div>

                <div>
                  <Skeleton className="h-4 w-40 mb-3" />
                  <div className="space-y-3">
                    {Array(3)
                      .fill(0)
                      .map((_, index) => (
                        <div
                          key={`sentiment-skeleton-${index}`}
                          className="space-y-1"
                        >
                          <div className="flex justify-between">
                            <Skeleton className="h-3 w-20" />
                            <Skeleton className="h-3 w-12" />
                          </div>
                          <Skeleton className="h-2 w-full rounded-full" />
                        </div>
                      ))}
                  </div>
                </div>

                <div>
                  <Skeleton className="h-4 w-40 mb-3" />
                  <div className="flex flex-wrap gap-2">
                    {Array(6)
                      .fill(0)
                      .map((_, index) => (
                        <Skeleton
                          key={`badge-skeleton-${index}`}
                          className="h-6 w-24 rounded-full"
                        />
                      ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Quality Score Section */}
                <div>
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Star className="h-4 w-4 text-amber-500" />
                    Content Quality Scores
                  </h3>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="p-3 rounded-lg bg-gradient-to-br from-amber-500/10 to-amber-600/5 flex flex-col items-center justify-center">
                      <span className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                        92%
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Image Quality
                      </span>
                    </div>
                    <div className="p-3 rounded-lg bg-gradient-to-br from-purple-500/10 to-purple-600/5 flex flex-col items-center justify-center">
                      <span className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        85%
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Style Match
                      </span>
                    </div>
                    <div className="p-3 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/5 flex flex-col items-center justify-center">
                      <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        78%
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Text Relevance
                      </span>
                    </div>
                  </div>
                </div>

                {/* Sentiment Distribution */}
                <div>
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <ThumbsUp className="h-4 w-4 text-blue-500" />
                    Sentiment Distribution
                  </h3>
                  <div className="space-y-3">
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Positive</span>
                        <span className="text-xs font-medium">68%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: "68%" }}
                        ></div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Neutral</span>
                        <span className="text-xs font-medium">22%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: "22%" }}
                        ></div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Negative</span>
                        <span className="text-xs font-medium">10%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-red-500 h-2 rounded-full"
                          style={{ width: "10%" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Popular Styles */}
                <div>
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-500" />
                    Popular Styles
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-300">
                      <Sparkles className="h-3 w-3 mr-1" /> Photorealistic
                      <span className="ml-1 text-xs opacity-70">34%</span>
                    </Badge>
                    <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300">
                      <Sparkles className="h-3 w-3 mr-1" /> Cartoon
                      <span className="ml-1 text-xs opacity-70">22%</span>
                    </Badge>
                    <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-300">
                      <Sparkles className="h-3 w-3 mr-1" /> Pop Art
                      <span className="ml-1 text-xs opacity-70">16%</span>
                    </Badge>
                    <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300">
                      <Sparkles className="h-3 w-3 mr-1" /> Watercolor
                      <span className="ml-1 text-xs opacity-70">12%</span>
                    </Badge>
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300">
                      <Sparkles className="h-3 w-3 mr-1" /> Abstract
                      <span className="ml-1 text-xs opacity-70">8%</span>
                    </Badge>
                    <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300">
                      <Sparkles className="h-3 w-3 mr-1" /> Other
                      <span className="ml-1 text-xs opacity-70">8%</span>
                    </Badge>
                  </div>
                </div>

                {/* Processing Time */}
                <div>
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4 text-green-500" />
                    Generation Performance
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 rounded-lg border flex flex-col">
                      <span className="text-sm text-muted-foreground">
                        Avg. Generation Time
                      </span>
                      <span className="text-xl font-medium">4.2s</span>
                    </div>
                    <div className="p-3 rounded-lg border flex flex-col">
                      <span className="text-sm text-muted-foreground">
                        Success Rate
                      </span>
                      <span className="text-xl font-medium">96.5%</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Mosaics */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Latest Mosaics</CardTitle>
            <CardDescription>
              Recently generated mosaic artworks
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="grid grid-cols-2 gap-3">
                {Array(6)
                  .fill(0)
                  .map((_, index) => (
                    <Skeleton
                      key={`mosaic-skeleton-${index}`}
                      className="w-full aspect-video rounded-lg"
                    />
                  ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-3">
                {recentItems.mosaics.length > 0 ? (
                  recentItems.mosaics.map((mosaic: any) => (
                    <div
                      key={mosaic.id}
                      className="relative aspect-video rounded-lg overflow-hidden border group hover:border-primary transition-all"
                    >
                      {mosaic.url ? (
                        <Image
                          src={mosaic.url.split("?")[0]}
                          alt="Mosaic"
                          fill
                          className="object-cover transition-transform group-hover:scale-105"
                        />
                      ) : (
                        <div className="absolute inset-0 bg-muted flex items-center justify-center">
                          <Layers className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                      <div className="absolute bottom-2 left-2 right-2 text-white opacity-0 group-hover:opacity-100 transition-opacity">
                        <p className="text-xs truncate">
                          {formatTimestamp(mosaic.created_at)}
                        </p>
                        <Badge
                          variant="outline"
                          className="bg-black/50 text-white border-0 mt-1"
                        >
                          {mosaic.platform || "platform"}
                        </Badge>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 flex flex-col items-center justify-center py-8">
                    <Layers className="h-12 w-12 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No mosaics found</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Generated Images */}
      <Card>
        <CardHeader>
          <CardTitle>Latest Generated Images</CardTitle>
          <CardDescription>
            Recently processed comments with AI-generated imagery
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <Card
                    key={`image-skeleton-${index}`}
                    className="overflow-hidden"
                  >
                    <Skeleton className="w-full aspect-square" />
                    <div className="p-4 space-y-3">
                      <Skeleton className="h-10 w-full" />
                      <div className="pt-3 space-y-2">
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    </div>
                    <div className="px-4 py-2 border-t">
                      <Skeleton className="h-3 w-full" />
                    </div>
                  </Card>
                ))}
            </div>
          ) : (
            <ScrollArea className="h-a">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                {recentItems.comments.length > 0 ? (
                  recentItems.comments.map((comment: any) => (
                    <Card
                      key={comment.id}
                      className="overflow-hidden bg-gradient-to-br from-slate-950/10 to-slate-900/20 border-slate-800 hover:shadow-lg transition-all duration-300 group"
                    >
                      <div className="flex flex-col">
                        {/* Image Section with Hover Effects */}
                        <div className="relative w-full aspect-square overflow-hidden">
                          {comment.imageUrl ? (
                            <>
                              <Image
                                src={comment.imageUrl.split("?")[0]}
                                alt="Generated Image"
                                fill
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                priority
                                className="object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                              {/* Hover badges */}
                              <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                {comment.imageStyle && (
                                  <Badge
                                    variant="secondary"
                                    className="bg-black/50 text-white border-0"
                                  >
                                    {comment.imageStyle}
                                  </Badge>
                                )}
                                {comment.mood && (
                                  <Badge
                                    variant="secondary"
                                    className="bg-black/50 text-white border-0"
                                  >
                                    {comment.mood}
                                  </Badge>
                                )}
                              </div>
                            </>
                          ) : (
                            <div className="absolute inset-0 bg-muted flex items-center justify-center">
                              <ImageIcon className="h-12 w-12 text-muted-foreground" />
                            </div>
                          )}
                        </div>

                        {/* Content Section */}
                        <div className="p-4">
                          {/* Comment */}
                          <div className="mb-3">
                            <blockquote className="relative pl-3 border-l-2 border-muted-foreground/30 italic text-sm text-muted-foreground line-clamp-2">
                              {comment.postBody}
                            </blockquote>
                          </div>

                          {/* Metadata Grid */}
                          <div className="grid grid-cols-2 gap-y-2 text-xs border-t pt-3 border-dashed border-slate-800">
                            {comment.imageStyle && (
                              <div className="flex items-center gap-1.5">
                                <Star className="h-3.5 w-3.5 text-purple-500" />
                                <span className="text-muted-foreground">
                                  Style:
                                </span>
                                <span className="font-medium truncate">
                                  {comment.imageStyle}
                                </span>
                              </div>
                            )}

                            {comment.mood && (
                              <div className="flex items-center gap-1.5">
                                <Sparkles className="h-3.5 w-3.5 text-amber-500" />
                                <span className="text-muted-foreground">
                                  Mood:
                                </span>
                                <span className="font-medium truncate">
                                  {comment.mood}
                                </span>
                              </div>
                            )}

                            {comment.textAnalysis &&
                              comment.textAnalysis.sentiment && (
                                <div className="flex items-center gap-1.5">
                                  <ThumbsUp className="h-3.5 w-3.5 text-blue-500" />
                                  <span className="text-muted-foreground">
                                    Sentiment:
                                  </span>
                                  <span className="font-medium">
                                    {comment.textAnalysis.sentiment}
                                  </span>
                                </div>
                              )}

                            {comment.comment_like_count > 0 && (
                              <div className="flex items-center gap-1.5">
                                <Users className="h-3.5 w-3.5 text-green-500" />
                                <span className="text-muted-foreground">
                                  Likes:
                                </span>
                                <span className="font-medium">
                                  {comment.comment_like_count}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <CardFooter className="px-4 py-2bg-slate-900/50 text-xs text-muted-foreground border-t">
                        <div className="flex justify-between w-full">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              {
                                formatTimestamp(comment.created_at).split(
                                  " at"
                                )[0]
                              }
                            </span>
                          </div>
                          <div className="flex gap-1.5">
                            {comment.color_palette &&
                              Array.isArray(comment.color_palette) &&
                              comment.color_palette
                                .slice(0, 3)
                                .map((color: string, idx: number) => (
                                  <div
                                    key={idx}
                                    className="w-3 h-3 rounded-full border border-gray-700"
                                    style={{ backgroundColor: color }}
                                    title={color}
                                  />
                                ))}
                          </div>
                        </div>
                      </CardFooter>
                    </Card>
                  ))
                ) : (
                  <div className="col-span-3 flex flex-col items-center justify-center py-12">
                    <div className="bg-muted/20 p-6 rounded-full mb-4">
                      <ImageIcon className="h-12 w-12 text-muted-foreground" />
                    </div>
                    <p className="text-muted-foreground font-medium">
                      No processed comments found
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Images will appear here once comments are processed
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

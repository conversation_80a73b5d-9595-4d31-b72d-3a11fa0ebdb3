/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ReloadIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  DownloadIcon,
  BoxIcon,
} from "@radix-ui/react-icons";
import { InstagramPost } from "@/types";
import { db } from "@/lib/firebase";
import { deleteDoc, doc } from "firebase/firestore";
import { Progress } from "@/components/ui/progress";
import { Badge, BadgeProps } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { postStatus } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { Pagination } from "@/components/ui/pagination";
import { fetchPosts, PostWithDocId } from "@/lib/fetchers/posts";

const POSTS_PER_PAGE = 20;

const AGENT_STATUSES = [
  "text_analyzing",
  "sentiment_analyzing",
  "story_generating",
  "quality_checking",
  "culture_checking",
  "art_generating",
  "image_generating",
] as const;

// Add these types at the top
type AgentStatus = (typeof AGENT_STATUSES)[number];
type AgentKey = keyof InstagramPost["agents"];

const AGENT_KEYS: Record<AgentStatus, AgentKey> = {
  text_analyzing: "textAnalyzer",
  sentiment_analyzing: "sentimentAnalyzer",
  story_generating: "storyGenerator",
  quality_checking: "qualityChecker",
  culture_checking: "cultureChecker",
  art_generating: "artGenerator",
  image_generating: "imageGenerator",
} as const;

export default function PostsPage() {
  const { toast } = useToast();
  const router = useRouter();
  const [posts, setPosts] = useState<PostWithDocId[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [filters, setFilters] = useState({
    search: "",
    hashtag: "all",
    status: "all",
    dateRange: "",
  });

  const loadPosts = async (page: number) => {
    setLoading(true);
    try {
      const result = await fetchPosts({
        page,
        pageSize: POSTS_PER_PAGE,
        hashtag: filters.hashtag,
        status: filters.status,
        search: filters.search,
      });

      setPosts(result.posts);
      setTotalItems(result.totalItems);
      setTotalPages(result.totalPages);
      setCurrentPage(page);
    } catch (error) {
      console.error("Error fetching posts:", error);
      toast({
        title: "Error",
        description: "Failed to fetch posts.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    loadPosts(1);
  }, []);

  // Handle filter changes
  useEffect(() => {
    loadPosts(1);
  }, [filters.hashtag, filters.status]);

  useEffect(() => {
    const timer = setTimeout(() => {
      loadPosts(1);
    }, 500);
    return () => clearTimeout(timer);
  }, [filters.search]);

  const handleSelectAll = (checked: boolean) => {
    setSelectedPosts(checked ? posts.map((post) => post.docId) : []);
  };

  const handleExportSelected = async () => {
    try {
      const selectedData = posts.filter((post) =>
        selectedPosts.includes(post.docId)
      );
      const jsonStr = JSON.stringify(selectedData, null, 2);
      const blob = new Blob([jsonStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `selected_posts_${new Date().toISOString()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast({
        title: "Export Successful",
        description: `${selectedData.length} posts have been exported.`,
      });
    } catch (error) {
      console.error("Error exporting posts:", error);
      toast({
        title: "Export Failed",
        description: "Failed to export selected posts.",
        variant: "destructive",
      });
    }
  };

  const handleExportAll = async () => {
    try {
      const jsonStr = JSON.stringify(posts, null, 2);
      const blob = new Blob([jsonStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `all_posts_${new Date().toISOString()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast({
        title: "Export Successful",
        description: `${posts.length} posts have been exported.`,
      });
    } catch (error) {
      console.error("Error exporting posts:", error);
      toast({
        title: "Export Failed",
        description: "Failed to export posts.",
        variant: "destructive",
      });
    }
  };

  const handleRowClick = (docId: string) => {
    router.push(`/dashboard/posts/${docId}`);
  };

  const handleDeleteSelected = async () => {
    try {
      setLoading(true);

      // Delete posts from Firestore
      for (const postId of selectedPosts) {
        await deleteDoc(doc(db, "social_media_posts", postId));
      }

      // Clear selection
      setSelectedPosts([]);

      // Calculate if we need to go to the previous page
      const remainingPosts = posts.filter(
        (post) => !selectedPosts.includes(post.docId)
      );
      const shouldGoToPreviousPage =
        remainingPosts.length === 0 && currentPage > 1;

      // Refresh the current page or go to previous page if current page becomes empty
      await loadPosts(shouldGoToPreviousPage ? currentPage - 1 : currentPage);

      toast({
        title: "Success",
        description: `${selectedPosts.length} posts have been deleted.`,
      });
    } catch (error) {
      console.error("Error deleting posts:", error);
      toast({
        title: "Error",
        description: "Failed to delete selected posts.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h2 className="text-2xl font-semibold tracking-tight">
                Posts ({totalItems})
              </h2>
              <p className="text-sm text-muted-foreground">
                Manage and monitor your Meta posts
              </p>
            </div>
            <div className="flex items-center gap-2">
              {selectedPosts.length > 0 && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      <TrashIcon className="mr-2 h-4 w-4" />
                      Delete Selected
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will permanently delete {selectedPosts.length}{" "}
                        selected posts.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDeleteSelected}>
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex gap-4 flex-1">
                <div className="relative flex-1">
                  <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search posts..."
                    className="pl-8"
                    value={filters.search}
                    onChange={(e) =>
                      setFilters((prev) => ({
                        ...prev,
                        search: e.target.value,
                      }))
                    }
                  />
                </div>
                <Select
                  value={filters.status}
                  onValueChange={(value) =>
                    setFilters((prev) => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    {AGENT_STATUSES.map((status) => {
                      const statusInfo = postStatus(status);
                      return (
                        <SelectItem key={status} value={status}>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                statusInfo.badge as BadgeProps["variant"]
                              }
                              className={statusInfo.badge}
                            >
                              {statusInfo.text}
                            </Badge>
                          </div>
                        </SelectItem>
                      );
                    })}
                    <SelectItem value="failed">
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive">Failed</Badge>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={filters.hashtag}
                  onValueChange={(value) =>
                    setFilters((prev) => ({ ...prev, hashtag: value }))
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by hashtag" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All hashtags</SelectItem>
                    {Array.from(new Set(posts.map((post) => post.hashtag))).map(
                      (hashtag) => (
                        <SelectItem key={hashtag} value={hashtag}>
                          #{hashtag}
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExportSelected()}
                >
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  Export Selected
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExportAll()}
                >
                  <BoxIcon className="mr-2 h-4 w-4" />
                  Export All
                </Button>
              </div>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={
                      selectedPosts.length > 0 &&
                      selectedPosts.length === posts.length
                    }
                    onCheckedChange={(checked) => handleSelectAll(!!checked)}
                  />
                </TableHead>
                <TableHead>Post</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Updated</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    <ReloadIcon className="h-4 w-4 animate-spin mx-auto" />
                  </TableCell>
                </TableRow>
              ) : posts.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={7}
                    className="h-24 text-center text-muted-foreground"
                  >
                    No posts found
                  </TableCell>
                </TableRow>
              ) : (
                posts.map((post) => (
                  <TableRow
                    key={post.docId}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={(e) => {
                      // Prevent navigation when clicking checkbox
                      const target = e.target as HTMLElement;
                      if (
                        target.tagName !== "INPUT" &&
                        !target.closest(".checkbox-cell")
                      ) {
                        handleRowClick(post.docId);
                      }
                    }}
                  >
                    <TableCell className="checkbox-cell">
                      <Checkbox
                        checked={selectedPosts.includes(post.docId)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPosts([...selectedPosts, post.docId]);
                          } else {
                            setSelectedPosts(
                              selectedPosts.filter((id) => id !== post.docId)
                            );
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {post.caption.slice(0, 50)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          #{post.hashtag}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {post.user_username}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {post.user_full_name}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {post.status === "failed" ? (
                        <Badge variant="destructive">Failed</Badge>
                      ) : (
                        <Badge
                          variant={
                            postStatus(post.status)
                              .badge as BadgeProps["variant"]
                          }
                          className={`${
                            postStatus(post.status).badge
                          } text-nowrap text-xs`}
                        >
                          {postStatus(post.status).text}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Progress
                        value={
                          (AGENT_STATUSES.reduce((count, status) => {
                            const agentKey = AGENT_KEYS[status];
                            return (
                              count + (post.agents?.[agentKey] !== null ? 1 : 0)
                            );
                          }, 0) /
                            AGENT_STATUSES.length) *
                          100
                        }
                        className="h-2"
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {new Date(post.created_at).toLocaleDateString()}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {new Date(post.created_at).toLocaleTimeString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {new Date(post.updated_at).toLocaleDateString()}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {new Date(post.updated_at).toLocaleTimeString()}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          <div className="mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              pageSize={POSTS_PER_PAGE}
              onPageChange={loadPosts}
              isLoading={loading}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

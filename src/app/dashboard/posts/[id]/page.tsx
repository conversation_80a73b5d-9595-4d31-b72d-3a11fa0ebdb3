"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge, BadgeProps } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  ImageGeneratorResult,
  TextAnalyzerResult,
  ArtGeneratorResult,
  CultureCheckerResult,
  SentimentAnalyzerResult,
  StoryGeneratorResult,
  QualityCheckerResult,
  InstagramPost,
} from "@/types";
import { db } from "@/lib/firebase";
import { doc, getDoc } from "firebase/firestore";
import { ReloadIcon, TextIcon, DownloadIcon } from "@radix-ui/react-icons";
import { MessageSquare, Heart } from "lucide-react";
import { postStatus } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

interface MosaicGeneratorResult {
  url: string;
  prompt: string;
  processed_at?: FirestoreTimestamp;
}

interface PostWithDocId extends InstagramPost {
  docId: string;
  agents: {
    textAnalyzer?: TextAnalyzerResult;
    sentimentAnalyzer?: SentimentAnalyzerResult;
    storyGenerator?: StoryGeneratorResult;
    qualityChecker?: QualityCheckerResult;
    cultureChecker?: CultureCheckerResult;
    artGenerator?: ArtGeneratorResult;
    imageGenerator?: ImageGeneratorResult;
    mosaicGenerator?: MosaicGeneratorResult;
  };
}

export default function PostDetailsPage() {
  const params = useParams();
  const [post, setPost] = useState<PostWithDocId | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPost = async () => {
      if (!params.id) return;

      try {
        const docRef = doc(db, "social_media_posts", params.id as string);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          setPost({ ...(docSnap.data() as InstagramPost), docId: docSnap.id });
        }
      } catch (error) {
        console.error("Error fetching post:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <ReloadIcon className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!post) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-muted-foreground">Post not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.history.back()}
          className="gap-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="lucide lucide-chevron-left"
          >
            <path d="m15 18-6-6 6-6" />
          </svg>
          Back to Posts
        </Button>
      </div>

      {/* Post Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-12 w-12">
                {post.user_profile_pic_url ? (
                  <div className="relative w-full h-full">
                    <Image
                      src={`https://wsrv.nl/?url=${encodeURIComponent(
                        post.user_profile_pic_url
                      )}`}
                      alt={post.user_username}
                      fill
                      className="object-cover rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                        // Show fallback immediately on error
                        const fallback =
                          target.parentElement?.parentElement?.querySelector(
                            '[role="presentation"]'
                          ) as HTMLElement;
                        if (fallback) {
                          fallback.style.display = "flex";
                        }
                      }}
                    />
                  </div>
                ) : null}
                <AvatarFallback className="bg-primary text-primary-foreground uppercase">
                  {post.user_username.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-2xl font-semibold">
                  @{post.user_username}
                </h2>
                <p className="text-sm text-muted-foreground">
                  {post.user_full_name}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge
                variant={postStatus(post.status).badge as BadgeProps["variant"]}
                className={postStatus(post.status).badge}
              >
                {postStatus(post.status).text}
              </Badge>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Heart className="h-4 w-4 text-red-500" />
                  <span>{post.like_count}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-4 w-4 text-blue-500" />
                  <span>{post.comment_count}</span>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content */}
      <Tabs defaultValue="details" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Post Details & Analysis</TabsTrigger>
          <TabsTrigger value="analytics">Analytics & Stats</TabsTrigger>
          <TabsTrigger value="agents">Agent Results</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          {/* Post Information */}
          <Card>
            <CardHeader>
              <CardTitle>Post Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Caption</h3>
                  <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                    {post.caption}
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Hashtags</h3>
                  <div className="flex flex-wrap gap-2">
                    {post.hashtags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-6">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Engagement</h3>
                  <div className="flex gap-4">
                    <div className="flex items-center gap-2">
                      <Heart className="h-4 w-4 text-red-500" />
                      <span className="text-sm">{post.like_count}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">{post.comment_count}</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Created</h3>
                  <p className="text-sm text-muted-foreground">
                    {new Date(post.created_at).toLocaleString()}
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Platform & Type</h3>
                  <div className="flex gap-2">
                    <Badge variant="outline">{post.platform}</Badge>
                    <Badge variant="outline">{post.content_type}</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Generated Content Section */}
          <div className="grid grid-cols-2 gap-4">
            {/* Generated Image Section */}
            {post.agents.imageGenerator && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Generated Image</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative aspect-video rounded-md overflow-hidden bg-background border">
                    <Image
                      src={post.agents.imageGenerator.storageUrl}
                      alt="Generated image"
                      className="object-contain"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium flex items-center gap-2">
                      <TextIcon className="h-4 w-4" />
                      Image Prompt
                    </h3>
                    <div className="max-h-[200px] overflow-y-auto rounded-md border p-4">
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                        {post.agents.imageGenerator.caption ||
                          "No prompt available"}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        window.open(
                          post?.agents?.imageGenerator?.storageUrl,
                          "_blank"
                        )
                      }
                    >
                      <DownloadIcon className="h-4 w-4 mr-2" />
                      Download Image
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Generated Mosaic Section */}
            {post.agents.mosaicGenerator && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Generated Mosaic</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative aspect-video rounded-md overflow-hidden bg-background border">
                    <Image
                      src={post.agents.mosaicGenerator.url}
                      alt="Generated mosaic"
                      className="object-contain"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium flex items-center gap-2">
                      <TextIcon className="h-4 w-4" />
                      Mosaic Prompt
                    </h3>
                    <div className="max-h-[200px] overflow-y-auto rounded-md border p-4">
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                        {post.agents.mosaicGenerator.prompt ||
                          "No prompt available"}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        window.open(post.agents.mosaicGenerator?.url, "_blank")
                      }
                    >
                      <DownloadIcon className="h-4 w-4 mr-2" />
                      Download Mosaic
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Sentiment Analysis */}
            {post.agents.sentimentAnalyzer && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Sentiment Analysis</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Sentiment Score</span>
                    <Badge
                      variant={
                        post.agents.sentimentAnalyzer.sentiment_score > 0.5
                          ? "default"
                          : "destructive"
                      }
                    >
                      {post.agents.sentimentAnalyzer.sentiment_score.toFixed(2)}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Overall Sentiment
                    </span>
                    <Badge>{post.agents.sentimentAnalyzer.sentiment}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Tone</span>
                    <Badge variant="outline">
                      {post.agents.sentimentAnalyzer.tone}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quality Check */}
            {post.agents.qualityChecker && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quality Assessment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overall Score</span>
                    <Badge
                      variant={
                        post.agents.qualityChecker.overall_score > 0.7
                          ? "default"
                          : "secondary"
                      }
                    >
                      {post.agents.qualityChecker.overall_score.toFixed(2)}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Key Strengths</h4>
                    <ul className="list-disc list-inside text-sm text-muted-foreground">
                      {post.agents.qualityChecker.key_strengths.map(
                        (strength: string, i: number) => (
                          <li key={i}>{strength}</li>
                        )
                      )}
                    </ul>
                  </div>
                  {post.agents.qualityChecker.areas_of_concern.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Areas of Concern</h4>
                      <ul className="list-disc list-inside text-sm text-muted-foreground">
                        {post.agents.qualityChecker.areas_of_concern.map(
                          (concern: string, i: number) => (
                            <li key={i}>{concern}</li>
                          )
                        )}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Culture Check */}
            {post.agents.cultureChecker && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Cultural Alignment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Alignment Score</span>
                    <Badge
                      variant={
                        post.agents.cultureChecker.alignment_score > 0.7
                          ? "default"
                          : "secondary"
                      }
                    >
                      {post.agents.cultureChecker.alignment_score.toFixed(2)}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Cultural Elements</h4>
                    <div className="flex flex-wrap gap-2">
                      {post.agents.cultureChecker.cultural_elements.map(
                        (element: string, i: number) => (
                          <Badge key={i} variant="outline">
                            {element}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Brand Voice Match</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Score
                      </span>
                      <Badge
                        variant={
                          post.agents.cultureChecker.brand_voice_match.score >
                          0.7
                            ? "default"
                            : "secondary"
                        }
                      >
                        {Math.min(
                          Math.max(
                            Math.round(
                              post.agents.cultureChecker.brand_voice_match
                                .score * 100
                            ),
                            0
                          ),
                          100
                        )}
                        %
                      </Badge>
                    </div>
                    <ul className="list-disc list-inside text-sm text-muted-foreground">
                      {post.agents.cultureChecker.brand_voice_match.observations.map(
                        (obs: string, i: number) => (
                          <li key={i}>{obs}</li>
                        )
                      )}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Engagement Stats */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col gap-4">
                  <div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">Likes</p>
                      <div className="flex items-center gap-2">
                        <Heart className="h-4 w-4 text-red-500" />
                        <span className="text-2xl font-bold">
                          {post.like_count}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">Comments</p>
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-blue-500" />
                        <span className="text-2xl font-bold">
                          {post.comment_count}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="pt-2">
                    <p className="text-sm font-medium mb-1">Engagement Rate</p>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className="h-full bg-green-500 rounded-full"
                          style={{
                            width: `${Math.min(
                              ((post.like_count + post.comment_count) / 1000) *
                                100,
                              100
                            )}%`,
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium">
                        {(
                          ((post.like_count + post.comment_count) / 1000) *
                          100
                        ).toFixed(1)}
                        %
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Sentiment Stats */}
            {post.agents.sentimentAnalyzer && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Sentiment Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium">Overall Sentiment</p>
                        <Badge
                          variant={
                            post.agents.sentimentAnalyzer.sentiment ===
                            "positive"
                              ? "default"
                              : post.agents.sentimentAnalyzer.sentiment ===
                                "negative"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {post.agents.sentimentAnalyzer.sentiment}
                        </Badge>
                      </div>
                      {post.agents.sentimentAnalyzer.sentiment_score > 0 && (
                        <div className="flex items-center gap-2">
                          <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full ${
                                post.agents.sentimentAnalyzer.sentiment_score >
                                0.6
                                  ? "bg-green-500"
                                  : post.agents.sentimentAnalyzer
                                      .sentiment_score > 0.3
                                  ? "bg-yellow-500"
                                  : "bg-red-500"
                              }`}
                              style={{
                                width: `${Math.min(
                                  Math.max(
                                    post.agents.sentimentAnalyzer
                                      .sentiment_score * 100,
                                    0
                                  ),
                                  100
                                )}%`,
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium">
                            {Math.min(
                              Math.max(
                                Math.round(
                                  post.agents.sentimentAnalyzer
                                    .sentiment_score * 100
                                ),
                                0
                              ),
                              100
                            )}
                            %
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium mb-1">Emotional Tone</p>
                      <Badge variant="outline" className="text-sm">
                        {post.agents.sentimentAnalyzer.tone}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quality Stats */}
            {post.agents.qualityChecker && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Quality Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium">Overall Quality</p>
                        <span className="text-2xl font-bold">
                          {Math.min(
                            Math.max(
                              Math.round(
                                post.agents.qualityChecker.overall_score * 100
                              ),
                              0
                            ),
                            100
                          )}
                          %
                        </span>
                      </div>
                      {post.agents.qualityChecker.overall_score > 0 && (
                        <div className="flex items-center gap-2">
                          <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full ${
                                post.agents.qualityChecker.overall_score > 0.7
                                  ? "bg-green-500"
                                  : post.agents.qualityChecker.overall_score >
                                    0.4
                                  ? "bg-yellow-500"
                                  : "bg-red-500"
                              }`}
                              style={{
                                width: `${Math.min(
                                  Math.max(
                                    post.agents.qualityChecker.overall_score *
                                      100,
                                    0
                                  ),
                                  100
                                )}%`,
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium mb-1">Content</p>
                        <Badge
                          variant={
                            post.agents.qualityChecker
                              .content_appropriateness === "appropriate"
                              ? "default"
                              : "destructive"
                          }
                        >
                          {post.agents.qualityChecker.content_appropriateness}
                        </Badge>
                      </div>
                      <div>
                        <p className="text-sm font-medium mb-1">
                          Brand Alignment
                        </p>
                        <Badge
                          variant={
                            post.agents.qualityChecker.brand_alignment ===
                            "aligned"
                              ? "default"
                              : "destructive"
                          }
                        >
                          {post.agents.qualityChecker.brand_alignment}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Culture and Brand Stats */}
          {post.agents.cultureChecker && (
            <Card>
              <CardHeader>
                <CardTitle>Cultural Elements & Brand Voice</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <h3 className="text-sm font-medium mb-3">
                      Cultural Elements
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {post.agents.cultureChecker.cultural_elements.map(
                        (element: string, i: number) => (
                          <Badge key={i} variant="secondary">
                            {element}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-medium">Brand Voice Match</h3>
                      <span className="text-2xl font-bold">
                        {Math.min(
                          Math.max(
                            Math.round(
                              post.agents.cultureChecker.brand_voice_match
                                .score * 100
                            ),
                            0
                          ),
                          100
                        )}
                        %
                      </span>
                    </div>
                    <div className="space-y-2">
                      {post.agents.cultureChecker.brand_voice_match.observations.map(
                        (obs: string, i: number) => (
                          <div
                            key={i}
                            className="flex items-start gap-2 text-sm"
                          >
                            <span className="text-muted-foreground">•</span>
                            <span>{obs}</span>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Agent Processing Status */}
          <Card>
            <CardHeader>
              <CardTitle>Agent Processing Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                {Object.entries(post.agents).map(([agentName, agent]) => (
                  <div key={agentName} className="flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium capitalize">
                        {agentName.replace(/([A-Z])/g, " $1").trim()}
                      </p>
                      <Badge
                        variant={agent?.processed_at ? "default" : "secondary"}
                      >
                        {agent?.processed_at ? "Completed" : "Pending"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <ScrollArea className="h-[600px] space-y-4 pr-4">
            {/* Text Analyzer */}
            {post.agents.textAnalyzer && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Text Analysis</span>
                    <Badge variant="outline">
                      {post.agents.textAnalyzer.processed_at
                        ? new Date(
                            post.agents.textAnalyzer.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.textAnalyzer, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Sentiment Analyzer */}
            {post.agents.sentimentAnalyzer && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Sentiment Analysis</span>
                    <Badge variant="outline">
                      {post.agents.sentimentAnalyzer.processed_at
                        ? new Date(
                            post.agents.sentimentAnalyzer.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.sentimentAnalyzer, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Story Generator */}
            {post.agents.storyGenerator && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Generated Story</span>
                    <Badge variant="outline">
                      {post.agents.storyGenerator.processed_at
                        ? new Date(
                            post.agents.storyGenerator.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.storyGenerator, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Quality Checker */}
            {post.agents.qualityChecker && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Quality Check</span>
                    <Badge variant="outline">
                      {post.agents.qualityChecker.processed_at
                        ? new Date(
                            post.agents.qualityChecker.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.qualityChecker, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Culture Checker */}
            {post.agents.cultureChecker && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Culture Check</span>
                    <Badge variant="outline">
                      {post.agents.cultureChecker.processed_at
                        ? new Date(
                            post.agents.cultureChecker.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.cultureChecker, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Art Generator */}
            {post.agents.artGenerator && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Art Generation</span>
                    <Badge variant="outline">
                      {post.agents.artGenerator.processed_at
                        ? new Date(
                            post.agents.artGenerator.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.artGenerator, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Image Generator */}
            {post.agents.imageGenerator && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Image Generation</span>
                    <Badge variant="outline">
                      {post.agents.imageGenerator.processed_at
                        ? new Date(
                            post.agents.imageGenerator.processed_at
                          ).toLocaleString()
                        : "Processing..."}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(post.agents.imageGenerator, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}

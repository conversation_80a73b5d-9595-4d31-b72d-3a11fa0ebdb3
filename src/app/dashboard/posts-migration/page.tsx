"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  getDocs,
  addDoc,
  updateDoc,
  doc,
  where,
  Timestamp,
} from "firebase/firestore";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
  ReloadIcon,
  CheckIcon,
  Magnifying<PERSON>lassIcon,
  Cross2Icon,
} from "@radix-ui/react-icons";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Image from "next/image";

interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

interface SourcePost {
  agents_ready: boolean;
  authorImage: string;
  authorName: string;
  created_at: FirestoreTimestamp;
  isComment: boolean;
  is_created_by_media_owner: boolean;
  metrics?: {
    likes: number;
    replies: number;
    retweets: number;
    parentPostId?: string;
    platform?: string;
    postBody?: string;
    post_id?: string;
    type?: string;
    url?: string;
  };
  parentPostId?: string;
  platform: string;
  postBody?: string;
  post_id: string;
  type: string;
  url: string;
  username: string;
  postId?: string;
}

interface MigrationLog {
  id: string;
  sourceId: string;
  targetId: string;
  status: "success" | "error";
  message: string;
  timestamp: Date;
}

export default function PostsMigrationPage() {
  const [posts, setPosts] = useState<(SourcePost & { id: string })[]>([]);
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [logs, setLogs] = useState<MigrationLog[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    migrated: 0,
    failed: 0,
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    pageSize: 20,
    totalItems: 0,
    hasMore: true,
  });
  const [previewPost, setPreviewPost] = useState<
    (SourcePost & { id: string }) | null
  >(null);
  const observer = useRef<IntersectionObserver | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Reset posts and pagination when search query changes
    setPosts([]);
    setPagination((prev) => ({
      ...prev,
      currentPage: 1,
      hasMore: true,
    }));
    fetchPosts(1, pagination.pageSize);
    fetchStats();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  // Function to fetch posts
  const fetchPosts = async (page: number, pageSize: number, append = false) => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      // Use the server API to fetch posts
      const response = await fetch(
        `/api/posts-migration?page=${page}&pageSize=${pageSize}&search=${searchQuery}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch posts");
      }

      const data = await response.json();

      // If append is true, add new posts to existing ones
      if (append) {
        setPosts((prev) => [...prev, ...data.posts]);
      } else {
        setPosts(data.posts);
      }

      setPagination((prev) => ({
        ...prev,
        totalItems: data.pagination.totalItems,
        totalPages: data.pagination.totalPages,
        currentPage: data.pagination.currentPage,
        hasMore: data.pagination.currentPage < data.pagination.totalPages,
      }));
    } catch (error) {
      console.error("Error fetching posts:", error);
      toast({
        title: "Error",
        description: "Failed to fetch posts",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load more posts when user scrolls to the bottom
  const loadMorePosts = useCallback(() => {
    if (pagination.hasMore && !isLoading) {
      const nextPage = pagination.currentPage + 1;
      fetchPosts(nextPage, pagination.pageSize, true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    pagination.hasMore,
    pagination.currentPage,
    pagination.pageSize,
    isLoading,
  ]);

  // Set up intersection observer for infinite scrolling
  const lastPostRef = useCallback(
    (node: HTMLElement | null) => {
      if (isLoading) return;

      if (observer.current) {
        observer.current.disconnect();
      }

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && pagination.hasMore) {
          loadMorePosts();
        }
      });

      if (node) {
        observer.current.observe(node);
      }
    },
    [isLoading, pagination.hasMore, loadMorePosts]
  );

  const fetchStats = async () => {
    try {
      // Count total posts in source collection
      const sourceRef = collection(db, "posts");
      const sourceSnapshot = await getDocs(sourceRef);

      // Count migrated posts in target collection
      const targetRef = collection(db, "social_media_posts");
      const targetQuery = query(
        targetRef,
        where("migrated_from_legacy", "==", true)
      );
      const targetSnapshot = await getDocs(targetQuery);

      // Count failed migrations
      const failedCount = logs.filter((log) => log.status === "error").length;

      setStats({
        total: sourceSnapshot.size,
        migrated: targetSnapshot.size,
        failed: failedCount,
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(posts.map((post) => post.id));
    } else {
      setSelectedPosts([]);
    }
  };

  const handleSelectPost = (postId: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts((prev) => [...prev, postId]);
    } else {
      setSelectedPosts((prev) => prev.filter((id) => id !== postId));
    }
  };

  const addLog = (
    sourceId: string,
    targetId: string,
    status: "success" | "error",
    message: string
  ) => {
    const log = {
      id: `${Date.now()}-${Math.random()}`,
      sourceId,
      targetId,
      status,
      message,
      timestamp: new Date(),
    };
    setLogs((prev) => [log, ...prev]);
  };

  const migratePost = async (post: SourcePost & { id: string }) => {
    try {
      // Extract hashtags from the post body
      const postContent = post.postBody || post.metrics?.postBody || "";
      const hashtagRegex = /#(\w+)/g;
      const matches = postContent.match(hashtagRegex) || [];
      const hashtags = matches.map((tag) => tag.substring(1));
      const mainHashtag = hashtags.length > 0 ? hashtags[0] : "";

      // Create the target post object
      const targetPost = {
        id: post.post_id,
        caption: postContent, // Use the correct post content
        batch: 1,
        platform: post.platform || post.metrics?.platform || "unknown",
        status: "pending",
        timestamp: new Date(post.created_at.seconds * 1000).toISOString(),
        hashtag: mainHashtag,
        content_type: post.type || post.metrics?.type || "unknown",
        created_at: new Date(post.created_at.seconds * 1000).toISOString(),
        updated_at: new Date().toISOString(),
        hashtags: hashtags,
        user_full_name: post.authorName,
        user_username: post.username,
        user_profile_pic_url: post.authorImage,
        user_id: post.username, // Using username as user_id if not available
        comment_count: post.metrics?.replies || 0,
        like_count: post.metrics?.likes || 0,
        agents: {},
        migrated_from_legacy: true,
        source_post_id: post.id,
        original_url: post.url || post.metrics?.url || "",
        parent_post_id:
          post.parentPostId ||
          post.metrics?.parentPostId ||
          post.postId ||
          null,
        is_comment: post.isComment || false,
      };

      // Log the post content for verification
      console.log(`Migrating post ${post.id} with content: ${postContent}`);

      // Add the post to social_media_posts collection
      const docRef = await addDoc(
        collection(db, "social_media_posts"),
        targetPost
      );

      // Update the source post to mark it as migrated
      await updateDoc(doc(db, "posts", post.id), {
        migrated: true,
        migrated_at: Timestamp.now(),
        migrated_to: docRef.id,
      });

      addLog(
        post.id,
        docRef.id,
        "success",
        `Successfully migrated post ${post.post_id}`
      );
      return true;
    } catch (error) {
      console.error(`Error migrating post ${post.id}:`, error);
      addLog(
        post.id,
        "",
        "error",
        `Failed to migrate post ${post.id}: ${error}`
      );
      return false;
    }
  };

  const handleMigrate = async () => {
    if (selectedPosts.length === 0) {
      toast({
        title: "No posts selected",
        description: "Please select at least one post to migrate",
        variant: "destructive",
      });
      return;
    }

    setIsMigrating(true);
    setProgress(0);

    const postsToMigrate = posts.filter((post) =>
      selectedPosts.includes(post.id)
    );
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < postsToMigrate.length; i++) {
      const post = postsToMigrate[i];
      const success = await migratePost(post);

      if (success) {
        successCount++;
      } else {
        failCount++;
      }

      setProgress(Math.round(((i + 1) / postsToMigrate.length) * 100));
    }

    // Refresh posts and stats
    fetchPosts(pagination.currentPage, pagination.pageSize);
    fetchStats();

    toast({
      title: "Migration completed",
      description: `Successfully migrated ${successCount} posts. Failed: ${failCount}`,
      variant: successCount > 0 ? "default" : "destructive",
    });

    setIsMigrating(false);
    setSelectedPosts([]);
  };

  const handlePreviewPost = (post: SourcePost & { id: string }) => {
    setPreviewPost(post);
  };

  const filteredPosts = posts.filter((post) => {
    const postContent = post.postBody || post.metrics?.postBody || "";
    const username = post.username || "";
    const authorName = post.authorName || "";

    return (
      postContent.toLowerCase().includes(searchQuery.toLowerCase()) ||
      username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      authorName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Posts Migration</CardTitle>
          <CardDescription>
            Migrate posts from legacy collection to social_media_posts for agent
            processing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => {
                  setPosts([]);
                  setPagination((prev) => ({
                    ...prev,
                    currentPage: 1,
                    hasMore: true,
                  }));
                  fetchPosts(1, pagination.pageSize);
                }}
                variant="outline"
                disabled={isLoading}
              >
                {isLoading ? (
                  <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <ReloadIcon className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search posts..."
                  className="pl-8 w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button
                onClick={handleMigrate}
                disabled={isMigrating || selectedPosts.length === 0}
              >
                {isMigrating ? (
                  <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckIcon className="mr-2 h-4 w-4" />
                )}
                Migrate Selected
              </Button>
            </div>
          </div>

          {isMigrating && (
            <div className="mb-6">
              <Progress value={progress} className="h-2" />
              <p className="text-sm text-muted-foreground mt-2">
                Migrating {selectedPosts.length} posts... {progress}% complete
              </p>
            </div>
          )}

          <div className="grid grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Posts
                  </p>
                  <h3 className="text-3xl font-bold">{stats.total}</h3>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">
                    Migrated
                  </p>
                  <h3 className="text-3xl font-bold text-green-500">
                    {stats.migrated}
                  </h3>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">
                    Failed
                  </p>
                  <h3 className="text-3xl font-bold text-red-500">
                    {stats.failed}
                  </h3>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="posts">
            <TabsList>
              <TabsTrigger value="posts">Posts</TabsTrigger>
              <TabsTrigger value="logs">Migration Logs</TabsTrigger>
            </TabsList>
            <TabsContent value="posts">
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={
                            selectedPosts.length > 0 &&
                            selectedPosts.length === filteredPosts.length
                          }
                          onCheckedChange={(checked) =>
                            handleSelectAll(!!checked)
                          }
                        />
                      </TableHead>
                      <TableHead>Content</TableHead>
                      <TableHead>Author</TableHead>
                      <TableHead>Platform</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-12">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPosts.length > 0 ? (
                      filteredPosts.map((post, index) => (
                        <TableRow
                          key={post.id}
                          ref={
                            index === filteredPosts.length - 1
                              ? lastPostRef
                              : null
                          }
                        >
                          <TableCell>
                            <Checkbox
                              checked={selectedPosts.includes(post.id)}
                              onCheckedChange={(checked) =>
                                handleSelectPost(post.id, !!checked)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <div className="max-w-md truncate">
                              {post.postBody ||
                                post.metrics?.postBody ||
                                "No content"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {post.username}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {post.authorName}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{post.platform}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">{post.type}</Badge>
                          </TableCell>
                          <TableCell>
                            {new Date(
                              post.created_at.seconds * 1000
                            ).toLocaleString()}
                          </TableCell>
                          <TableCell>
                            {post.agents_ready ? (
                              <Badge
                                variant="success"
                                className="bg-green-500 text-white"
                              >
                                Ready
                              </Badge>
                            ) : (
                              <Badge variant="outline">Pending</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePreviewPost(post)}
                            >
                              <MagnifyingGlassIcon className="h-4 w-4" />
                              <span className="sr-only">Preview</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-6">
                          {isLoading
                            ? "Loading posts..."
                            : searchQuery
                            ? "No posts match your search"
                            : "No posts found"}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>

                {isLoading && (
                  <div className="flex justify-center items-center py-4 border-t">
                    <ReloadIcon className="h-6 w-6 animate-spin text-muted-foreground" />
                    <span className="ml-2 text-sm text-muted-foreground">
                      Loading more posts...
                    </span>
                  </div>
                )}

                {!isLoading &&
                  filteredPosts.length > 0 &&
                  !pagination.hasMore && (
                    <div className="text-center py-4 border-t text-sm text-muted-foreground">
                      No more posts to load
                    </div>
                  )}
              </div>
            </TabsContent>
            <TabsContent value="logs">
              <ScrollArea className="h-[400px] border rounded-md">
                <div className="p-4 space-y-4">
                  {logs.length > 0 ? (
                    logs.map((log) => (
                      <div
                        key={log.id}
                        className={`p-3 rounded-md border ${
                          log.status === "success"
                            ? "bg-green-50 border-green-200"
                            : "bg-red-50 border-red-200"
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {log.status === "success" ? (
                              <CheckIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <Cross2Icon className="h-4 w-4 text-red-500" />
                            )}
                            <span className="font-medium">
                              {log.status === "success" ? "Success" : "Error"}
                            </span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="mt-1 text-sm">{log.message}</p>
                        <div className="mt-2 text-xs text-muted-foreground">
                          <span>Source ID: {log.sourceId}</span>
                          {log.targetId && (
                            <span className="ml-4">
                              Target ID: {log.targetId}
                            </span>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      No migration logs yet
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Post Preview Dialog */}
      <Dialog
        open={!!previewPost}
        onOpenChange={(open) => !open && setPreviewPost(null)}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Post Preview</DialogTitle>
            <DialogDescription>
              Review post details before migration
            </DialogDescription>
          </DialogHeader>

          {previewPost && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">Source Details</h3>
                  <div className="rounded-md border p-4 space-y-2">
                    <div>
                      <span className="text-sm font-medium">ID:</span>
                      <p className="text-sm text-muted-foreground">
                        {previewPost.id}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Post ID:</span>
                      <p className="text-sm text-muted-foreground">
                        {previewPost.post_id}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Type:</span>
                      <p className="text-sm text-muted-foreground">
                        {previewPost.type}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Platform:</span>
                      <p className="text-sm text-muted-foreground">
                        {previewPost.platform}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Created:</span>
                      <p className="text-sm text-muted-foreground">
                        {new Date(
                          previewPost.created_at.seconds * 1000
                        ).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-1">User Information</h3>
                  <div className="rounded-md border p-4 space-y-2">
                    <div className="flex items-center gap-3">
                      {previewPost.authorImage && (
                        <Image
                          src={previewPost.authorImage}
                          alt={previewPost.authorName}
                          className="h-10 w-10 rounded-full"
                          width={40}
                          height={40}
                        />
                      )}
                      <div>
                        <p className="text-sm font-medium">
                          {previewPost.authorName}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          @{previewPost.username}
                        </p>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Metrics:</span>
                      <div className="flex gap-3 mt-1">
                        <Badge variant="outline">
                          Likes: {previewPost.metrics?.likes || 0}
                        </Badge>
                        <Badge variant="outline">
                          Replies: {previewPost.metrics?.replies || 0}
                        </Badge>
                        <Badge variant="outline">
                          Retweets: {previewPost.metrics?.retweets || 0}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Post Content</h3>
                <div className="rounded-md border p-4">
                  <p className="whitespace-pre-wrap">
                    {previewPost.postBody ||
                      previewPost.metrics?.postBody ||
                      "No content"}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Migration Preview</h3>
                <div className="rounded-md border p-4 space-y-2 bg-muted/20">
                  {/* Extract hashtags from the post body */}
                  {(() => {
                    const postContent =
                      previewPost.postBody ||
                      previewPost.metrics?.postBody ||
                      "";
                    const hashtagRegex = /#(\w+)/g;
                    const matches = postContent.match(hashtagRegex) || [];
                    const hashtags = matches.map((tag) => tag.substring(1));
                    const mainHashtag = hashtags.length > 0 ? hashtags[0] : "";

                    return (
                      <>
                        <div>
                          <span className="text-sm font-medium">Caption:</span>
                          <p className="text-sm text-muted-foreground">
                            {postContent}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm font-medium">
                            Main Hashtag:
                          </span>
                          <p className="text-sm text-muted-foreground">
                            {mainHashtag ? `#${mainHashtag}` : "None"}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm font-medium">
                            All Hashtags:
                          </span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {hashtags.length > 0 ? (
                              hashtags.map((tag, index) => (
                                <Badge key={index} variant="secondary">
                                  #{tag}
                                </Badge>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground">
                                None
                              </p>
                            )}
                          </div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setPreviewPost(null)}>
              Close
            </Button>
            {previewPost && (
              <Button
                onClick={async () => {
                  if (previewPost) {
                    const success = await migratePost(previewPost);
                    if (success) {
                      toast({
                        title: "Success",
                        description: `Post ${previewPost.post_id} migrated successfully`,
                      });
                      fetchPosts(pagination.currentPage, pagination.pageSize);
                      fetchStats();
                    } else {
                      toast({
                        title: "Error",
                        description: `Failed to migrate post ${previewPost.post_id}`,
                        variant: "destructive",
                      });
                    }
                    setPreviewPost(null);
                  }
                }}
              >
                Migrate This Post
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

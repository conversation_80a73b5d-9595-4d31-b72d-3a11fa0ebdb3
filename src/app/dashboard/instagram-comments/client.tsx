"use client";

import { useState, useEffect } from "react";
import { db } from "@/lib/firebase";
import { collection, getDocs, query, orderBy, limit } from "firebase/firestore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Search, Download } from "lucide-react";
import { ReloadIcon, UpdateIcon } from "@radix-ui/react-icons";

export default function InstagramCommentsClient() {
  const [comments, setComments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [limitCount, setLimitCount] = useState(100);
  const { toast } = useToast();
  const [isNormalizing, setIsNormalizing] = useState(false);

  // Fetch comments from Firestore
  const fetchComments = async () => {
    try {
      setLoading(true);
      const commentsRef = collection(db, "instagram_comments");
      const q = query(
        commentsRef,
        orderBy("created_at", "desc"),
        limit(limitCount)
      );
      const snapshot = await getDocs(q);

      const fetchedComments = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      setComments(fetchedComments);
      toast({
        title: "Data Loaded",
        description: `Successfully loaded ${fetchedComments.length} comments`,
      });
    } catch (error) {
      console.error("Error fetching comments:", error);
      toast({
        title: "Error",
        description: "Failed to fetch comments",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter comments based on search term
  const filteredComments = comments.filter((comment) =>
    JSON.stringify(comment).toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Download JSON file
  const downloadJson = () => {
    const dataStr = JSON.stringify(filteredComments, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "instagram-comments.json";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const normalizeComments = async () => {
    try {
      setIsNormalizing(true);
      const response = await fetch("/api/comments/normalize", {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to normalize comments");
      }

      const data = await response.json();
      toast({
        title: "Success",
        description: `Normalized ${data.processed} comments (${data.skipped} already normalized)`,
      });
    } catch (error) {
      console.error("Error normalizing comments:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to normalize comments",
      });
    } finally {
      setIsNormalizing(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchComments();
  }, [limitCount]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Instagram Comments Management</CardTitle>
            <Button
              onClick={normalizeComments}
              disabled={isNormalizing}
              className="gap-2"
            >
              {isNormalizing ? (
                <ReloadIcon className="h-4 w-4 animate-spin" />
              ) : (
                <UpdateIcon className="h-4 w-4" />
              )}
              {isNormalizing ? "Normalizing..." : "Normalize Comments"}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Use the normalize function to ensure all comments have the required
            fields for image generation.
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle>Instagram Comments JSON View</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                View and search through Instagram comments collection data
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  value={limitCount}
                  onChange={(e) => setLimitCount(Number(e.target.value))}
                  className="w-24"
                  min={1}
                  max={1000}
                />
                <Button
                  onClick={fetchComments}
                  disabled={loading}
                  variant="secondary"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    "Refresh"
                  )}
                </Button>
              </div>
              <Button onClick={downloadJson} disabled={comments.length === 0}>
                <Download className="h-4 w-4 mr-2" />
                Download JSON
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search in JSON data..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <div className="relative">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : comments.length > 0 ? (
              <pre className="bg-muted p-4 rounded-lg overflow-x-auto max-h-[600px] overflow-y-auto">
                <code className="text-sm">
                  {JSON.stringify(filteredComments, null, 2)}
                </code>
              </pre>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No comments found
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

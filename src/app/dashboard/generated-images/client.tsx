/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useState, useEffect, useMemo } from "react";
import { db } from "@/lib/firebase";
import {
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  doc,
  getDoc,
} from "firebase/firestore";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ReloadIcon } from "@radix-ui/react-icons";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { LayoutGrid, List } from "lucide-react";

interface Comment {
  id: string;
  authorName: string;
  username: string;
  postBody: string;
  created_at: string;
  comment_like_count: number;
  metrics: {
    likes: number;
  };
  imageUrl?: string;
  imageGenerated?: boolean;
  imageGenerating?: boolean;
  imageCaption?: string;
  imageStyle?: string;
  imageDimensions?: {
    width: string;
    height: string;
  };
  imageProcessedAt?: string;
  textAnalysis?: Record<string, unknown>;
  sentimentAnalysis?: Record<string, unknown>;
  storyAnalysis?: Record<string, unknown>;
  cultureFitAnalysis?: Record<string, unknown>;
  qualityCheck?: Record<string, unknown>;
  artDirection?: Record<string, unknown>;
  mood?: string;
  color_palette?: string[];
  focal_points?: string[];
  visual_elements?: string[];
  lighting?: string;
  texture_style?: string;
  atmosphere?: string;
  emotional_impact?: string;
  visual_flow?: string;
  key_elements?: string[];
}

interface GeneratedImagesClientProps {
  initialImages: Comment[];
  totalCount: number;
}

export default function GeneratedImagesClient({
  initialImages,
  totalCount,
}: GeneratedImagesClientProps) {
  const [images, setImages] = useState<Comment[]>(initialImages);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(initialImages.length < totalCount);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchById, setSearchById] = useState("");
  const [selectedFilter, setSelectedFilter] = useState<string>("all");
  const [selectedSort, setSelectedSort] = useState<string>("newest");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showProcessing, setShowProcessing] = useState(false);
  const [showQueue, setShowQueue] = useState(false);
  const [hideCompleted, setHideCompleted] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedMood, setSelectedMood] = useState<string>("");
  const [dateRange, setDateRange] = useState<{
    start: string | null;
    end: string | null;
  }>({
    start: null,
    end: null,
  });

  const IMAGES_PER_PAGE = 50;
  const { toast } = useToast();
  const [selectedImage, setSelectedImage] = useState<Comment | null>(null);
  const [fullImageView, setFullImageView] = useState<Comment | null>(null);

  // Filter options
  const filterOptions = [
    { value: "all", label: "All Images" },
    { value: "processing", label: "Processing" },
    { value: "completed", label: "Completed" },
    { value: "failed", label: "Failed" },
    { value: "recent", label: "Last 24 Hours" },
  ];

  const sortOptions = [
    { value: "newest", label: "Newest First" },
    { value: "oldest", label: "Oldest First" },
    { value: "likes", label: "Most Liked" },
    { value: "processing", label: "Processing First" },
  ];

  const moodOptions = [
    "Joyful",
    "Serene",
    "Energetic",
    "Mysterious",
    "Dramatic",
    "Peaceful",
    "Melancholic",
    "Vibrant",
  ];

  // Enhanced filtering and sorting logic
  const processedImages = useMemo(() => {
    const filtered = images.filter((img) => {
      // Basic filter conditions
      if (selectedFilter === "processing" && !img.imageGenerating) return false;
      if (selectedFilter === "completed" && !img.imageGenerated) return false;
      if (selectedFilter === "failed" && img.imageGenerated !== false)
        return false;
      if (selectedFilter === "recent") {
        const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
        if (new Date(img.created_at) < last24Hours) return false;
      }

      // Mood filter
      if (selectedMood && img.mood !== selectedMood) return false;

      // Date range filter
      if (
        dateRange.start &&
        new Date(img.created_at) < new Date(dateRange.start)
      )
        return false;
      if (dateRange.end && new Date(img.created_at) > new Date(dateRange.end))
        return false;

      // Tag filter
      if (selectedTags.length > 0) {
        const imgTags = [
          ...(img.visual_elements || []),
          ...(img.key_elements || []),
        ];
        if (!selectedTags.some((tag) => imgTags.includes(tag))) return false;
      }

      // Search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          img.postBody?.toLowerCase().includes(searchLower) ||
          img.imageCaption?.toLowerCase().includes(searchLower) ||
          img.imageStyle?.toLowerCase().includes(searchLower) ||
          img.mood?.toLowerCase().includes(searchLower)
        );
      }

      return true;
    });

    // Sort the filtered images
    return filtered.sort((a, b) => {
      switch (selectedSort) {
        case "oldest":
          return (
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );
        case "likes":
          return (b.metrics?.likes || 0) - (a.metrics?.likes || 0);
        case "processing":
          return (
            (b.imageGenerating === true ? 1 : -1) -
            (a.imageGenerating === true ? 1 : -1)
          );
        default: // newest
          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
      }
    });
  }, [
    images,
    selectedFilter,
    selectedMood,
    dateRange,
    selectedTags,
    searchTerm,
    selectedSort,
  ]);

  // Get processing images
  const processingImages = useMemo(() => {
    return images.filter((img) => img.imageGenerating === true);
  }, [images]);

  // Count of processing images
  const processingCount = processingImages.length;

  // Effect to automatically show queue when hideCompleted is true
  useEffect(() => {
    if (hideCompleted) {
      setShowQueue(true);
    }
  }, [hideCompleted]);

  // Fetch images with pagination
  const fetchImages = async (pageToFetch = 1, append = false) => {
    try {
      setLoading(true);

      // If searching by ID, fetch that specific document
      if (searchById.trim()) {
        try {
          const docRef = doc(db, "instagram_comments", searchById.trim());
          const docSnap = await getDoc(docRef);

          if (
            docSnap.exists() &&
            (docSnap.data().imageGenerated || docSnap.data().imageGenerating)
          ) {
            const commentData = {
              id: docSnap.id,
              ...docSnap.data(),
              created_at:
                docSnap.data().created_at?.toDate?.()?.toISOString() ||
                new Date().toISOString(),
            } as Comment;

            setImages([commentData]);
            setHasMore(false);
            setPage(1);
          } else {
            toast({
              title: "Image not found",
              description: `No generated or processing image found with ID: ${searchById}`,
              variant: "destructive",
            });
            setImages([]);
          }
        } catch (error) {
          console.error("Error fetching document by ID:", error);
          toast({
            title: "Error",
            description: "Failed to fetch image by ID",
            variant: "destructive",
          });
          setImages([]);
        }
        setLoading(false);
        return;
      }

      // Regular search
      const commentsRef = collection(db, "instagram_comments");

      // Create the base query
      let baseQuery;

      if (showProcessing) {
        // Show both generated and processing images
        baseQuery = query(
          commentsRef,
          where("imageGenerating", "in", [true, false]), // Include both true and false
          orderBy("created_at", "desc"),
          limit(pageToFetch * IMAGES_PER_PAGE)
        );
      } else {
        // Only show generated images (original behavior)
        baseQuery = query(
          commentsRef,
          where("imageGenerated", "==", true),
          orderBy("created_at", "desc"),
          limit(pageToFetch * IMAGES_PER_PAGE)
        );
      }

      const snapshot = await getDocs(baseQuery);

      if (snapshot.empty) {
        toast({
          title: "No images found",
          description: "No generated images were found in the database",
          variant: "destructive",
        });
        setImages([]);
      } else {
        const imagesData = snapshot.docs.map((doc) => {
          const data = doc.data() as Record<string, any>;
          return {
            id: doc.id,
            authorName: data.authorName,
            username: data.username,
            postBody: data.postBody,
            comment_like_count: data.comment_like_count,
            metrics: data.metrics,
            imageUrl: data.imageUrl,
            imageGenerated: data.imageGenerated,
            imageGenerating: data.imageGenerating,
            imageCaption: data.imageCaption,
            imageStyle: data.imageStyle,
            imageDimensions: data.imageDimensions,
            imageProcessedAt: data.imageProcessedAt,
            mood: data.mood,
            created_at:
              data.created_at?.toDate?.()?.toISOString() ||
              new Date().toISOString(),
          } as Comment;
        });

        if (append) {
          setImages((prev) => [...prev, ...imagesData.slice(prev.length)]);
        } else {
          setImages(imagesData);
        }

        setHasMore(imagesData.length >= pageToFetch * IMAGES_PER_PAGE);
        setPage(pageToFetch);
      }
    } catch (error) {
      console.error("Error fetching images:", error);
      toast({
        title: "Error",
        description: "Failed to fetch images",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadMoreImages = () => {
    if (!loading && hasMore) {
      fetchImages(page + 1, true);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Clear ID search when doing text search
    if (searchTerm) {
      setSearchById("");
    }
    fetchImages(1, false);
  };

  const handleIdSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Clear text search when doing ID search
    if (searchById) {
      setSearchTerm("");
      setSelectedFilter("all");
    }
    fetchImages(1, false);
  };

  const filterImages = (images: Comment[]) => {
    let filtered = [...images];

    // Skip filtering if we're searching by ID
    if (searchById.trim()) {
      return filtered;
    }

    // Apply search term filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (img) =>
          img.postBody?.toLowerCase().includes(searchLower) ||
          img.imageStyle?.toLowerCase().includes(searchLower) ||
          img.mood?.toLowerCase().includes(searchLower)
      );
    }

    // Filter for processing images if showProcessing is true
    if (showProcessing) {
      // Prioritize processing images at the top
      filtered.sort((a, b) => {
        // Processing images first
        if (a.imageGenerating && !b.imageGenerating) return -1;
        if (!a.imageGenerating && b.imageGenerating) return 1;

        // Then sort by date (newest first)
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return dateB - dateA;
      });
    } else {
      // Only show generated images
      filtered = filtered.filter((img) => img.imageGenerated === true);
    }

    // If hideCompleted is true, only show processing images
    if (hideCompleted) {
      filtered = filtered.filter((img) => img.imageGenerating === true);
    }

    // Apply style/category filter
    if (selectedFilter !== "all") {
      filtered = filtered.filter((img) => {
        if (selectedFilter === "photorealistic") {
          return img.imageStyle?.toLowerCase().includes("photorealistic");
        } else if (selectedFilter === "digital-art") {
          return img.imageStyle?.toLowerCase().includes("digital art");
        } else if (selectedFilter === "watercolor") {
          return img.imageStyle?.toLowerCase().includes("watercolor");
        } else if (selectedFilter === "3d") {
          return img.imageStyle?.toLowerCase().includes("3d");
        } else if (selectedFilter === "other") {
          return (
            !img.imageStyle?.toLowerCase().includes("photorealistic") &&
            !img.imageStyle?.toLowerCase().includes("digital art") &&
            !img.imageStyle?.toLowerCase().includes("watercolor") &&
            !img.imageStyle?.toLowerCase().includes("3d")
          );
        }
        return true;
      });
    }

    // Apply sorting
    if (selectedSort === "newest") {
      filtered.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    } else if (selectedSort === "oldest") {
      filtered.sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    } else if (selectedSort === "popular") {
      filtered.sort(
        (a, b) => (b.metrics?.likes || 0) - (a.metrics?.likes || 0)
      );
    }

    return filtered;
  };

  const filteredImages = filterImages(images);

  // Format JSON for display
  const formatJSON = (json: Record<string, unknown> | null | undefined) => {
    if (!json) return "No data";
    return JSON.stringify(json, null, 2);
  };

  // Render a single image card
  const ImageCard = ({
    comment,
    onSelect,
    viewMode,
  }: {
    comment: Comment;
    onSelect: (image: Comment | null) => void;
    viewMode: "grid" | "list";
  }) => {
    // Get analysis data
    const sentimentData = comment.sentimentAnalysis as any;
    const qualityData = comment.qualityCheck as any;
    const artDirection = comment.artDirection as any;
    const cultureData = comment.cultureFitAnalysis as any;

    return (
      <Card
        className={`overflow-hidden h-full flex flex-col group hover:shadow-xl transition-all duration-300 ${
          comment.imageGenerating
            ? "border-yellow-500 dark:border-yellow-600 animate-pulse"
            : "hover:border-primary/50"
        }`}
      >
        <div className="relative aspect-video">
          {comment.imageUrl ? (
            <>
              <Image
                src={comment.imageUrl.split("?")[0]}
                alt={comment.imageCaption || "Generated image"}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect(comment);
                }}
              />

              {/* Top badges */}
              <div className="absolute top-2 left-2 flex gap-1">
                <div className="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-lg">
                  ✓
                </div>
                {artDirection?.style && (
                  <div className="bg-black/70 text-white text-xs px-2 py-0.5 rounded-full backdrop-blur-sm">
                    {artDirection.style}
                  </div>
                )}
              </div>

              <div
                className="absolute inset-0 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect(comment);
                }}
              ></div>
            </>
          ) : comment.imageGenerating ? (
            <div className="w-full h-full flex items-center justify-center bg-yellow-50 dark:bg-yellow-900/20">
              <div className="text-center">
                <div className="flex justify-center mb-2">
                  <svg
                    className="animate-spin h-8 w-8 text-yellow-600 dark:text-yellow-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                </div>
                <p className="text-yellow-800 dark:text-yellow-400 font-medium">
                  Processing Image
                </p>
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-muted">
              <p className="text-muted-foreground">No image available</p>
            </div>
          )}

          {comment.imageUrl && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 bg-black/40 hover:bg-black/60 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                setFullImageView(comment);
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-maximize-2"
              >
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              <span className="sr-only">View Full Image</span>
            </Button>
          )}
        </div>

        <CardContent className="p-4 flex-grow flex flex-col">
          {/* Author info */}
          <div className="flex items-center gap-2 mb-3">
            <div className="text-xs text-muted-foreground">
              {comment.imageProcessedAt
                ? new Date(comment.imageProcessedAt).toLocaleDateString()
                : new Date(comment.created_at).toLocaleDateString()}
            </div>
          </div>

          {/* Status and tags */}
          <div className="mb-3 space-y-2">
            {comment.imageGenerating && (
              <span className="text-xs px-2 py-0.5 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-400 rounded-full flex items-center gap-1 w-fit">
                <svg
                  className="animate-spin h-3 w-3"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing
              </span>
            )}

            {/* Analysis badges */}
            <div className="flex items-center gap-1 flex-wrap">
              {sentimentData?.sentiment && (
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    sentimentData.sentiment === "positive"
                      ? "bg-green-100 text-green-800"
                      : sentimentData.sentiment === "negative"
                      ? "bg-red-100 text-red-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  😊 {sentimentData.sentiment_score}/10
                </span>
              )}
              {qualityData?.overall_score && (
                <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                  ⭐ {qualityData.overall_score}/10
                </span>
              )}
              {qualityData?.engagement_score && (
                <span className="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">
                  📈 {qualityData.engagement_score}/10
                </span>
              )}
            </div>

            {/* Style and mood tags */}
            <div className="flex items-center gap-1 flex-wrap">
              <span className="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded-full">
                🎨 {comment.imageStyle || artDirection?.style || "Standard"}
              </span>
              {(comment.mood || artDirection?.mood) && (
                <span className="text-xs px-2 py-0.5 bg-secondary/10 text-secondary rounded-full">
                  💭 {comment.mood || artDirection?.mood}
                </span>
              )}
            </div>
          </div>

          {/* Post content preview */}
          <div className="flex-grow">
            <p className="text-xs text-muted-foreground line-clamp-3 mb-2">
              {comment.postBody}
            </p>
          </div>

          <div className="text-xs text-muted-foreground mb-3 pt-2 border-t">
            <div className="flex justify-between items-center">
              <span>ID: {comment.id.substring(0, 8)}...</span>
              <div className="flex items-center gap-1">
                <span>❤️</span>
                <span>
                  {comment.comment_like_count || comment.metrics?.likes || 0}
                </span>
              </div>
            </div>
          </div>
        </CardContent>

        <div className="p-4 pt-0">
          <Button
            variant="outline"
            size="sm"
            className="w-full hover:bg-primary hover:text-primary-foreground transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onSelect(comment);
            }}
          >
            View Analysis Report
          </Button>
        </div>
      </Card>
    );
  };

  // Image Details Dialog component
  const ImageDetailsDialog = ({
    image,
    onClose,
  }: {
    image: Comment | null;
    onClose: () => void;
  }) => {
    if (!image) return null;

    return (
      <Dialog open={!!image} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              Image Analysis Report
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Image Column */}
            <div className="lg:col-span-1">
              {image.imageUrl ? (
                <div className="relative aspect-square rounded-lg overflow-hidden border shadow-md">
                  <Image
                    src={image.imageUrl.split("?")[0]}
                    alt="Generated image"
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="aspect-square rounded-lg bg-muted flex items-center justify-center">
                  <p className="text-muted-foreground">No image available</p>
                </div>
              )}

              {/* Author & Post Info */}
              <div className="mt-4 p-4 bg-muted/30 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    {image.authorName?.charAt(0) ||
                      image.username?.charAt(0) ||
                      "U"}
                  </div>
                  <div>
                    <p className="font-medium text-sm">
                      {image.authorName || "Unknown Author"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      @{image.username || "unknown"}
                    </p>
                  </div>
                </div>
                <p className="text-sm mb-3 p-2 bg-background rounded border">
                  {image.postBody}
                </p>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <span className="text-red-500">❤️</span>
                    <span>
                      {image.comment_like_count || image.metrics?.likes || 0}{" "}
                      likes
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-blue-500">📅</span>
                    <span>
                      {new Date(image.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Analysis Column 1 */}
            <div className="lg:col-span-1">
              <Accordion
                type="multiple"
                className="w-full"
                defaultValue={["image-info", "sentiment"]}
              >
                <AccordionItem value="image-info">
                  <AccordionTrigger>Image Information</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      <div>
                        <span className="font-semibold">Style:</span>{" "}
                        {image.imageStyle ||
                          (image.artDirection as any)?.style ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Mood:</span>{" "}
                        {image.mood ||
                          (image.artDirection as any)?.mood ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Dimensions:</span>{" "}
                        {image.imageDimensions
                          ? `${image.imageDimensions.width}x${image.imageDimensions.height}`
                          : "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Generated at:</span>{" "}
                        {image.imageProcessedAt
                          ? new Date(image.imageProcessedAt).toLocaleString()
                          : "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Atmosphere:</span>{" "}
                        {image.atmosphere ||
                          (image.artDirection as any)?.atmosphere ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Lighting:</span>{" "}
                        {image.lighting ||
                          (image.artDirection as any)?.lighting ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Texture Style:</span>{" "}
                        {image.texture_style ||
                          (image.artDirection as any)?.texture_style ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Visual Flow:</span>{" "}
                        {image.visual_flow ||
                          (image.artDirection as any)?.visual_flow ||
                          "Not specified"}
                      </div>
                      <div>
                        <span className="font-semibold">Emotional Impact:</span>{" "}
                        {image.emotional_impact ||
                          (image.artDirection as any)?.emotional_impact ||
                          "Not specified"}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="color-palette">
                  <AccordionTrigger>Color Palette</AccordionTrigger>
                  <AccordionContent>
                    <div className="flex flex-wrap gap-2">
                      {(image.color_palette ||
                        (image.artDirection as any)?.color_palette) &&
                      (
                        image.color_palette ||
                        (image.artDirection as any)?.color_palette
                      ).length > 0 ? (
                        (
                          image.color_palette ||
                          (image.artDirection as any)?.color_palette
                        ).map((color: string, index: number) => {
                          // Extract hex code if it exists in the format "color (#hex)"
                          const hexMatch = color.match(/#[0-9A-Fa-f]{6}/);
                          const hexColor = hexMatch
                            ? hexMatch[0]
                            : color.startsWith("#")
                            ? color
                            : "#CCCCCC";

                          return (
                            <div
                              key={index}
                              className="flex flex-col items-center"
                            >
                              <div
                                className="w-10 h-10 rounded-full border border-gray-200"
                                style={{ backgroundColor: hexColor }}
                              />
                              <span className="text-xs mt-1">{color}</span>
                            </div>
                          );
                        })
                      ) : (
                        <p className="text-sm text-gray-500">
                          No color palette specified
                        </p>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="elements">
                  <AccordionTrigger>Visual Elements</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-1">Focal Points</h4>
                        {(image.focal_points ||
                          (image.artDirection as any)?.focal_points) &&
                        (
                          image.focal_points ||
                          (image.artDirection as any)?.focal_points
                        ).length > 0 ? (
                          <ul className="list-disc pl-5 text-sm">
                            {(
                              image.focal_points ||
                              (image.artDirection as any)?.focal_points
                            ).map((point: string, index: number) => (
                              <li key={index}>{point}</li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No focal points specified
                          </p>
                        )}
                      </div>

                      <div>
                        <h4 className="font-semibold mb-1">Visual Elements</h4>
                        {(image.visual_elements ||
                          (image.artDirection as any)?.visual_elements) &&
                        (
                          image.visual_elements ||
                          (image.artDirection as any)?.visual_elements
                        ).length > 0 ? (
                          <ul className="list-disc pl-5 text-sm">
                            {(
                              image.visual_elements ||
                              (image.artDirection as any)?.visual_elements
                            ).map((element: string, index: number) => (
                              <li key={index}>{element}</li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No visual elements specified
                          </p>
                        )}
                      </div>

                      <div>
                        <h4 className="font-semibold mb-1">Key Elements</h4>
                        {(image.key_elements ||
                          (image.artDirection as any)?.key_elements) &&
                        (
                          image.key_elements ||
                          (image.artDirection as any)?.key_elements
                        ).length > 0 ? (
                          <ul className="list-disc pl-5 text-sm">
                            {(
                              image.key_elements ||
                              (image.artDirection as any)?.key_elements
                            ).map((element: string, index: number) => (
                              <li key={index}>{element}</li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No key elements specified
                          </p>
                        )}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="sentiment">
                  <AccordionTrigger className="text-green-700">
                    <div className="flex items-center gap-2">
                      <span>😊</span>
                      Sentiment Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(image.sentimentAnalysis as any) && (
                        <>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">Sentiment:</span>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${
                                (image.sentimentAnalysis as any).sentiment ===
                                "positive"
                                  ? "bg-green-100 text-green-800"
                                  : (image.sentimentAnalysis as any)
                                      .sentiment === "negative"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {(image.sentimentAnalysis as any).sentiment}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              (
                              {(image.sentimentAnalysis as any).sentiment_score}
                              /10)
                            </span>
                          </div>

                          <div>
                            <span className="font-semibold">Intensity:</span>{" "}
                            <span
                              className={`px-2 py-1 rounded text-xs ${
                                (image.sentimentAnalysis as any).intensity ===
                                "high"
                                  ? "bg-red-50 text-red-700"
                                  : (image.sentimentAnalysis as any)
                                      .intensity === "low"
                                  ? "bg-blue-50 text-blue-700"
                                  : "bg-orange-50 text-orange-700"
                              }`}
                            >
                              {(image.sentimentAnalysis as any).intensity}
                            </span>
                          </div>

                          {(image.sentimentAnalysis as any).emotions && (
                            <div>
                              <h4 className="font-semibold mb-1">Emotions</h4>
                              <div className="flex flex-wrap gap-1">
                                {(image.sentimentAnalysis as any).emotions.map(
                                  (emotion: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                                    >
                                      {emotion}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                          {(image.sentimentAnalysis as any).summary && (
                            <div>
                              <h4 className="font-semibold mb-1">Summary</h4>
                              <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                                {(image.sentimentAnalysis as any).summary}
                              </p>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="text-analysis">
                  <AccordionTrigger className="text-blue-700">
                    <div className="flex items-center gap-2">
                      <span>📝</span>
                      Text Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(image.textAnalysis as any) && (
                        <>
                          {(image.textAnalysis as any).summary && (
                            <div>
                              <h4 className="font-semibold mb-1">Summary</h4>
                              <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                                {(image.textAnalysis as any).summary}
                              </p>
                            </div>
                          )}

                          {(image.textAnalysis as any).topics && (
                            <div>
                              <h4 className="font-semibold mb-1">Topics</h4>
                              <div className="flex flex-wrap gap-1">
                                {(image.textAnalysis as any).topics.map(
                                  (topic: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs"
                                    >
                                      {topic}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                          {(image.textAnalysis as any).keywords && (
                            <div>
                              <h4 className="font-semibold mb-1">Keywords</h4>
                              <div className="flex flex-wrap gap-1">
                                {(image.textAnalysis as any).keywords.map(
                                  (keyword: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs"
                                    >
                                      {keyword}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            {/* Analysis Column 2 */}
            <div className="lg:col-span-1">
              <Accordion
                type="multiple"
                className="w-full"
                defaultValue={["quality", "culture-fit"]}
              >
                <AccordionItem value="quality">
                  <AccordionTrigger className="text-orange-700">
                    <div className="flex items-center gap-2">
                      <span>⭐</span>
                      Quality Assessment
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(image.qualityCheck as any) && (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-green-600">
                                {(image.qualityCheck as any).overall_score}/10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Overall
                              </div>
                            </div>
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-blue-600">
                                {(image.qualityCheck as any).engagement_score}
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Engagement
                              </div>
                            </div>
                          </div>

                          {(image.qualityCheck as any).feedback && (
                            <div>
                              <h4 className="font-semibold mb-1">Feedback</h4>
                              <ul className="space-y-1 text-sm">
                                {(image.qualityCheck as any).feedback.map(
                                  (item: string, index: number) => (
                                    <li
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-green-500 mt-1">
                                        •
                                      </span>
                                      <span>{item}</span>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}

                          {(image.qualityCheck as any).improvements && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Improvements
                              </h4>
                              <ul className="space-y-1 text-sm">
                                {(image.qualityCheck as any).improvements.map(
                                  (item: string, index: number) => (
                                    <li
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-orange-500 mt-1">
                                        →
                                      </span>
                                      <span>{item}</span>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="culture-fit">
                  <AccordionTrigger className="text-purple-700">
                    <div className="flex items-center gap-2">
                      <span>🎯</span>
                      Culture Fit Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(image.cultureFitAnalysis as any) && (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-purple-600">
                                {
                                  (image.cultureFitAnalysis as any)
                                    .alignment_score
                                }
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Alignment
                              </div>
                            </div>
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-lg font-bold text-green-600">
                                {(image.cultureFitAnalysis as any).market_fit}
                                /10
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Market Fit
                              </div>
                            </div>
                          </div>

                          {(image.cultureFitAnalysis as any).brand_values && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Brand Values
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {(
                                  image.cultureFitAnalysis as any
                                ).brand_values.map(
                                  (value: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs"
                                    >
                                      {value}
                                    </span>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                          {(image.cultureFitAnalysis as any)
                            .recommendations && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Recommendations
                              </h4>
                              <ul className="space-y-1 text-sm">
                                {(
                                  image.cultureFitAnalysis as any
                                ).recommendations.map(
                                  (item: string, index: number) => (
                                    <li
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-green-500 mt-1">
                                        ✓
                                      </span>
                                      <span>{item}</span>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="story">
                  <AccordionTrigger className="text-indigo-700">
                    <div className="flex items-center gap-2">
                      <span>📚</span>
                      Story Analysis
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(image.storyAnalysis as any) && (
                        <>
                          {(image.storyAnalysis as any).title && (
                            <div>
                              <h4 className="font-semibold mb-1">Title</h4>
                              <p className="font-medium text-indigo-700">
                                {(image.storyAnalysis as any).title}
                              </p>
                            </div>
                          )}

                          <div className="grid grid-cols-2 gap-2">
                            {(image.storyAnalysis as any).style && (
                              <div>
                                <span className="font-semibold">Style:</span>{" "}
                                <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs">
                                  {(image.storyAnalysis as any).style}
                                </span>
                              </div>
                            )}
                            {(image.storyAnalysis as any).tone && (
                              <div>
                                <span className="font-semibold">Tone:</span>{" "}
                                <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs">
                                  {(image.storyAnalysis as any).tone}
                                </span>
                              </div>
                            )}
                          </div>

                          {(image.storyAnalysis as any).story && (
                            <div>
                              <h4 className="font-semibold mb-1">
                                Generated Story
                              </h4>
                              <div className="max-h-32 overflow-y-auto bg-muted/50 p-3 rounded text-sm">
                                {(image.storyAnalysis as any).story}
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="art-direction">
                  <AccordionTrigger className="text-gray-700">
                    <div className="flex items-center gap-2">
                      <span>🎨</span>
                      Art Direction (Technical)
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      {(image.artDirection as any)?.composition && (
                        <div>
                          <h4 className="font-semibold mb-1">Composition</h4>
                          <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                            {(image.artDirection as any).composition}
                          </p>
                        </div>
                      )}
                      <details className="border rounded p-2">
                        <summary className="cursor-pointer font-medium text-sm">
                          View Full Art Direction JSON
                        </summary>
                        <pre className="bg-muted p-2 rounded-md text-xs overflow-x-auto whitespace-pre-wrap mt-2">
                          {JSON.stringify(image.artDirection, null, 2)}
                        </pre>
                      </details>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Handle keyboard navigation for full image view
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!fullImageView) return;

      if (e.key === "ArrowRight") {
        navigateImage("next");
      } else if (e.key === "ArrowLeft") {
        navigateImage("prev");
      } else if (e.key === "Escape") {
        setFullImageView(null);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fullImageView, images]);

  // Navigate to next/previous image
  const navigateImage = (direction: "next" | "prev") => {
    if (!fullImageView) return;
    const currentIndex = images.findIndex((img) => img.id === fullImageView.id);
    if (currentIndex === -1) return;

    let newIndex;
    if (direction === "next") {
      newIndex = currentIndex + 1 >= images.length ? 0 : currentIndex + 1;
    } else {
      newIndex = currentIndex - 1 < 0 ? images.length - 1 : currentIndex - 1;
    }

    setFullImageView(images[newIndex]);
  };

  // Full Image Dialog component
  const FullImageDialog = () => {
    if (!fullImageView) return null;

    // Find the current image index
    const currentIndex = images.findIndex((img) => img.id === fullImageView.id);

    // Handle image download
    const downloadImage = async () => {
      if (!fullImageView.imageUrl) return;

      try {
        // Fetch the image
        const response = await fetch(fullImageView.imageUrl);
        const blob = await response.blob();

        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;

        // Generate a filename
        const filename = `image-${fullImageView.id.substring(0, 8)}.png`;
        link.download = filename;

        // Trigger the download
        document.body.appendChild(link);
        link.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);

        toast({
          title: "Download started",
          description: `Downloading ${filename}`,
        });
      } catch (error) {
        console.error("Error downloading image:", error);
        toast({
          title: "Download failed",
          description: "There was an error downloading the image",
          variant: "destructive",
        });
      }
    };

    return (
      <Dialog
        open={!!fullImageView}
        onOpenChange={(open) => !open && setFullImageView(null)}
      >
        <DialogContent className="max-w-7xl p-0 overflow-hidden bg-black/90">
          <DialogHeader className="absolute top-4 left-4 right-4 z-50 flex justify-between items-center">
            <DialogTitle className="text-white text-lg">
              Image Preview
            </DialogTitle>
            <div className="flex gap-2">
              {fullImageView.imageUrl && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-black/40 hover:bg-black/60 text-white rounded-full"
                  onClick={downloadImage}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-download"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  <span className="sr-only">Download Image</span>
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="bg-black/40 hover:bg-black/60 text-white rounded-full"
                onClick={() => setFullImageView(null)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-x"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
                <span className="sr-only">Close</span>
              </Button>
            </div>
          </DialogHeader>

          <div className="relative w-full h-[90vh] flex items-center justify-center">
            {fullImageView.imageUrl ? (
              <div className="relative w-full h-full">
                <Image
                  src={fullImageView.imageUrl}
                  alt={fullImageView.imageCaption || "Generated image"}
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-full w-full">
                <p className="text-white">No image available</p>
              </div>
            )}

            {/* Navigation buttons */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-1/2 left-4 -translate-y-1/2 bg-black/40 hover:bg-black/60 text-white rounded-full h-10 w-10"
                  onClick={() => navigateImage("prev")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-chevron-left"
                  >
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                  <span className="sr-only">Previous Image</span>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-1/2 right-4 -translate-y-1/2 bg-black/40 hover:bg-black/60 text-white rounded-full h-10 w-10"
                  onClick={() => navigateImage("next")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-chevron-right"
                  >
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                  <span className="sr-only">Next Image</span>
                </Button>
              </>
            )}

            <div className="absolute bottom-4 left-4 right-4 bg-black/60 p-3 rounded-md">
              <p className="text-white text-sm">{fullImageView.postBody}</p>
              <p className="text-white/70 text-xs mt-1">
                ID: {fullImageView.id.substring(0, 8)}... •
                {fullImageView.imageProcessedAt
                  ? new Date(
                      fullImageView.imageProcessedAt
                    ).toLocaleDateString()
                  : "Unknown date"}
                {images.length > 1 &&
                  ` • Image ${currentIndex + 1} of ${images.length}`}
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Dashboard Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 bg-card rounded-lg p-6 shadow-sm border">
        <div>
          <h1 className="text-2xl font-bold">Generated Images Gallery</h1>
          <p className="text-muted-foreground">
            Browse and manage your AI-generated images
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
            className="w-10 h-10 p-0"
          >
            {viewMode === "grid" ? (
              <List className="h-4 w-4" />
            ) : (
              <LayoutGrid className="h-4 w-4" />
            )}
          </Button>
          <Button
            onClick={() => fetchImages(1)}
            disabled={loading}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <ReloadIcon
              className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <input
                type="text"
                placeholder="Search images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 rounded-md border bg-background"
              />
            </div>

            {/* Filter Dropdown */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Filter</label>
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="w-full px-3 py-2 rounded-md border bg-background"
              >
                {filterOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort Dropdown */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort</label>
              <select
                value={selectedSort}
                onChange={(e) => setSelectedSort(e.target.value)}
                className="w-full px-3 py-2 rounded-md border bg-background"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Mood Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Mood</label>
              <select
                value={selectedMood}
                onChange={(e) => setSelectedMood(e.target.value)}
                className="w-full px-3 py-2 rounded-md border bg-background"
              >
                <option value="">All Moods</option>
                {moodOptions.map((mood) => (
                  <option key={mood} value={mood}>
                    {mood}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Advanced Filters */}
          <div className="mt-4 pt-4 border-t">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={showProcessing ? "default" : "outline"}
                size="sm"
                onClick={() => setShowProcessing(!showProcessing)}
              >
                {showProcessing ? "Hide Processing" : "Show Processing"}
              </Button>
              <Button
                variant={hideCompleted ? "default" : "outline"}
                size="sm"
                onClick={() => setHideCompleted(!hideCompleted)}
              >
                {hideCompleted ? "Show Completed" : "Hide Completed"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Processing Queue Section */}
      {showQueue && processingCount > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Processing Queue
              <span className="text-sm bg-yellow-500/10 text-yellow-500 px-2 py-0.5 rounded-full">
                {processingCount} Processing
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {processingImages.map((img) => (
                <ImageCard
                  key={img.id}
                  comment={img}
                  onSelect={setSelectedImage}
                  viewMode="grid"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Gallery */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              Gallery
              {loading && (
                <ReloadIcon className="ml-2 h-4 w-4 animate-spin inline" />
              )}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Showing {processedImages.length} of {totalCount} images
            </p>
          </div>
        </CardHeader>
        <CardContent>
          {processedImages.length > 0 ? (
            <div
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6"
                  : "space-y-4"
              }
            >
              {processedImages.map((img) => (
                <ImageCard
                  key={img.id}
                  comment={img}
                  onSelect={setSelectedImage}
                  viewMode={viewMode}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No images found</p>
            </div>
          )}

          {hasMore && (
            <div className="mt-6 text-center">
              <Button
                onClick={loadMoreImages}
                disabled={loading}
                variant="outline"
              >
                {loading ? (
                  <>
                    <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Load More"
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Image Details Dialog */}
      <ImageDetailsDialog
        image={selectedImage}
        onClose={() => setSelectedImage(null)}
      />

      {/* Full Image Dialog */}
      <FullImageDialog />
    </div>
  );
}

import { Suspense } from "react";
import GeneratedImagesClient from "./client";
import { adminDb } from "@/lib/firebase-admin";

// Server component to fetch initial data with pagination
export default async function GeneratedImagesPage() {
  try {
    // Fetch a limited number of images to prevent freezing
    const INITIAL_LIMIT = 50; // Start with a reasonable number

    // Fetch all generated images using the admin SDK with pagination
    const commentsRef = adminDb.collection("instagram_comments");
    const snapshot = await commentsRef
      .where("imageGenerated", "==", true)
      .orderBy("created_at", "desc")
      .limit(INITIAL_LIMIT)
      .get();

    // Get total count of generated images
    const countQuery = commentsRef.where("imageGenerated", "==", true);
    const countSnapshot = await countQuery.count().get();
    const totalCount = countSnapshot.data().count;

    // Process the images with proper type handling
    const images = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        authorName: data.authorName || "",
        username: data.username || "",
        postBody: data.postBody || "",
        created_at:
          data.created_at?.toDate?.()?.toISOString() ||
          new Date().toISOString(),
        comment_like_count: data.comment_like_count || 0,
        metrics: data.metrics || { likes: 0 },
        imageUrl: data.imageUrl || "",
        imageGenerated: true,
        textAnalysis: data.textAnalysis || null,
        sentimentAnalysis: data.sentimentAnalysis || null,
        storyAnalysis: data.storyAnalysis || null,
        cultureFitAnalysis: data.cultureFitAnalysis || null,
        qualityCheck: data.qualityCheck || null,
        artDirection: data.artDirection || null,
      };
    });

    return (
      <Suspense
        fallback={
          <div className="flex justify-center items-center h-screen">
            Loading...
          </div>
        }
      >
        <GeneratedImagesClient initialImages={images} totalCount={totalCount} />
      </Suspense>
    );
  } catch (error) {
    console.error("Error fetching images:", error);
    // Return an error state
    return (
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Generated Images Gallery</h1>
        <div className="bg-destructive/20 p-4 rounded-md">
          <h2 className="text-xl font-semibold text-destructive">Error</h2>
          <p className="text-destructive">
            Failed to fetch images from the database. Error:{" "}
            {error instanceof Error ? error.message : String(error)}
          </p>
        </div>
      </div>
    );
  }
}

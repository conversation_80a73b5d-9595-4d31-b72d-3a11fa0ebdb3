"use client";

import { useAuth } from "@/hooks/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { PersonIcon, ExitIcon } from "@radix-ui/react-icons";
import Image from "next/image";

export default function ProfilePage() {
  const { user, logout } = useAuth();

  if (!user) {
    return null;
  }

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Failed to logout:", error);
    }
  };

  return (
    <div className="container max-w-2xl py-6 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
          <CardDescription>
            Manage your account settings and profile information.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* <PERSON> Header */}
          <div className="flex items-start gap-6">
            <Avatar className="h-20 w-20">
              {user.photoURL ? (
                <Image
                  src={user.photoURL}
                  alt={user.displayName || "User"}
                  width={80}
                  height={80}
                  className="object-cover"
                />
              ) : (
                <AvatarFallback className="text-2xl">
                  {user.displayName?.[0] || <PersonIcon className="h-8 w-8" />}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="space-y-1">
              <h2 className="text-2xl font-bold">
                {user.displayName || "User"}
              </h2>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          </div>

          {/* Profile Details */}
          <div className="space-y-4">
            <div className="grid gap-1">
              <h3 className="text-sm font-medium">Email</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
            <div className="grid gap-1">
              <h3 className="text-sm font-medium">Display Name</h3>
              <p className="text-sm text-muted-foreground">
                {user.displayName || "Not set"}
              </p>
            </div>
            <div className="grid gap-1">
              <h3 className="text-sm font-medium">Account Created</h3>
              <p className="text-sm text-muted-foreground">
                {user.metadata.creationTime
                  ? new Date(user.metadata.creationTime).toLocaleDateString()
                  : "Unknown"}
              </p>
            </div>
            <div className="grid gap-1">
              <h3 className="text-sm font-medium">Last Sign In</h3>
              <p className="text-sm text-muted-foreground">
                {user.metadata.lastSignInTime
                  ? new Date(user.metadata.lastSignInTime).toLocaleDateString()
                  : "Unknown"}
              </p>
            </div>
          </div>

          {/* Account Management */}
          <div className="pt-6 space-y-4">
            <h3 className="text-sm font-medium">Account Management</h3>
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <h4 className="text-sm font-medium">Sign Out</h4>
                  <p className="text-sm text-muted-foreground">
                    Sign out of your account on this device
                  </p>
                </div>
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-600"
                >
                  <ExitIcon className="mr-2 h-4 w-4" />
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

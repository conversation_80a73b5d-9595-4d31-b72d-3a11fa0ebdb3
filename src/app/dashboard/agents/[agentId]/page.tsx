"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/hooks/auth-context";
import { useAgents } from "@/hooks/agents-context";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ReloadIcon, GearIcon, SpeakerLoudIcon } from "@radix-ui/react-icons";
import { agentConfig } from "@/hooks/agents-context";
import * as RadixIcons from "@radix-ui/react-icons";
import { ComponentProps, ComponentType } from "react";

export default function AgentSettingsPage() {
  const params = useParams();
  const agentId = params.agentId as string;
  const agent = agentConfig[agentId as keyof typeof agentConfig];
  const { user } = useAuth();
  const { agents, loading, updateAgent } = useAgents();
  const { toast } = useToast();
  const [mounted, setMounted] = useState(false);
  const [saving, setSaving] = useState(false);
  const [localSettings, setLocalSettings] = useState(agents[agentId]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Update local settings when agents change (e.g., initial load)
  useEffect(() => {
    if (agents[agentId]) {
      setLocalSettings(agents[agentId]);
    }
  }, [agents, agentId]);

  const handleSave = async () => {
    if (!user || !agentId || !localSettings) return;

    setSaving(true);
    try {
      await updateAgent(agentId, localSettings);
      toast({
        title: "Settings saved",
        description: "Your agent settings have been successfully updated.",
      });
    } catch (error) {
      console.error("Error saving settings:", error);
      toast({
        variant: "destructive",
        title: "Error saving settings",
        description: "There was a problem saving your agent settings.",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!mounted || loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <ReloadIcon className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!agent || !localSettings) {
    return (
      <div className="space-y-6">
        <Card>
          <div className="p-4 text-muted-foreground">Agent not found</div>
        </Card>
      </div>
    );
  }

  const AgentIcon = RadixIcons[
    agent.icon as keyof typeof RadixIcons
  ] as ComponentType<ComponentProps<"svg">>;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <AgentIcon className="h-5 w-5" />
              <h2 className="text-2xl font-semibold">{agent.name} Settings</h2>
            </div>
            <p className="text-sm text-muted-foreground">{agent.description}</p>
          </div>
          <div className="flex items-center gap-4">
            <Switch
              checked={localSettings.enabled}
              onCheckedChange={(checked) =>
                setLocalSettings({ ...localSettings, enabled: checked })
              }
            />
            <Button
              onClick={handleSave}
              disabled={
                saving ||
                JSON.stringify(localSettings) ===
                  JSON.stringify(agents[agentId])
              }
            >
              {saving && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="prompts" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="prompts" className="flex items-center gap-2">
                <SpeakerLoudIcon className="h-4 w-4" />
                Prompts
              </TabsTrigger>
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <GearIcon className="h-4 w-4" />
                Basic Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="prompts">
              <div className="space-y-6 pt-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">System Prompt</label>
                  <Textarea
                    value={localSettings.systemPrompt}
                    onChange={(e) =>
                      setLocalSettings({
                        ...localSettings,
                        systemPrompt: e.target.value,
                      })
                    }
                    placeholder="Enter the system prompt..."
                    className="min-h-[120px] font-mono"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Instructions</label>
                  <Textarea
                    value={localSettings.instructions}
                    onChange={(e) =>
                      setLocalSettings({
                        ...localSettings,
                        instructions: e.target.value,
                      })
                    }
                    placeholder="Enter specific instructions..."
                    className="min-h-[200px] font-mono"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Custom Instructions
                  </label>
                  <Textarea
                    value={localSettings.customInstructions}
                    onChange={(e) =>
                      setLocalSettings({
                        ...localSettings,
                        customInstructions: e.target.value,
                      })
                    }
                    placeholder="Enter any custom instructions or rules..."
                    className="min-h-[150px] font-mono"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="basic">
              <div className="space-y-6 pt-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Model</label>
                  {agentId === "imageGenerator" ? (
                    <Select
                      value={localSettings.model}
                      onValueChange={(value) =>
                        setLocalSettings({ ...localSettings, model: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="imagen-3.0-generate-002">
                          Imagen 3.0 Generate 002 (Best Quality)
                        </SelectItem>
                        <SelectItem value="imagen-3.0-generate-001">
                          Imagen 3.0 Generate 001
                        </SelectItem>
                        <SelectItem value="imagen-3.0-fast-generate-001">
                          Imagen 3.0 Fast Generate 001
                        </SelectItem>
                        <SelectItem value="imagegeneration@006">
                          Image Generation 006
                        </SelectItem>
                        <SelectItem value="imagegeneration@005">
                          Image Generation 005
                        </SelectItem>
                        <SelectItem value="imagegeneration@002">
                          Image Generation 002
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Select
                      value={localSettings.model}
                      onValueChange={(value) =>
                        setLocalSettings({ ...localSettings, model: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gemini-2.0-flash-002">
                          Gemini 1.5 Flash 002
                        </SelectItem>
                        <SelectItem value="gemini-2.0-flash-001">
                          Gemini 1.5 Flash 001
                        </SelectItem>
                        <SelectItem value="gemini-1.5-pro-002">
                          Gemini 1.5 Pro 002
                        </SelectItem>
                        <SelectItem value="gemini-1.5-pro-001">
                          Gemini 1.5 Pro 001
                        </SelectItem>
                        <SelectItem value="gemini-1.0-pro-vision-001">
                          Gemini 1.0 Pro Vision 001
                        </SelectItem>
                        <SelectItem value="gemini-1.0-pro-002">
                          Gemini 1.0 Pro 002
                        </SelectItem>
                        <SelectItem value="gemini-1.0-pro-001">
                          Gemini 1.0 Pro 001
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>

                {agentId === "imageGenerator" ? (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Sample Count
                      </label>
                      <div className="flex items-center gap-4">
                        <Slider
                          value={[localSettings.sampleCount || 1]}
                          onValueChange={([value]) =>
                            setLocalSettings({
                              ...localSettings,
                              sampleCount: value,
                            })
                          }
                          min={1}
                          max={
                            localSettings.model === "imagegeneration@002"
                              ? 8
                              : 4
                          }
                          step={1}
                          className="w-full"
                        />
                        <span className="text-sm w-12 text-right">
                          {localSettings.sampleCount || 1}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Aspect Ratio
                      </label>
                      <Select
                        value={localSettings.aspectRatio || "1:1"}
                        onValueChange={(
                          value: "1:1" | "9:16" | "16:9" | "3:4" | "4:3"
                        ) =>
                          setLocalSettings({
                            ...localSettings,
                            aspectRatio: value,
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select aspect ratio" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1:1">1:1 (Square)</SelectItem>
                          {localSettings.model !== "imagegeneration@002" && (
                            <>
                              <SelectItem value="9:16">
                                9:16 (Portrait)
                              </SelectItem>
                              {localSettings.model !==
                                "imagegeneration@005" && (
                                <>
                                  <SelectItem value="16:9">
                                    16:9 (Landscape)
                                  </SelectItem>
                                  <SelectItem value="3:4">3:4</SelectItem>
                                  <SelectItem value="4:3">4:3</SelectItem>
                                </>
                              )}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {[
                      "imagen-3.0-generate-002",
                      "imagen-3.0-generate-001",
                      "imagen-3.0-fast-generate-001",
                      "imagegeneration@006",
                    ].includes(localSettings.model) && (
                      <>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">
                            Person Generation
                          </label>
                          <Select
                            value={
                              localSettings.personGeneration || "allow_adult"
                            }
                            onValueChange={(
                              value: "dont_allow" | "allow_adult" | "allow_all"
                            ) =>
                              setLocalSettings({
                                ...localSettings,
                                personGeneration: value,
                              })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select person generation setting" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="dont_allow">
                                Don&apos;t Allow People
                              </SelectItem>
                              <SelectItem value="allow_adult">
                                Allow Adults Only
                              </SelectItem>
                              <SelectItem value="allow_all">
                                Allow All Ages
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">
                            Safety Setting
                          </label>
                          <Select
                            value={
                              localSettings.safetySetting ||
                              "block_medium_and_above"
                            }
                            onValueChange={(
                              value:
                                | "block_low_and_above"
                                | "block_medium_and_above"
                                | "block_only_high"
                                | "block_none"
                            ) =>
                              setLocalSettings({
                                ...localSettings,
                                safetySetting: value,
                              })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select safety setting" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="block_low_and_above">
                                Strongest Filtering
                              </SelectItem>
                              <SelectItem value="block_medium_and_above">
                                Medium Filtering
                              </SelectItem>
                              <SelectItem value="block_only_high">
                                Light Filtering
                              </SelectItem>
                              <SelectItem value="block_none">
                                Minimal Filtering
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </>
                    )}

                    {localSettings.model === "imagegeneration@002" && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Sample Image Style
                        </label>
                        <Select
                          value={localSettings.sampleImageStyle || "photograph"}
                          onValueChange={(
                            value:
                              | "photograph"
                              | "digital_art"
                              | "landscape"
                              | "sketch"
                              | "watercolor"
                              | "cyberpunk"
                              | "pop_art"
                          ) =>
                            setLocalSettings({
                              ...localSettings,
                              sampleImageStyle: value,
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select image style" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="photograph">
                              Photograph
                            </SelectItem>
                            <SelectItem value="digital_art">
                              Digital Art
                            </SelectItem>
                            <SelectItem value="landscape">Landscape</SelectItem>
                            <SelectItem value="sketch">Sketch</SelectItem>
                            <SelectItem value="watercolor">
                              Watercolor
                            </SelectItem>
                            <SelectItem value="cyberpunk">Cyberpunk</SelectItem>
                            <SelectItem value="pop_art">Pop Art</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Temperature</label>
                      <div className="flex items-center gap-4">
                        <Slider
                          value={[localSettings.temperature ?? 0]}
                          onValueChange={([value]) =>
                            setLocalSettings({
                              ...localSettings,
                              temperature: value,
                            })
                          }
                          min={0}
                          max={2}
                          step={0.1}
                          className="w-full"
                        />
                        <span className="text-sm w-12 text-right">
                          {localSettings.temperature}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Max Tokens</label>
                      <div className="flex items-center gap-4">
                        <Slider
                          value={[localSettings.maxTokens ?? 100]}
                          onValueChange={([value]) =>
                            setLocalSettings({
                              ...localSettings,
                              maxTokens: value,
                            })
                          }
                          min={100}
                          max={4000}
                          step={100}
                          className="w-full"
                        />
                        <span className="text-sm w-12 text-right">
                          {localSettings.maxTokens}
                        </span>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

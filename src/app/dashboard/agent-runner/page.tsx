"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useAgents } from "@/hooks/agents-context";
import { db } from "@/lib/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  limit,
} from "firebase/firestore";
import { PlayIcon, StopIcon } from "@radix-ui/react-icons";
import textAnalyzer from "@/lib/agents/textAnalyzer";
import sentimentAnalyzer from "@/lib/agents/sentimentAnalyzer";
import storyGenerator from "@/lib/agents/storyGenerator";
import qualityChecker from "@/lib/agents/qualityChecker";
import cultureChecker from "@/lib/agents/cultureChecker";
import artGenerator from "@/lib/agents/artGenerator";
import { AgentSettings, InstagramPost } from "@/types";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import imageGenerator from "@/lib/agents/imageGenerator";
import Image from "next/image";
import { LucideLoader } from "lucide-react";

interface AgentStats {
  ready: number;
  pending: number;
  processed: number;
  failed: number;
  isLoading: boolean;
  percentage: number;
  avgProcessingTime: number;
  processingTimes: number[];
  totalPosts: number;
  remaining: number;
  lastProcessedAt?: Date;
}

type AgentType = {
  key: string;
  fn: (settings: AgentSettings, post: InstagramPost) => Promise<string>;
  name: string;
  completed: string;
};

const agentOrder: AgentType[] = [
  {
    key: "textAnalyzer",
    fn: (settings: AgentSettings, post: InstagramPost) =>
      textAnalyzer(settings, post),
    name: "Text Analyzer",
    completed: "text_analyzing",
  },
  {
    key: "sentimentAnalyzer",
    fn: (settings: AgentSettings, post: InstagramPost) =>
      sentimentAnalyzer(settings, post),
    name: "Sentiment Analyzer",
    completed: "sentiment_analyzing",
  },
  {
    key: "storyGenerator",
    fn: (settings: AgentSettings, post: InstagramPost) =>
      storyGenerator(settings, post),
    name: "Story Generator",
    completed: "story_generating",
  },
  {
    key: "qualityChecker",
    fn: (settings: AgentSettings, post: InstagramPost) =>
      qualityChecker(settings, post),
    name: "Quality Checker",
    completed: "quality_checking",
  },
  {
    key: "cultureChecker",
    fn: (settings: AgentSettings, post: InstagramPost) =>
      cultureChecker(settings, post),
    name: "Culture Fit",
    completed: "culture_checking",
  },
  {
    key: "artGenerator",
    fn: (settings: AgentSettings, post: InstagramPost) =>
      artGenerator(settings, post),
    name: "Art Director",
    completed: "art_generating",
  },
  {
    key: "imageGenerator",
    fn: async (settings: AgentSettings, post: InstagramPost) =>
      JSON.stringify(await imageGenerator(settings, post)),
    name: "Image Generator",
    completed: "image_generating",
  },
];

interface AgentResultData {
  processed_at?: string;
  processing_time?: number;
  overall_score?: number;
  alignment_score?: number;
  sentiment_score?: number;
  content?: string;
  caption?: string;
  storageUrl?: string;
  prompt?: string;
  analysis?: {
    sentiment?: string;
    topics?: string[];
    keywords?: string[];
    summary?: string;
  };
}

interface LogMessage {
  id: string;
  type: "info" | "success" | "warning" | "error";
  message: string;
  details?: string;
  timestamp: Date;
  results?: {
    agent: string;
    data: AgentResultData;
  };
}

export default function AgentRunnerPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [currentAgent, setCurrentAgent] = useState<string | null>(null);
  const [currentImage, setCurrentImage] = useState<{
    storageUrl: string;
    caption: string;
  } | null>(null);
  const [agentStats, setAgentStats] = useState<Record<string, AgentStats>>(
    Object.fromEntries(
      agentOrder.map((agent) => [
        agent.key,
        {
          ready: 0,
          pending: 0,
          processed: 0,
          failed: 0,
          isLoading: false,
          percentage: 0,
          avgProcessingTime: 0,
          processingTimes: [],
          totalPosts: 0,
          remaining: 0,
        },
      ])
    )
  );
  const { agents } = useAgents();
  const [logs, setLogs] = useState<LogMessage[]>([]);
  const [showConsole, setShowConsole] = useState(true);
  const consoleRef = useRef<HTMLDivElement>(null);
  const [isConsoleHovered, setIsConsoleHovered] = useState(false);
  const isRunningRef = useRef(false);

  const addLog = (
    message: string,
    type: LogMessage["type"] = "info",
    details?: string,
    results?: { agent: string; data: AgentResultData }
  ) => {
    const log = {
      id: `${Date.now()}-${Math.random()}`,
      type,
      message,
      details,
      timestamp: new Date(),
      results,
    };
    setLogs((prev) => [log, ...prev]);
  };

  useEffect(() => {
    if (!consoleRef.current || isConsoleHovered) return;

    const scrollToTop = () => {
      if (consoleRef.current) {
        consoleRef.current.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
    };

    const observer = new MutationObserver(scrollToTop);

    observer.observe(consoleRef.current, {
      childList: true,
      subtree: true,
    });

    scrollToTop();

    return () => observer.disconnect();
  }, [logs, isConsoleHovered]);

  useEffect(() => {
    // Inject the animation styles
    const style = document.createElement("style");
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const processPost = async (
    post: InstagramPost & { docId: string },
    agent: AgentType
  ) => {
    const startTime = Date.now();
    try {
      const agentSettings = agents[agent.key];
      if (!agentSettings?.enabled) {
        addLog(`${agent.name} is disabled, skipping...`, "warning");
        return false;
      }

      addLog(`Processing post ${post.id} with ${agent.name}...`, "info");

      const result = await agent.fn(agentSettings, post);
      const parsedResult = JSON.parse(result);

      if (parsedResult) {
        const postRef = doc(db, "social_media_posts", post.docId);
        const processingTime = Date.now() - startTime;

        // Add log with agent results
        addLog(
          `${agent.name} completed post ${post.id}`,
          "success",
          `Processed in ${(processingTime / 1000).toFixed(1)}s`,
          {
            agent: agent.name,
            data: parsedResult,
          }
        );

        // Update agent stats with new processing time and last processed timestamp
        setAgentStats((prev) => ({
          ...prev,
          [agent.key]: {
            ...prev[agent.key],
            processingTimes: [
              ...(prev[agent.key].processingTimes || []),
              processingTime,
            ],
            avgProcessingTime:
              (prev[agent.key].avgProcessingTime * prev[agent.key].processed +
                processingTime) /
              (prev[agent.key].processed + 1),
            lastProcessedAt: new Date(),
          },
        }));

        // Handle image generator specific updates
        if (agent.key === "imageGenerator" && parsedResult.storageUrl) {
          setCurrentImage({
            storageUrl: parsedResult.storageUrl,
            caption: parsedResult.caption,
          });
        }

        await updateDoc(postRef, {
          status: agent.completed,
          agents: {
            ...post.agents,
            [agent.key]: {
              ...parsedResult,
              processed_at: new Date().toISOString(),
              processing_time: processingTime,
            },
          },
          updated_at: new Date().toISOString(),
        });

        return true;
      }

      throw new Error(`Failed to get ${agent.name} results`);
    } catch (err) {
      console.error(`Error in ${agent.name}:`, err);
      addLog(
        `Error processing post ${post.id} with ${agent.name}`,
        "error",
        err instanceof Error ? err.message : "Unknown error"
      );

      const postRef = doc(db, "social_media_posts", post.docId);
      await updateDoc(postRef, {
        status: "failed",
        agents: {
          ...post.agents,
          [agent.key]: null,
        },
      });

      return false;
    }
  };

  const stopProcessing = async () => {
    addLog("Stopping all agents...", "warning");
    isRunningRef.current = false;
    setIsRunning(false);
    setCurrentAgent(null);

    // Reset all agent processing states
    setAgentStats((prev) => {
      const newStats = { ...prev };
      Object.keys(newStats).forEach((key) => {
        newStats[key] = {
          ...newStats[key],
          lastProcessedAt: undefined, // Clear processing indicator
        };
      });
      return newStats;
    });

    // Wait for any in-progress operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    addLog("All agents stopped successfully", "success");
    await fetchAgentStats();
  };

  const runAgents = async () => {
    if (isRunningRef.current) return;

    setIsRunning(true);
    isRunningRef.current = true;
    addLog("Starting sequential agent runner...", "info");

    try {
      // Process agents one by one
      for (const agent of agentOrder) {
        if (!isRunningRef.current) {
          addLog("Processing stopped by user", "warning");
          break;
        }

        if (!agents[agent.key]?.enabled) {
          addLog(`Agent ${agent.name} is disabled, skipping`, "warning");
          continue;
        }

        setCurrentAgent(agent.name);
        addLog(`Starting ${agent.name} processing`, "info");

        // Process all posts for current agent
        while (isRunningRef.current) {
          const postsRef = collection(db, "social_media_posts");
          let q;

          // Query for first agent
          if (agent.key === "textAnalyzer") {
            q = query(
              postsRef,
              where("agents.textAnalyzer", "==", null),
              limit(10)
            );
          } else {
            // For other agents, check if previous agent has processed
            const prevAgentKey =
              agentOrder[agentOrder.findIndex((a) => a.key === agent.key) - 1]
                .key;
            q = query(
              postsRef,
              where(`agents.${agent.key}`, "==", null),
              where(`agents.${prevAgentKey}`, "!=", null),
              limit(10)
            );
          }

          const snapshot = await getDocs(q);
          if (snapshot.empty || !isRunningRef.current) {
            break;
          }

          // Check if stopped before processing batch
          if (!isRunningRef.current) {
            break;
          }

          addLog(
            `Processing batch of ${snapshot.size} posts with ${agent.name}`,
            "info"
          );

          // Process current batch in parallel for faster processing
          const processingPromises = snapshot.docs.map(async (doc) => {
            // Check if stopped before each post
            if (!isRunningRef.current) {
              return;
            }

            const postData = {
              docId: doc.id,
              ...(doc.data() as InstagramPost),
            } as InstagramPost & { docId: string };

            try {
              addLog(`${agent.name} processing post ${postData.id}`, "info");
              const success = await processPost(postData, agent);
              if (success && isRunningRef.current) {
                addLog(
                  `${agent.name} completed post ${postData.id}`,
                  "success"
                );
              }
            } catch (error) {
              if (isRunningRef.current) {
                addLog(
                  `Error in ${agent.name} processing post ${postData.id}`,
                  "error",
                  error instanceof Error ? error.message : "Unknown error"
                );
              }
            }
          });

          await Promise.all(processingPromises);

          if (!isRunningRef.current) {
            break;
          }

          await fetchAgentStats();
          await new Promise((resolve) => setTimeout(resolve, 500));
        }

        if (!isRunningRef.current) {
          break;
        }
      }

      if (isRunningRef.current) {
        addLog("All agents completed processing!", "success");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      addLog("Error in processing loop", "error", errorMessage);
    } finally {
      setIsRunning(false);
      isRunningRef.current = false;
      setCurrentAgent(null);
      await fetchAgentStats();
    }
  };

  const fetchAgentStats = async () => {
    // First, get the total posts in the system
    const postsRef = collection(db, "social_media_posts");
    const totalPostsQuery = query(postsRef);
    const totalPostsSnapshot = await getDocs(totalPostsQuery);
    const systemTotalPosts = totalPostsSnapshot.size;

    for (const agent of agentOrder) {
      try {
        setAgentStats((prev) => ({
          ...prev,
          [agent.key]: { ...prev[agent.key], isLoading: true },
        }));

        // Get processed posts for this agent
        const processedQuery = query(
          postsRef,
          where(`agents.${agent.key}`, "!=", null)
        );
        const processedSnapshot = await getDocs(processedQuery);
        const processedCount = processedSnapshot.size;

        // Get remaining posts (those not processed by current agent)
        const remainingQuery = query(
          postsRef,
          where(`agents.${agent.key}`, "==", null),
          agent.key === "textAnalyzer"
            ? where("status", "in", ["pending", "ready"])
            : where(
                `agents.${
                  agentOrder[
                    agentOrder.findIndex((a) => a.key === agent.key) - 1
                  ].key
                }`,
                "!=",
                null
              )
        );
        const remainingSnapshot = await getDocs(remainingQuery);
        const remainingCount = remainingSnapshot.size;

        // Calculate percentage based on total system posts
        const percentage = (processedCount / systemTotalPosts) * 100;

        let totalProcessingTime = 0;
        processedSnapshot.forEach((doc) => {
          const data = doc.data();
          if (data.agents?.[agent.key]?.processing_time) {
            totalProcessingTime += data.agents[agent.key].processing_time;
          }
        });

        const avgProcessingTime =
          processedCount > 0 ? totalProcessingTime / processedCount : 0;

        setAgentStats((prev) => ({
          ...prev,
          [agent.key]: {
            ready: remainingCount,
            pending: 0,
            processed: processedCount,
            failed: 0,
            isLoading: false,
            percentage,
            avgProcessingTime,
            processingTimes: prev[agent.key].processingTimes || [],
            totalPosts: systemTotalPosts,
            remaining: remainingCount,
          },
        }));
      } catch (error) {
        console.error(`Error fetching stats for ${agent.key}:`, error);
        setAgentStats((prev) => ({
          ...prev,
          [agent.key]: { ...prev[agent.key], isLoading: false },
        }));
      }
    }
  };

  useEffect(() => {
    fetchAgentStats();
    const interval = setInterval(fetchAgentStats, 2000);
    return () => clearInterval(interval);
  }, []);

  const AgentCard = ({
    agent,
    stats,
    isActive,
    isRecentlyProcessed,
  }: {
    agent: AgentType;
    stats: AgentStats;
    isActive: boolean;
    isRecentlyProcessed: boolean;
  }) => {
    return (
      <Card
        className={cn(
          `${
            agent.key === "imageGenerator"
              ? "col-span-1 md:col-span-2 lg:col-span-3 xl:col-span-2"
              : ""
          }`,
          "transition-all duration-500",
          isActive && "ring-2 ring-primary",
          isRecentlyProcessed &&
            isRunning &&
            "bg-green-500/10 ring-2 ring-green-500/80"
        )}
      >
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Agent Header */}
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-2 justify-between w-full">
                <h3 className="font-medium text-xs">{agent.name}</h3>
                <div className="flex items-center gap-2">
                  {agents[agent.key]?.enabled && (
                    <Badge
                      variant={
                        isRecentlyProcessed && isRunning ? "success" : "outline"
                      }
                      className="text-[9px]"
                    >
                      {isRecentlyProcessed && isRunning ? (
                        <span className="text-black">Saving</span>
                      ) : (
                        "Active"
                      )}
                    </Badge>
                  )}
                  {isActive && (
                    <LucideLoader className="h-4 w-4 animate-spin text-primary" />
                  )}
                </div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <Progress
                value={stats.percentage}
                className={cn(
                  "h-2 transition-all",
                  isActive && "bg-primary/20"
                )}
              />
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  {stats.processed} of {stats.totalPosts}
                </span>
                <span className="font-medium">
                  {stats.percentage.toFixed(1)}%
                </span>
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Remaining</span>
                <span className="font-medium">{stats.remaining}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Avg. Time</span>
                <span className="font-medium">
                  {stats.avgProcessingTime > 0
                    ? `${(stats.avgProcessingTime / 1000).toFixed(1)}s`
                    : "N/A"}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Agent Runner</h2>
              <p className="text-sm text-muted-foreground">
                Run agents sequentially on posts missing analysis
              </p>
            </div>
            <div className="flex items-center gap-2">
              {isRunning ? (
                <Button onClick={stopProcessing} variant="destructive">
                  <StopIcon className="mr-2" />
                  Stop Processing
                </Button>
              ) : (
                <Button onClick={runAgents}>
                  <PlayIcon className="mr-2" />
                  Start Processing
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Overall Progress */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="text-sm font-medium">Pipeline Progress</div>
                  <div className="text-sm text-muted-foreground">
                    Average completion across all agents
                  </div>
                </div>
                <Badge variant="outline">
                  {(
                    Object.values(agentStats).reduce(
                      (acc, stat) => acc + stat.percentage,
                      0
                    ) / agentOrder.length
                  ).toFixed(1)}
                  %
                </Badge>
              </div>
              <Progress
                value={
                  Object.values(agentStats).reduce(
                    (acc, stat) => acc + stat.percentage,
                    0
                  ) / agentOrder.length
                }
                className="h-2"
              />
            </div>

            {/* Agent Status Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {agentOrder.map((agent) => {
                const stats = agentStats[agent.key];
                const isActive = currentAgent === agent.name && isRunning;
                const isRecentlyProcessed =
                  stats.lastProcessedAt &&
                  Date.now() - stats.lastProcessedAt.getTime() < 5000;

                return (
                  <AgentCard
                    key={agent.key}
                    agent={agent}
                    stats={stats}
                    isActive={isActive}
                    isRecentlyProcessed={isRecentlyProcessed || false}
                  />
                );
              })}
              {/* Image Preview */}
              <Card className="col-span-1 md:col-span-2 lg:col-span-3 xl:col-span-4">
                <CardContent>
                  {currentImage ? (
                    <div className="space-y-4 grid grid-cols-1 gap-4">
                      <div className="relative aspect-video rounded-lg overflow-hidden bg-background">
                        <Image
                          src={currentImage.storageUrl}
                          alt="Generated image"
                          className="object-contain"
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                        />
                      </div>
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Caption</h4>
                        <p className="text-sm text-muted-foreground">
                          {currentImage.caption}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center min-h-[280px] text-sm text-muted-foreground">
                      No image generated yet. Start processing to see results.
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Console and Image Preview */}
      {/* Console */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="space-y-1">
            <h3 className="text-lg font-medium">Processing Console</h3>
            <p className="text-sm text-muted-foreground">
              Real-time processing logs and results
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => setLogs([])}>
              Clear
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowConsole((prev) => !prev)}
            >
              {showConsole ? "Hide" : "Show"}
            </Button>
          </div>
        </CardHeader>
        {showConsole && (
          <CardContent>
            <ScrollArea
              ref={consoleRef}
              className="h-[500px] rounded-md border bg-muted p-4 font-mono text-sm"
              onMouseEnter={() => setIsConsoleHovered(true)}
              onMouseLeave={() => setIsConsoleHovered(false)}
            >
              {logs.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  No logs yet. Start processing to see activity.
                </div>
              ) : (
                <div className="space-y-2">
                  {logs.map((log) => (
                    <div
                      key={log.id}
                      className={cn(
                        "rounded border bg-background p-2 transition-colors hover:bg-accent",
                        {
                          "text-destructive border-destructive/50":
                            log.type === "error",
                          "text-green-500 border-green-500/50":
                            log.type === "success",
                          "text-yellow-500 border-yellow-500/50":
                            log.type === "warning",
                          "text-muted-foreground": log.type === "info",
                        }
                      )}
                    >
                      <div className="flex items-start gap-2">
                        <span className="whitespace-nowrap text-xs opacity-50">
                          {log.timestamp.toLocaleTimeString()}
                        </span>
                        <span className="flex-1 break-all">{log.message}</span>
                      </div>
                      {log.details && (
                        <div className="mt-1 ml-[4.5rem] text-xs text-muted-foreground break-all">
                          {log.details}
                        </div>
                      )}
                      {log.results && (
                        <div className="mt-2 ml-[4.5rem] text-xs">
                          <div className="font-medium mb-1">
                            Results from {log.results.agent}:
                          </div>
                          <pre className="bg-muted p-2 rounded overflow-x-auto whitespace-pre-wrap break-all">
                            {JSON.stringify(log.results.data, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        )}
      </Card>
    </div>
  );
}

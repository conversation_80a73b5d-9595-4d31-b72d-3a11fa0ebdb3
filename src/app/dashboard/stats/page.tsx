"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { db } from "@/lib/firebase";
import { collection, getDocs } from "firebase/firestore";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Legend,
} from "recharts";
import {
  ArrowUpIcon,
  ReloadIcon,
  RocketIcon,
  BarChartIcon,
  HeartIcon,
  TimerIcon,
  PersonIcon,
  Link2Icon,
  StarIcon,
  StarFilledIcon,
} from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface AgentStats {
  total: number;
  completed: number;
  pending: number;
  failed: number;
  percentage: number;
  avgProcessingTime: number;
  processingTimes: number[];
  lastError?: string;
}

interface ContentMetrics {
  totalPosts: number;
  avgQualityScore: number;
  avgCultureScore: number;
  avgSentimentScore: number;
  topPerformingContent: Array<{
    id: string;
    caption: string;
    engagement: number;
    qualityScore: number;
  }>;
  contentTypeDistribution: Array<{
    type: string;
    count: number;
  }>;
  hashtagStats: Array<{
    hashtag: string;
    count: number;
    avgEngagement: number;
  }>;
}

interface EngagementMetrics {
  totalLikes: number;
  totalComments: number;
  avgEngagementRate: number;
  engagementTrend: Array<{
    date: string;
    likes: number;
    comments: number;
    total: number;
  }>;
  topEngagingUsers: Array<{
    username: string;
    engagement: number;
    postCount: number;
  }>;
  hourlyEngagement: Array<{
    hour: number;
    engagement: number;
  }>;
}

type Stats = {
  [key: string]: AgentStats;
};

interface AgentData {
  processed_at?: string;
  overall_score?: number;
  alignment_score?: number;
  sentiment_score?: number;
  error?: string;
}

const defaultStats: AgentStats = {
  total: 0,
  completed: 0,
  pending: 0,
  failed: 0,
  percentage: 0,
  avgProcessingTime: 0,
  processingTimes: [],
};

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

export default function StatsPage() {
  const [stats, setStats] = useState<Stats>({
    textAnalyzer: { ...defaultStats },
    sentimentAnalyzer: { ...defaultStats },
    storyGenerator: { ...defaultStats },
    qualityChecker: { ...defaultStats },
    cultureChecker: { ...defaultStats },
    artGenerator: { ...defaultStats },
    imageGenerator: { ...defaultStats },
  });
  const [contentMetrics, setContentMetrics] = useState<ContentMetrics>({
    totalPosts: 0,
    avgQualityScore: 0,
    avgCultureScore: 0,
    avgSentimentScore: 0,
    topPerformingContent: [],
    contentTypeDistribution: [],
    hashtagStats: [],
  });
  const [engagementMetrics, setEngagementMetrics] = useState<EngagementMetrics>(
    {
      totalLikes: 0,
      totalComments: 0,
      avgEngagementRate: 0,
      engagementTrend: [],
      topEngagingUsers: [],
      hourlyEngagement: [],
    }
  );
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [loading, setLoading] = useState(true);

  async function fetchStats() {
    try {
      setLoading(true);
      const postsRef = collection(db, "social_media_posts");
      const snapshot = await getDocs(postsRef);
      const total = snapshot.size;

      const newStats: Stats = {
        textAnalyzer: { ...defaultStats },
        sentimentAnalyzer: { ...defaultStats },
        storyGenerator: { ...defaultStats },
        qualityChecker: { ...defaultStats },
        cultureChecker: { ...defaultStats },
        artGenerator: { ...defaultStats },
        imageGenerator: { ...defaultStats },
      };

      // Initialize metrics tracking
      let totalQualityScore = 0;
      let totalCultureScore = 0;
      let totalSentimentScore = 0;
      let totalLikes = 0;
      let totalComments = 0;
      const contentTypes = new Map<string, number>();
      const hashtags = new Map<string, { count: number; engagement: number }>();
      const hourlyEngagement = new Array(24).fill(0);
      const userEngagement = new Map<
        string,
        { engagement: number; posts: number }
      >();
      const processingTimesByAgent = new Map<string, number[]>();
      const dailyEngagement = new Map<
        string,
        { likes: number; comments: number }
      >();

      // Initialize agent processing times
      Object.keys(newStats).forEach((agentKey) => {
        processingTimesByAgent.set(agentKey, []);
        newStats[agentKey].total = total;
      });

      // Process each document
      snapshot.forEach((doc) => {
        const data = doc.data();
        const agents = data.agents || {};
        const status = data.status || "pending";
        const timestamp = new Date(data.timestamp);
        const dateKey = timestamp.toISOString().split("T")[0];
        const hour = timestamp.getHours();

        // Track content type distribution
        contentTypes.set(
          data.content_type,
          (contentTypes.get(data.content_type) || 0) + 1
        );

        // Track engagement metrics
        const postLikes = data.like_count || 0;
        const postComments = data.comment_count || 0;
        totalLikes += postLikes;
        totalComments += postComments;
        hourlyEngagement[hour] += postLikes + postComments;

        // Track daily engagement
        const dailyStats = dailyEngagement.get(dateKey) || {
          likes: 0,
          comments: 0,
        };
        dailyEngagement.set(dateKey, {
          likes: dailyStats.likes + postLikes,
          comments: dailyStats.comments + postComments,
        });

        // Track user engagement
        const userStats = userEngagement.get(data.user_username) || {
          engagement: 0,
          posts: 0,
        };
        userEngagement.set(data.user_username, {
          engagement: userStats.engagement + postLikes + postComments,
          posts: userStats.posts + 1,
        });

        // Track hashtags
        (data.hashtags || []).forEach((tag: string) => {
          const tagStats = hashtags.get(tag) || { count: 0, engagement: 0 };
          hashtags.set(tag, {
            count: tagStats.count + 1,
            engagement: tagStats.engagement + postLikes + postComments,
          });
        });

        // Process each agent's stats
        Object.entries(agents).forEach(([agentKey, agentData]) => {
          const typedAgentData = agentData as AgentData;
          if (!typedAgentData) return;

          if (typedAgentData.processed_at) {
            const processTime =
              new Date(typedAgentData.processed_at).getTime() -
              new Date(data.created_at).getTime();
            const times = processingTimesByAgent.get(agentKey) || [];
            times.push(processTime);
            processingTimesByAgent.set(agentKey, times);

            let hasValidResults = false;
            switch (agentKey) {
              case "qualityChecker":
                hasValidResults =
                  typeof typedAgentData.overall_score === "number";
                if (hasValidResults)
                  totalQualityScore += typedAgentData.overall_score || 0;
                break;
              case "cultureChecker":
                hasValidResults =
                  typeof typedAgentData.alignment_score === "number";
                if (hasValidResults)
                  totalCultureScore += typedAgentData.alignment_score || 0;
                break;
              case "sentimentAnalyzer":
                hasValidResults =
                  typeof typedAgentData.sentiment_score === "number";
                if (hasValidResults)
                  totalSentimentScore += typedAgentData.sentiment_score || 0;
                break;
              default:
                hasValidResults = true;
                break;
            }

            if (hasValidResults) {
              newStats[agentKey].completed++;
            } else {
              newStats[agentKey].failed++;
              // Track the last error if available
              if (typedAgentData.error) {
                newStats[agentKey].lastError = typedAgentData.error;
              }
            }
          } else if (status === "processing") {
            newStats[agentKey].pending++;
          }
        });
      });

      // Calculate agent-specific metrics
      Object.keys(newStats).forEach((agentKey) => {
        const times = processingTimesByAgent.get(agentKey) || [];
        newStats[agentKey].processingTimes = times;
        newStats[agentKey].avgProcessingTime =
          times.length > 0
            ? times.reduce((a, b) => a + b, 0) / times.length
            : 0;
        newStats[agentKey].percentage =
          newStats[agentKey].total > 0
            ? (newStats[agentKey].completed / newStats[agentKey].total) * 100
            : 0;
      });

      // Prepare engagement trend data
      const engagementTrendData = Array.from(dailyEngagement.entries())
        .sort((a, b) => a[0].localeCompare(b[0]))
        .map(([date, stats]) => ({
          date,
          likes: stats.likes,
          comments: stats.comments,
          total: stats.likes + stats.comments,
        }));

      // Prepare hourly engagement data
      const hourlyEngagementData = hourlyEngagement.map((value, hour) => ({
        hour,
        engagement: value,
      }));

      // Prepare content type distribution
      const contentTypeData = Array.from(contentTypes.entries())
        .map(([type, count]) => ({ type, count }))
        .sort((a, b) => b.count - a.count);

      // Prepare hashtag stats
      const hashtagData = Array.from(hashtags.entries())
        .map(([hashtag, stats]) => ({
          hashtag,
          count: stats.count,
          avgEngagement: stats.engagement / stats.count,
        }))
        .sort((a, b) => b.avgEngagement - a.avgEngagement)
        .slice(0, 10);

      // Prepare top engaging users
      const topUsers = Array.from(userEngagement.entries())
        .map(([username, stats]) => ({
          username,
          engagement: stats.engagement,
          postCount: stats.posts,
        }))
        .sort((a, b) => b.engagement - a.engagement)
        .slice(0, 5);

      // Update all metrics
      setStats(newStats);
      setContentMetrics({
        totalPosts: total,
        avgQualityScore: totalQualityScore / total,
        avgCultureScore: totalCultureScore / total,
        avgSentimentScore: totalSentimentScore / total,
        topPerformingContent: [], // Implemented in the UI
        contentTypeDistribution: contentTypeData,
        hashtagStats: hashtagData,
      });
      setEngagementMetrics({
        totalLikes,
        totalComments,
        avgEngagementRate: (totalLikes + totalComments) / total,
        engagementTrend: engagementTrendData,
        topEngagingUsers: topUsers,
        hourlyEngagement: hourlyEngagementData,
      });

      setLastUpdated(new Date());
    } catch (error) {
      console.error("Error fetching stats:", error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchStats();
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const agentDisplayNames: { [key: string]: string } = {
    textAnalyzer: "Text Analyzer",
    sentimentAnalyzer: "Sentiment Analyzer",
    storyGenerator: "Story Generator",
    qualityChecker: "Quality Assurance",
    cultureChecker: "Culture Fit",
    artGenerator: "Art Director",
    imageGenerator: "Image Generator",
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[200px]">
        <ReloadIcon className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
        <Button asChild>
          <Link href="/dashboard/analytics">
            <StarFilledIcon className="h-4 w-4 mr-2" />
            View AI Insights
          </Link>
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Posts
                </p>
                <h2 className="text-3xl font-bold">
                  {contentMetrics.totalPosts}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-primary/10">
                <BarChartIcon className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">12%</span>
              <span className="ml-2">vs last week</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Avg Processing Time
                </p>
                <h2 className="text-3xl font-bold">
                  {(
                    Object.values(stats).reduce(
                      (acc, curr) => acc + curr.avgProcessingTime,
                      0
                    ) /
                    Object.values(stats).length /
                    1000
                  ).toFixed(0)}
                  s
                </h2>
              </div>
              <div className="rounded-full p-2 bg-blue-500/10">
                <TimerIcon className="h-6 w-6 text-blue-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">5%</span>
              <span className="ml-2">faster than avg</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Unique Contributors
                </p>
                <h2 className="text-3xl font-bold">
                  {engagementMetrics.topEngagingUsers.length}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-violet-500/10">
                <PersonIcon className="h-6 w-6 text-violet-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">18%</span>
              <span className="ml-2">more active</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Unique Hashtags
                </p>
                <h2 className="text-3xl font-bold">
                  {contentMetrics.hashtagStats.length}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-orange-500/10">
                <Link2Icon className="h-6 w-6 text-orange-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">24%</span>
              <span className="ml-2">more variety</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Avg Quality Score
                </p>
                <h2 className="text-3xl font-bold">
                  {contentMetrics.avgQualityScore.toFixed(1)}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-green-500/10">
                <StarIcon className="h-6 w-6 text-green-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">8%</span>
              <span className="ml-2">vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Success Rate
                </p>
                <h2 className="text-3xl font-bold">
                  {(
                    (Object.values(stats).reduce(
                      (acc, curr) => acc + curr.completed,
                      0
                    ) /
                      Object.values(stats).reduce(
                        (acc, curr) => acc + curr.total,
                        0
                      )) *
                    100
                  ).toFixed(1)}
                  %
                </h2>
              </div>
              <div className="rounded-full p-2 bg-yellow-500/10">
                <RocketIcon className="h-6 w-6 text-yellow-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">15%</span>
              <span className="ml-2">improvement</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Avg Engagement Rate
                </p>
                <h2 className="text-3xl font-bold">
                  {engagementMetrics.avgEngagementRate.toFixed(1)}%
                </h2>
              </div>
              <div className="rounded-full p-2 bg-red-500/10">
                <HeartIcon className="h-6 w-6 text-red-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">15%</span>
              <span className="ml-2">vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Culture Score
                </p>
                <h2 className="text-3xl font-bold">
                  {contentMetrics.avgCultureScore.toFixed(1)}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-pink-500/10">
                <StarFilledIcon className="h-6 w-6 text-pink-500" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500">10%</span>
              <span className="ml-2">improvement</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Processing Performance */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Processing Performance</h2>
              <p className="text-sm text-muted-foreground">
                Agent processing times and success rates
              </p>
            </div>
            {lastUpdated && (
              <p className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(stats).map(([agentKey, agentStats]) => (
              <Card key={agentKey}>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <div className="text-sm font-medium">
                          {agentDisplayNames[agentKey]}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Progress
                        value={agentStats.percentage}
                        className={cn("h-2", {
                          "bg-red-100": agentStats.failed > 0,
                        })}
                      />
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>
                          {agentStats.percentage.toFixed(1)}% Complete
                        </span>
                        {agentStats.failed > 0 && (
                          <span className="text-red-500">
                            {agentStats.failed} failed
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="font-medium text-green-500">
                        {agentStats.completed}
                      </div>
                      <div className="text-muted-foreground">
                        Posts Analyzed
                      </div>
                      <span>
                        {agentStats.avgProcessingTime > 0 &&
                          `${(agentStats.avgProcessingTime / 1000).toFixed(
                            1
                          )}s`}
                      </span>
                      {agentStats.lastError && (
                        <div className="mt-2 text-sm text-red-500 text-left p-2 bg-red-50 rounded-md">
                          <p className="font-medium">Last Error:</p>
                          <p className="truncate">{agentStats.lastError}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Content Quality Metrics */}
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-semibold">Content Quality Metrics</h2>
          <p className="text-sm text-muted-foreground">
            Analysis of content quality and cultural alignment
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Quality Score</h3>
              <div className="flex items-center justify-between">
                <Progress
                  value={contentMetrics.avgQualityScore * 10}
                  className="h-2 flex-1"
                />
                <span className="ml-2 text-sm font-medium">
                  {contentMetrics.avgQualityScore.toFixed(1)}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Culture Alignment</h3>
              <div className="flex items-center justify-between">
                <Progress
                  value={contentMetrics.avgCultureScore * 10}
                  className="h-2 flex-1"
                />
                <span className="ml-2 text-sm font-medium">
                  {contentMetrics.avgCultureScore.toFixed(1)}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Sentiment Score</h3>
              <div className="flex items-center justify-between">
                <Progress
                  value={(contentMetrics.avgSentimentScore + 1) * 50}
                  className="h-2 flex-1"
                />
                <span className="ml-2 text-sm font-medium">
                  {contentMetrics.avgSentimentScore.toFixed(1)}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Distribution */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">Content Type Distribution</h2>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={contentMetrics.contentTypeDistribution}
                    dataKey="count"
                    nameKey="type"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {contentMetrics.contentTypeDistribution.map(
                      (entry, index) => (
                        <Cell
                          key={entry.type}
                          fill={COLORS[index % COLORS.length]}
                        />
                      )
                    )}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">Top Hashtags</h2>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={contentMetrics.hashtagStats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="hashtag"
                    angle={-45}
                    textAnchor="end"
                    height={70}
                  />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="avgEngagement" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Analytics */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">Engagement Trend</h2>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={engagementMetrics.engagementTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="likes" stroke="#8884d8" />
                  <Line type="monotone" dataKey="comments" stroke="#82ca9d" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">Hourly Engagement</h2>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={engagementMetrics.hourlyEngagement}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="engagement" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Engaging Users */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Top Engaging Users</h2>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {engagementMetrics.topEngagingUsers.map((user, index) => (
              <div
                key={user.username}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2">
                  <div className="text-lg font-bold text-muted-foreground">
                    #{index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{user.username}</div>
                    <div className="text-sm text-muted-foreground">
                      {user.postCount} posts
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">
                    {user.engagement.toLocaleString()} engagements
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {(user.engagement / user.postCount).toFixed(1)} per post
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

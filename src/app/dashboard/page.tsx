"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlayIcon, StopIcon } from "@radix-ui/react-icons";
import { fetchInstagramData } from "@/lib/fetchers/instagram";
import { db } from "@/lib/firebase";
import { InstagramPost } from "@/types";
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp,
  updateDoc,
} from "firebase/firestore";
import { toast } from "@/hooks/use-toast";
import { postStatus } from "@/lib/utils";
import { BadgeProps } from "@/components/ui/badge";
import { Badge } from "@/components/ui/badge";

interface InstagramApiItem {
  id: string;
  caption?: {
    text: string;
    hashtags: string[];
  };
  taken_at: number;
  user?: {
    full_name: string;
    username: string;
    profile_pic_url: string;
    id: string;
  };
  comment_count: number;
  like_count: number;
}

interface BatchHistory {
  id: string;
  hashtag: string;
  started_at: string;
  completed_at: string | null;
  posts_count: number;
  status: "processing" | "completed" | "error";
  pagination_token: string | null;
}

const generateBatchId = () => {
  return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
};

const validatePost = (item: InstagramApiItem): boolean => {
  return !!(
    item.id &&
    item.taken_at &&
    item.user?.id &&
    item.user?.username &&
    item.caption?.text
  );
};

const storeBatchHistory = async (batchHistory: BatchHistory) => {
  const batchesCollection = collection(db, "batch_history");
  await addDoc(batchesCollection, {
    ...batchHistory,
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
  });
};

const updateBatchHistory = async (
  batchId: string,
  updates: Partial<BatchHistory>
) => {
  const batchesCollection = collection(db, "batch_history");
  const batchQuery = query(batchesCollection, where("id", "==", batchId));
  const snapshot = await getDocs(batchQuery);

  if (!snapshot.empty) {
    const docRef = snapshot.docs[0].ref;
    await updateDoc(docRef, {
      ...updates,
      updated_at: Timestamp.now(),
    });
  }
};

export default function DashboardPage() {
  const [hashtag, setHashtag] = useState("");
  const [platform, setPlatform] = useState("instagram");
  const [isProcessing, setIsProcessing] = useState(false);
  const [batchesLimit, setBatchesLimit] = useState(1);
  const [progress, setProgress] = useState({
    initiated: false,
    collected: 0,
    processing: 0,
    completed: 0,
    batches: 0,
  });
  const [posts, setPosts] = useState<InstagramPost[]>([]);
  const [shouldStop, setShouldStop] = useState(false);

  // Fetch last 100 posts on component mount
  useEffect(() => {
    fetchRecentPosts();
  }, []);

  const fetchRecentPosts = async () => {
    const postsCollection = collection(db, "social_media_posts");
    const recentPostsQuery = query(
      postsCollection,
      orderBy("created_at", "desc"),
      limit(10)
    );
    const snapshot = await getDocs(recentPostsQuery);
    const recentPosts = snapshot.docs.map((doc) => doc.data() as InstagramPost);
    setPosts(recentPosts);
  };

  const storeToFirestore = async (newPosts: InstagramPost[]) => {
    const postsCollection = collection(db, "social_media_posts");
    const storedPosts: InstagramPost[] = [];
    for (const post of newPosts) {
      // Check if post already exists
      const existingPostQuery = query(
        postsCollection,
        where("id", "==", post.id)
      );
      const existingPostDocs = await getDocs(existingPostQuery);

      if (existingPostDocs.empty) {
        // Only add if post doesn't exist
        await addDoc(postsCollection, {
          ...post,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          batch: post.batch,
        });
        storedPosts.push(post);
      }
    }

    return storedPosts;
  };

  const handleStartProcessing = async () => {
    if (!hashtag || batchesLimit < 1) {
      toast({
        title: "Please enter a hashtag and set a valid number of batches",
        variant: "destructive",
        color: "red",
      });
      return;
    }
    setIsProcessing(true);
    setShouldStop(false);
    setPosts([]);
    setProgress({
      initiated: true,
      collected: 0,
      processing: 0,
      completed: 0,
      batches: 0,
    });

    try {
      let currentPaginationToken: string | null = null;
      let processedBatches = 0;

      while (processedBatches < batchesLimit && !shouldStop) {
        // Generate unique batch ID
        const batchId = generateBatchId();
        processedBatches++;

        // Create batch history record
        const batchHistory: BatchHistory = {
          id: batchId,
          hashtag,
          started_at: new Date().toISOString(),
          completed_at: null,
          posts_count: 0,
          status: "processing",
          pagination_token: currentPaginationToken,
        };
        await storeBatchHistory(batchHistory);

        // Fetch data with pagination token if available
        const result = await fetchInstagramData(
          hashtag,
          currentPaginationToken || undefined
        );
        const data = JSON.parse(result || "{}");

        if (!data.data?.items?.length) {
          await updateBatchHistory(batchId, {
            completed_at: new Date().toISOString(),
            status: "completed",
            posts_count: 0,
          });
          break;
        }

        // Filter and process valid posts
        const validPosts = data.data.items
          .filter(validatePost)
          .map((item: InstagramApiItem) => ({
            id: item.id,
            caption: item.caption?.text || "",
            platform: "instagram",
            status: "processing",
            batch: batchId,
            timestamp: new Date(item.taken_at * 1000).toISOString(),
            hashtag,
            content_type: "post",
            hashtags: item.caption?.hashtags || [],
            user_full_name: item.user?.full_name || "",
            user_username: item.user?.username || "",
            user_profile_pic_url: item.user?.profile_pic_url || "",
            user_id: item.user?.id || "",
            comment_count: item.comment_count || 0,
            like_count: item.like_count || 0,
            agents: {
              textAnalyzer: null,
              sentimentAnalyzer: null,
              storyGenerator: null,
              qualityChecker: null,
              cultureChecker: null,
              artGenerator: null,
              imageGenerator: null,
            },
          }));

        if (validPosts.length === 0) {
          await updateBatchHistory(batchId, {
            completed_at: new Date().toISOString(),
            status: "completed",
            posts_count: 0,
          });
          if (!data.pagination_token) {
            break;
          }
          currentPaginationToken = data.pagination_token;
          continue;
        }

        // Update progress for processing
        setProgress((prev) => ({
          ...prev,
          processing: prev.processing + validPosts.length,
          batches: processedBatches,
        }));

        try {
          // Store all valid posts in Firestore and get only newly stored posts
          const storedPosts = await storeToFirestore(validPosts);

          // Update batch history with results
          await updateBatchHistory(batchId, {
            completed_at: new Date().toISOString(),
            status: "completed",
            posts_count: storedPosts.length,
            pagination_token: data.pagination_token || null,
          });

          // Update progress after storage
          setProgress((prev) => ({
            ...prev,
            collected: prev.collected + storedPosts.length,
            processing: prev.processing - validPosts.length,
            completed: prev.completed + storedPosts.length,
            batches: processedBatches,
          }));

          // Fetch recent posts again to update the list
          await fetchRecentPosts();

          // Check if we've reached the batch limit
          if (processedBatches >= batchesLimit) {
            break;
          }

          // Update pagination token for next request
          if (data.pagination_token) {
            currentPaginationToken = data.pagination_token;
          } else {
            break;
          }

          // Add a delay to avoid rate limiting
          await new Promise((resolve) => setTimeout(resolve, 1000));
        } catch (error) {
          console.error("Firestore storage error:", error);
          await updateBatchHistory(batchId, {
            completed_at: new Date().toISOString(),
            status: "error",
          });
          setProgress((prev) => ({
            ...prev,
            processing: prev.processing - validPosts.length,
            batches: processedBatches,
          }));
          break; // Stop on error
        }
      }
    } catch (error) {
      console.error("Fetching error:", error);
    } finally {
      setIsProcessing(false);
      setShouldStop(false);
      await fetchRecentPosts();
    }
  };

  const handleStopProcessing = () => {
    setShouldStop(true);
  };

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card className="p-6">
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Social Media Fetcher</h2>
          <p className="text-sm text-muted-foreground">
            Fetch social media posts and process them through AI agents.
          </p>

          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Hashtag</label>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter hashtag to monitor..."
                  value={hashtag}
                  onChange={(e) => setHashtag(e.target.value)}
                  className="flex-1"
                />
                <Input
                  type="number"
                  placeholder="Number of Batches (min 1)"
                  value={batchesLimit}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    setBatchesLimit(value < 1 ? 1 : value);
                  }}
                  className="w-32"
                  min={1}
                  max={50}
                />
                <Button
                  onClick={
                    isProcessing ? handleStopProcessing : handleStartProcessing
                  }
                  variant={isProcessing ? "destructive" : "default"}
                  disabled={!hashtag || batchesLimit < 1}
                >
                  {isProcessing ? (
                    <>
                      <StopIcon className="mr-2 h-4 w-4" />
                      Stop Processing
                    </>
                  ) : (
                    <>
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Start Processing
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="w-48">
              <label className="text-sm font-medium mb-2 block">Platform</label>
              <Select value={platform} onValueChange={setPlatform}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="instagram">
                    Meta (Facebook & Instagram)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </Card>

      {/* Processing Status */}
      {progress.initiated && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Processing Status</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-4">
              <Card className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    Posts Collected
                  </p>
                  <p className="text-2xl font-semibold">{progress.collected}</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">In Processing</p>
                  <p className="text-2xl font-semibold">
                    {progress.processing}
                  </p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p className="text-2xl font-semibold">{progress.completed}</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Batches</p>
                  <p className="text-2xl font-semibold">{progress.batches}</p>
                </div>
              </Card>
            </div>
          </div>
        </Card>
      )}

      {/* Recent Posts */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Last 10 Posts</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/2">Post</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {posts.length > 0 ? (
                posts.map((post) => (
                  <TableRow key={post.id}>
                    <TableCell className="font-medium">
                      {post.caption.length > 100
                        ? `${post.caption.slice(0, 50)}...`
                        : post.caption}
                    </TableCell>
                    <TableCell>{post.platform}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          postStatus(post.status).badge as BadgeProps["variant"]
                        }
                        className={postStatus(post.status).badge}
                      >
                        {postStatus(post.status).text}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(post.timestamp).toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell className="font-medium">No posts yet</TableCell>
                  <TableCell>-</TableCell>
                  <TableCell>-</TableCell>
                  <TableCell>-</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </div>
  );
}

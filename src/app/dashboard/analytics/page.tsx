"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { db } from "@/lib/firebase";
import {
  doc,
  getDoc,
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  setDoc,
} from "firebase/firestore";
import { ReloadIcon, StarFilledIcon, RocketIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { useAgents } from "@/hooks/agents-context";
import analyticsGenerator from "@/lib/agents/analyticsGenerator";
import { InstagramPost } from "@/types";

interface Analytics {
  performance_summary: string;
  top_performing_content: string[];
  engagement_patterns: string[];
  content_recommendations: string[];
  hashtag_effectiveness: string[];
  sentiment_impact: string;
  quality_correlation: string;
  cultural_alignment_impact: string;
  key_metrics: {
    avg_engagement_rate: number;
    top_hashtag_performance: number;
    sentiment_correlation: number;
    quality_impact_score: number;
  };
  generated_at: string;
  data_range: {
    start: string;
    end: string;
  };
  post_count: number;
}

export default function AnalyticsPage() {
  const { agents } = useAgents();
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [isGeneratingAnalytics, setIsGeneratingAnalytics] = useState(false);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const analyticsRef = doc(db, "analytics", "latest");
      const analyticsDoc = await getDoc(analyticsRef);

      if (analyticsDoc.exists()) {
        setAnalytics(analyticsDoc.data() as Analytics);
      }
    } catch (error) {
      console.error("Error fetching analytics:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateAnalytics = async () => {
    setIsGeneratingAnalytics(true);
    try {
      // Get posts from the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const postsRef = collection(db, "social_media_posts");
      const q = query(
        postsRef,
        where("created_at", ">=", thirtyDaysAgo.toISOString()),
        orderBy("created_at", "desc"),
        limit(100)
      );

      const querySnapshot = await getDocs(q);
      const posts = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as InstagramPost[];

      // Use the analytics generator
      const analyticsResult = await analyticsGenerator(
        agents["textAnalyzer"],
        posts
      );
      const analytics = JSON.parse(analyticsResult);

      // Save analytics to Firestore
      const analyticsRef = doc(db, "analytics", "latest");
      await setDoc(analyticsRef, {
        ...analytics,
        generated_at: new Date().toISOString(),
        data_range: {
          start: thirtyDaysAgo.toISOString(),
          end: new Date().toISOString(),
        },
        post_count: posts.length,
      });

      console.log("Analytics generated and saved successfully");
      await fetchAnalytics(); // Refresh the analytics after generating new ones
    } catch (error) {
      console.error("Error generating analytics:", error);
    } finally {
      setIsGeneratingAnalytics(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[200px]">
        <ReloadIcon className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <StarFilledIcon className="h-12 w-12 mx-auto text-muted-foreground" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium">No Analytics Available</h3>
                <p className="text-sm text-muted-foreground">
                  Generate analytics to see insights here.
                </p>
                <Button
                  onClick={handleGenerateAnalytics}
                  disabled={isGeneratingAnalytics}
                  className="gap-2"
                >
                  {isGeneratingAnalytics ? (
                    <ReloadIcon className="h-4 w-4 animate-spin" />
                  ) : (
                    <StarFilledIcon className="h-4 w-4" />
                  )}
                  Generate AI Insights
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">AI Analytics Insights</h1>
          <p className="text-muted-foreground">
            Generated {new Date(analytics.generated_at).toLocaleString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleGenerateAnalytics}
            disabled={isGeneratingAnalytics}
            className="gap-2"
          >
            {isGeneratingAnalytics ? (
              <ReloadIcon className="h-4 w-4 animate-spin" />
            ) : (
              <StarFilledIcon className="h-4 w-4" />
            )}
            Generate New Insights
          </Button>
          <Button onClick={fetchAnalytics} variant="outline" size="icon">
            <ReloadIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Engagement Rate
                </p>
                <h2 className="text-3xl font-bold">
                  {analytics.key_metrics.avg_engagement_rate.toFixed(1)}%
                </h2>
              </div>
              <div className="rounded-full p-2 bg-primary/10">
                <RocketIcon className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Hashtag Performance
                </p>
                <h2 className="text-3xl font-bold">
                  {analytics.key_metrics.top_hashtag_performance.toFixed(1)}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-blue-500/10">
                <StarFilledIcon className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Sentiment Impact
                </p>
                <h2 className="text-3xl font-bold">
                  {analytics.key_metrics.sentiment_correlation.toFixed(2)}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-green-500/10">
                <StarFilledIcon className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Quality Score
                </p>
                <h2 className="text-3xl font-bold">
                  {analytics.key_metrics.quality_impact_score.toFixed(1)}
                </h2>
              </div>
              <div className="rounded-full p-2 bg-yellow-500/10">
                <StarFilledIcon className="h-6 w-6 text-yellow-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Summary</CardTitle>
          <CardDescription>
            Overall analysis of social media performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm leading-relaxed">
            {analytics.performance_summary}
          </p>
        </CardContent>
      </Card>

      {/* Content Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Content</CardTitle>
            <CardDescription>
              Characteristics of high-engagement posts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {analytics.top_performing_content.map((item, index) => (
                <li key={index} className="flex gap-2">
                  <Badge variant="outline">{index + 1}</Badge>
                  <span className="text-sm">{item}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Engagement Patterns</CardTitle>
            <CardDescription>Key patterns in user engagement</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {analytics.engagement_patterns.map((pattern, index) => (
                <li key={index} className="flex gap-2">
                  <Badge variant="outline">{index + 1}</Badge>
                  <span className="text-sm">{pattern}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations and Effectiveness */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Content Recommendations</CardTitle>
            <CardDescription>
              Strategic recommendations for improvement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {analytics.content_recommendations.map((rec, index) => (
                <li key={index} className="flex gap-2">
                  <Badge variant="outline">{index + 1}</Badge>
                  <span className="text-sm">{rec}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Hashtag Effectiveness</CardTitle>
            <CardDescription>Analysis of hashtag performance</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {analytics.hashtag_effectiveness.map((item, index) => (
                <li key={index} className="flex gap-2">
                  <Badge variant="outline">{index + 1}</Badge>
                  <span className="text-sm">{item}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Impact Analysis */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Sentiment Impact</CardTitle>
            <CardDescription>How sentiment affects engagement</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed">
              {analytics.sentiment_impact}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quality Correlation</CardTitle>
            <CardDescription>
              Relationship between quality and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed">
              {analytics.quality_correlation}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cultural Alignment</CardTitle>
            <CardDescription>
              Impact of cultural alignment on engagement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed">
              {analytics.cultural_alignment_impact}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analysis Info */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between text-sm text-muted-foreground">
            <div>
              <span>Analysis Period: </span>
              <span className="font-medium">
                {new Date(analytics.data_range.start).toLocaleDateString()} -{" "}
                {new Date(analytics.data_range.end).toLocaleDateString()}
              </span>
            </div>
            <div>
              <span>Posts Analyzed: </span>
              <span className="font-medium">{analytics.post_count}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

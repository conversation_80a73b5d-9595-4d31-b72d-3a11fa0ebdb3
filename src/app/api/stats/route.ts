import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";
import {
  StoryGeneratorResult,
  QualityCheckerResult,
  ArtGeneratorResult,
  CultureCheckerResult,
  ImageGeneratorResult,
  TextAnalyzerResult,
  SentimentAnalyzerResult,
} from "@/types";

interface AgentStats {
  total: number;
  completed: number;
  pending: number;
  failed: number;
  percentage: number;
}

type AgentKey =
  | "textAnalyzer"
  | "sentimentAnalyzer"
  | "storyGenerator"
  | "qualityChecker"
  | "cultureChecker"
  | "artGenerator"
  | "imageGenerator";

type Stats = Record<AgentKey, AgentStats>;

export async function GET() {
  try {
    const defaultStats: AgentStats = {
      total: 0,
      completed: 0,
      pending: 0,
      failed: 0,
      percentage: 0,
    };

    const stats: Stats = {
      textAnalyzer: { ...defaultStats },
      sentimentAnalyzer: { ...defaultStats },
      storyGenerator: { ...defaultStats },
      qualityChecker: { ...defaultStats },
      cultureChecker: { ...defaultStats },
      artGenerator: { ...defaultStats },
      imageGenerator: { ...defaultStats },
    };

    // Get all posts
    const postsRef = adminDb.collection("social_media_posts");
    const snapshot = await postsRef.get();
    const total = snapshot.size;

    // Initialize total counts
    (Object.keys(stats) as AgentKey[]).forEach((agentKey) => {
      stats[agentKey].total = total;
    });

    // Process each document
    snapshot.forEach((doc) => {
      const data = doc.data();
      const agents = data.agents as {
        textAnalyzer: TextAnalyzerResult | null;
        sentimentAnalyzer: SentimentAnalyzerResult | null;
        storyGenerator: StoryGeneratorResult | null;
        qualityChecker: QualityCheckerResult | null;
        cultureChecker: CultureCheckerResult | null;
        artGenerator: ArtGeneratorResult | null;
        imageGenerator: ImageGeneratorResult | null;
      };
      const status = data.status;

      // Process each agent's stats
      (Object.keys(stats) as AgentKey[]).forEach((agentKey) => {
        const agentData = agents[agentKey];

        if (agentData === undefined || agentData === null) {
          // Post hasn't been processed by this agent yet
          if (status === "processing") {
            stats[agentKey].pending++;
          }
        } else {
          // Post has been processed by this agent
          if (agentData.processed_at) {
            // Check for specific agent results based on the agent type
            let hasValidResults = false;
            switch (agentKey) {
              case "textAnalyzer":
                hasValidResults = !!(agentData as TextAnalyzerResult)
                  .content_quality;
                break;
              case "sentimentAnalyzer":
                hasValidResults = !!(agentData as SentimentAnalyzerResult)
                  .sentiment;
                break;
              case "storyGenerator":
                hasValidResults = !!(agentData as StoryGeneratorResult).story;
                break;
              case "qualityChecker":
                hasValidResults = !!(agentData as QualityCheckerResult)
                  .overall_score;
                break;
              case "cultureChecker":
                hasValidResults = !!(agentData as CultureCheckerResult)
                  .alignment_score;
                break;
              case "artGenerator":
                hasValidResults = !!(agentData as ArtGeneratorResult)
                  .visual_theme;
                break;
              case "imageGenerator":
                hasValidResults = !!(agentData as ImageGeneratorResult)
                  .storageUrl;
                break;
            }

            if (hasValidResults) {
              stats[agentKey].completed++;
            } else {
              stats[agentKey].failed++;
            }
          } else if (status === "processing") {
            stats[agentKey].pending++;
          }
        }
      });
    });

    // Calculate percentages
    (Object.keys(stats) as AgentKey[]).forEach((agentKey) => {
      const agent = stats[agentKey];
      agent.percentage =
        agent.total > 0 ? (agent.completed / agent.total) * 100 : 0;
    });

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch stats" },
      { status: 500 }
    );
  }
}

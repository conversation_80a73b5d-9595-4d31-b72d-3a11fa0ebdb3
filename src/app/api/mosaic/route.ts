"use server";

import { NextResponse } from "next/server";
import { GoogleAuth } from "google-auth-library";
import { storage } from "@/lib/firebase";
import { ref, getDownloadURL } from "firebase/storage";

const LOCATION = "us-central1";

// Initialize Google Auth client
const auth = new GoogleAuth({
  scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "qnd-platform",
  credentials: {
    type: "service_account",
    project_id: "qnd-platform",
    private_key_id: "d7810aee3f03d54751d93e449fa3b3d8a1f657e5",
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      "<EMAIL>",
    client_id: "107042730709320593986",
    universe_domain: "googleapis.com",
  },
});

async function getAccessToken(): Promise<string> {
  try {
    const client = await auth.getClient();
    const token = await client.getAccessToken();
    return token.token || "";
  } catch (error) {
    console.error("Error getting access token:", error);
    throw new Error(
      `Failed to get access token: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const captions = body.captions;
    const prompt = body.prompt;

    // Validate input
    if (!prompt || typeof prompt !== "string") {
      return NextResponse.json(
        {
          success: false,
          error: "Please provide a valid prompt string",
        },
        { status: 400 }
      );
    }

    if (!captions || !Array.isArray(captions) || captions.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: "Please provide an array of captions",
        },
        { status: 400 }
      );
    }

    // Clean captions for backup use
    const cleanedCaptions = captions
      .map((caption) => caption.replace(/[^\w\s.,]/g, ""))
      .join(". ");

    // Use the provided prompt, with cleaned captions as backup context
    const finalPrompt = `${prompt}

    Note: Don't include any text or typography elements in the image.

    Additional context from posts:
    ${cleanedCaptions}

    Style requirements:
    - Use a vibrant, bold color palette with rich magentas, teals, and golden yellows
    - Create a dynamic multi-panel composition with overlapping elements
    - Apply artistic color splash effects and paint splatter textures
    - Add ethereal sparkles and starlight elements throughout
    - Incorporate smooth color transitions and gradient overlays
    - Use a dreamy, ethereal atmosphere with glowing elements
    - Blend abstract and representational elements seamlessly
    - Add subtle bokeh and light particle effects
    - Maintain high contrast and visual impact
    - Create a modern, gallery-quality aesthetic

    Technical specifications:
    - Ultra-high resolution and sharp details
    - Professional color grading with rich saturation
    - Cinematic lighting with dramatic highlights
    - Smooth anti-aliasing and clean edges
    - Premium post-processing effects`;

    const accessToken = await getAccessToken();
    const endpoint = `https://${LOCATION}-aiplatform.googleapis.com/v1/projects/${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}/locations/${LOCATION}/publishers/google/models/imagen-3.0-generate-002:predict`;

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        instances: [{ prompt: finalPrompt }],
        parameters: {
          sampleCount: 1,
          enhancePrompt: false,
          aspectRatio: "16:9",
          personGeneration: "allow_all",
          safetySetting: "block_only_high",
          storageUri:
            "gs://qnd-platform.firebasestorage.app/mosaics_from_vertex",
          imageSize: "1920x1080",
        },
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vertex AI API error: ${errorText}`);
    }

    const data = await response.json();

    console.log(data.predictions[0].gcsUri);

    if (!data?.predictions?.[0]?.gcsUri) {
      throw new Error("Invalid response from Vertex AI");
    }

    const gcsUrl = data.predictions[0].gcsUri;
    // Extract the path from the GCS URI
    const gcsPath = gcsUrl.replace(
      "gs://qnd-platform.firebasestorage.app/",
      ""
    );

    // Get a reference to the file in Firebase Storage
    const storageRef = ref(storage, gcsPath);

    // Get the download URL with authentication token
    const downloadUrl = await getDownloadURL(storageRef);

    return NextResponse.json({ success: true, url: downloadUrl });
  } catch (error) {
    console.error("Error generating artwork:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

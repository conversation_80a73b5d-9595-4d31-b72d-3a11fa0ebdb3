import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";

interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

interface PostData {
  id: string;
  postBody?: string;
  username?: string;
  authorName?: string;
  created_at: FirestoreTimestamp | null;
  metrics?: {
    postBody?: string;
    likes?: number;
    replies?: number;
    retweets?: number;
    parentPostId?: string;
    platform?: string;
    post_id?: string;
  };
  [key: string]: any;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const search = searchParams.get("search") || "";

    // Calculate pagination
    const skip = (page - 1) * pageSize;

    // Get total count
    const countSnapshot = await adminDb.collection("posts").count().get();
    const totalItems = countSnapshot.data().count;
    const totalPages = Math.ceil(totalItems / pageSize);

    // Fetch posts with pagination
    const postsQuery = adminDb
      .collection("posts")
      .orderBy("created_at", "desc")
      .offset(skip)
      .limit(pageSize);

    const postsSnapshot = await postsQuery.get();

    const posts: PostData[] = postsSnapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        // Ensure created_at is properly serialized
        created_at: data.created_at
          ? {
              seconds: data.created_at.seconds,
              nanoseconds: data.created_at.nanoseconds,
            }
          : null,
      };
    });

    // Filter posts by search query if provided
    const filteredPosts = search
      ? posts.filter((post) => {
          const postBody = post.postBody || post.metrics?.postBody || "";
          const username = post.username || "";
          const authorName = post.authorName || "";

          return (
            postBody.toLowerCase().includes(search.toLowerCase()) ||
            username.toLowerCase().includes(search.toLowerCase()) ||
            authorName.toLowerCase().includes(search.toLowerCase())
          );
        })
      : posts;

    return NextResponse.json({
      posts: filteredPosts,
      pagination: {
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      },
    });
  } catch (error) {
    console.error("Error fetching posts:", error);
    return NextResponse.json(
      { error: "Failed to fetch posts" },
      { status: 500 }
    );
  }
}

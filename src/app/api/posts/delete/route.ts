import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";

export async function POST(req: Request) {
  try {
    const { postIds } = await req.json();

    if (!Array.isArray(postIds) || postIds.length === 0) {
      return NextResponse.json(
        { error: "Invalid or empty post IDs array" },
        { status: 400 }
      );
    }

    const batch = adminDb.batch();
    postIds.forEach((id) => {
      const docRef = adminDb.collection("social_media_posts").doc(id);
      batch.delete(docRef);
    });

    await batch.commit();

    return NextResponse.json({ success: true, deletedCount: postIds.length });
  } catch (error) {
    console.error("Error deleting posts:", error);
    return NextResponse.json(
      { error: "Failed to delete posts" },
      { status: 500 }
    );
  }
}

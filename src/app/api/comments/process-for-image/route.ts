import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";

export async function POST(request: Request) {
  try {
    // Get comment ID from request body
    const { commentId } = await request.json();

    if (!commentId) {
      return NextResponse.json(
        { error: "Comment ID is required" },
        { status: 400 }
      );
    }

    // Get the comment document
    const commentRef = adminDb.collection("instagram_comments").doc(commentId);
    const commentDoc = await commentRef.get();

    if (!commentDoc.exists) {
      return NextResponse.json({ error: "Comment not found" }, { status: 404 });
    }

    // Mark the comment as being processed
    await commentRef.update({
      imageGenerating: true,
      imageGenerated: false,
      imageProcessingStartedAt: new Date(),
    });

    // In a real implementation, you would trigger a Cloud Function or background process here
    // For now, we'll just return success to simulate the start of processing

    return NextResponse.json({
      success: true,
      message: "Comment processing started",
      commentId,
      status: "processing",
    });
  } catch (error) {
    console.error("Error processing comment for image generation:", error);
    return NextResponse.json(
      {
        error: "Failed to process comment for image generation",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

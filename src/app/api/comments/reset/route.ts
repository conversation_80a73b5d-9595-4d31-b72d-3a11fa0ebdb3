import { adminDb } from "@/lib/firebase-admin";
import { NextRequest, NextResponse } from "next/server";

export type ResetCommentSuccessResult = {
  success: true;
  message: string;
  commentId: string;
};

export type ResetCommentErrorResult = {
  success: false;
  error: string;
  details?: string;
};

export type ResetCommentResult =
  | ResetCommentSuccessResult
  | ResetCommentErrorResult;

export async function POST(request: NextRequest) {
  try {
    const { commentId } = await request.json();

    if (!commentId) {
      console.log("[API DEBUG] resetComment: commentId is empty");
      return NextResponse.json(
        {
          success: false,
          error: "Comment ID is required",
        } as ResetCommentErrorResult,
        { status: 400 }
      );
    }

    // Get the comment document
    const commentRef = adminDb.collection("instagram_comments").doc(commentId);
    console.log(`[API DEBUG] Getting comment document for ID: ${commentId}`);

    const commentDoc = await commentRef.get();

    if (!commentDoc.exists) {
      console.log(`[API DEBUG] Comment not found for ID: ${commentId}`);
      return NextResponse.json(
        {
          success: false,
          error: "Comment not found",
        } as ResetCommentErrorResult,
        { status: 404 }
      );
    }

    console.log(`[API DEBUG] Updating comment with ID: ${commentId}`);
    // Reset the image generation fields
    await commentRef.update({
      imageGenerating: false,
      imageGenerated: false,
      imageResetAt: new Date(),
    });

    console.log(`[API DEBUG] Reset successful for comment: ${commentId}`);
    return NextResponse.json({
      success: true,
      message: "Image generation status reset successfully",
      commentId,
    } as ResetCommentSuccessResult);
  } catch (error) {
    console.error("[API DEBUG] Error in resetComment:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to reset image generation status",
        details: (error as Error).message,
      } as ResetCommentErrorResult,
      { status: 500 }
    );
  }
}

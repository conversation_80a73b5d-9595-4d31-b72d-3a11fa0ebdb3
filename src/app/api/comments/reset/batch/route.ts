import { adminDb } from "@/lib/firebase-admin";
import { NextRequest, NextResponse } from "next/server";
import { DocumentData, Query, WriteResult } from "firebase-admin/firestore";

export type BatchResetSuccessResult = {
  success: true;
  message: string;
  reset: number;
  total: number;
  remaining: number;
};

export type BatchResetErrorResult = {
  success: false;
  error: string;
  details?: string;
};

export type BatchResetResult = BatchResetSuccessResult | BatchResetErrorResult;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    let filter = body.filter;
    let limit = body.limit ?? 100;

    // Validate inputs to prevent null values
    if (filter === null || filter === undefined) {
      console.log(
        "[API DEBUG] Filter is null or undefined, defaulting to 'failed'"
      );
      filter = "failed";
    }

    if (limit === null || limit === undefined || isNaN(Number(limit))) {
      console.log("[API DEBUG] Limit is invalid, defaulting to 100");
      limit = 100;
    }

    // Start building the query
    const commentsRef = adminDb.collection("instagram_comments");
    const query: Query<DocumentData> = commentsRef;

    try {
      const snapshot = await query.get();
      const commentsToReset = snapshot.docs.map((doc) => ({
        id: doc.id,
      }));

      // Reset comments in batches
      let resetCount = 0;
      const batches: Promise<WriteResult[]>[] = [];
      const now = new Date();
      const batchSize = 500;

      for (let i = 0; i < commentsToReset.length; i += batchSize) {
        const batch = adminDb.batch();
        const currentBatch = commentsToReset.slice(i, i + batchSize);

        currentBatch.forEach(({ id }) => {
          const docRef = commentsRef.doc(id);
          batch.update(docRef, {
            imageGenerating: false,
            imageGenerated: false,
            imageResetAt: now,
          });
          resetCount++;
        });

        batches.push(batch.commit());
      }

      // Wait for all batches to complete
      await Promise.all(batches);
      console.log(`[API DEBUG] Successfully reset ${resetCount} comments`);

      const response = {
        success: true,
        message: `Successfully reset ${resetCount} comments`,
        reset: resetCount,
        total: 0,
        remaining: 0,
      } as BatchResetSuccessResult;

      console.log("[API DEBUG] Returning response:", response);
      return NextResponse.json(response);
    } catch (queryError) {
      console.error("[API DEBUG] Error in query execution:", queryError);
      const errorResponse = {
        success: false,
        error: "Query execution failed",
        details:
          queryError instanceof Error ? queryError.message : String(queryError),
      } as BatchResetErrorResult;
      console.log("[API DEBUG] Returning error response:", errorResponse);
      return NextResponse.json(errorResponse, { status: 500 });
    }
  } catch (error) {
    console.error("[API DEBUG] Error in batchResetComments:", error);
    const errorResponse = {
      success: false,
      error: "Failed to batch reset image generation status",
      details: error instanceof Error ? error.message : String(error),
    } as BatchResetErrorResult;
    console.log("[API DEBUG] Returning error response:", errorResponse);
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

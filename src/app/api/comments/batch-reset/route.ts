import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";
import { DocumentData, Query, WriteResult } from "firebase-admin/firestore";

export async function POST(request: Request) {
  console.log("Batch reset API called");

  try {
    // Get parameters from request body
    let body;
    try {
      const text = await request.text();
      console.log("Request body text:", text);

      if (!text || text.trim() === "") {
        return NextResponse.json(
          {
            success: false,
            error: "Empty request body",
            details: "Request body cannot be empty",
          },
          { status: 400 }
        );
      }

      body = JSON.parse(text);
      console.log("Parsed body:", body);
    } catch (error) {
      console.error("Error parsing request body:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Invalid JSON in request body",
          details: (error as Error).message,
        },
        { status: 400 }
      );
    }

    const { filter, limit = 100, batchSize = 500 } = body || {};

    // Validate filter parameter
    if (!filter) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing filter parameter",
          details:
            "Filter parameter is required (generated, generating, or failed)",
        },
        { status: 400 }
      );
    }

    console.log(
      `Processing batch reset with filter: ${filter}, limit: ${limit}`
    );

    // Start building the query
    const commentsRef = adminDb.collection("instagram_comments");
    let query: Query<DocumentData> = commentsRef;

    // Apply filter if provided
    if (filter === "generated") {
      query = query.where("imageGenerated", "==", true);
    } else if (filter === "generating") {
      query = query.where("imageGenerating", "==", true);
    } else if (filter === "failed") {
      query = query
        .where("imageGenerating", "==", false)
        .where("imageGenerated", "==", false);
    } else {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid filter parameter",
          details: "Filter must be one of: generated, generating, failed",
        },
        { status: 400 }
      );
    }

    // Get count of matching comments
    let countSnapshot;
    try {
      countSnapshot = await query.count().get();
      console.log(`Found ${countSnapshot.data().count} matching comments`);
    } catch (error) {
      console.error("Error getting count:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to get comment count",
          details: (error as Error).message,
        },
        { status: 500 }
      );
    }

    const totalCount = countSnapshot.data().count;

    // If no comments match, return early
    if (totalCount === 0) {
      return NextResponse.json({
        success: true,
        message: "No comments match the filter criteria",
        reset: 0,
        total: totalCount,
      });
    }

    // Limit the number of comments to reset if specified
    const limitToUse = limit > 0 ? Math.min(limit, totalCount) : totalCount;
    console.log(`Will reset up to ${limitToUse} comments`);

    // Get comments to reset
    let snapshot;
    try {
      snapshot = await query
        .orderBy("created_at", "desc")
        .limit(limitToUse)
        .get();

      console.log(`Retrieved ${snapshot.docs.length} comments to reset`);
    } catch (error) {
      console.error("Error fetching comments:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch comments",
          details: (error as Error).message,
        },
        { status: 500 }
      );
    }

    const commentsToReset = snapshot.docs.map((doc) => ({ id: doc.id }));

    // Reset comments in batches to avoid Firestore limits
    let resetCount = 0;
    const batches: Promise<WriteResult[]>[] = [];
    const now = new Date();

    for (let i = 0; i < commentsToReset.length; i += batchSize) {
      const batch = adminDb.batch();
      const currentBatch = commentsToReset.slice(i, i + batchSize);

      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1} with ${
          currentBatch.length
        } comments`
      );

      currentBatch.forEach(({ id }) => {
        const docRef = commentsRef.doc(id);
        batch.update(docRef, {
          imageGenerating: false,
          imageGenerated: false,
          imageResetAt: now,
        });
        resetCount++;
      });

      batches.push(batch.commit());
    }

    // Wait for all batches to complete
    try {
      await Promise.all(batches);
      console.log(`Successfully reset ${resetCount} comments`);
    } catch (error) {
      console.error("Error committing batches:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update comments",
          details: (error as Error).message,
        },
        { status: 500 }
      );
    }

    const result = {
      success: true,
      message: `Successfully reset ${resetCount} comments`,
      reset: resetCount,
      total: totalCount,
      remaining: totalCount - resetCount,
    };

    console.log("Returning result:", result);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error batch resetting image generation status:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to batch reset image generation status",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

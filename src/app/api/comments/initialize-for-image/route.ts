import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";
import { WriteResult } from "firebase-admin/firestore";
export async function POST(request: Request) {
  try {
    // Get parameters from request body
    const { limit = 100, batchSize = 500 } = await request.json();

    // Get comments that don't have imageGenerating field set
    const commentsRef = adminDb.collection("instagram_comments");

    // First, count how many comments need initialization
    const countQuery = commentsRef.where("imageGenerating", "in", [
      true,
      false,
    ]);
    const countSnapshot = await countQuery.count().get();
    const initializedCount = countSnapshot.data().count;

    // Get total count
    const totalCountSnapshot = await commentsRef.count().get();
    const totalCount = totalCountSnapshot.data().count;

    // Calculate how many need initialization
    const needInitializationCount = totalCount - initializedCount;

    // If no comments need initialization, return early
    if (needInitializationCount === 0) {
      return NextResponse.json({
        success: true,
        message: "No comments need initialization",
        initialized: 0,
        total: totalCount,
      });
    }

    // Limit the number of comments to initialize if specified
    const limitToUse =
      limit > 0
        ? Math.min(limit, needInitializationCount)
        : needInitializationCount;

    // Get comments that don't have imageGenerating field set
    // We can't directly query for documents where a field doesn't exist,
    // so we'll get all documents and filter out the ones that already have the field
    const snapshot = await commentsRef
      .orderBy("created_at", "desc")
      .limit(limitToUse + initializedCount) // Get more than we need to account for already initialized ones
      .get();

    // Filter out comments that already have imageGenerating field
    const commentsToInitialize = snapshot.docs
      .map((doc) => ({ id: doc.id, data: doc.data() }))
      .filter(
        (doc) =>
          doc.data.imageGenerating !== true &&
          doc.data.imageGenerating !== false
      )
      .slice(0, limitToUse); // Limit to the number we want to initialize

    // Initialize comments in batches to avoid Firestore limits
    let initializedCount2 = 0;
    const batches: Promise<WriteResult[]>[] = [];

    for (let i = 0; i < commentsToInitialize.length; i += batchSize) {
      const batch = adminDb.batch();
      const currentBatch = commentsToInitialize.slice(i, i + batchSize);

      currentBatch.forEach(({ id }) => {
        const docRef = commentsRef.doc(id);
        batch.update(docRef, {
          imageGenerating: false,
          imageGenerated: false,
        });
        initializedCount2++;
      });

      batches.push(batch.commit());
    }

    // Wait for all batches to complete
    await Promise.all(batches);

    return NextResponse.json({
      success: true,
      message: `Successfully initialized ${initializedCount2} comments for image generation`,
      initialized: initializedCount2,
      total: totalCount,
      remaining: needInitializationCount - initializedCount2,
    });
  } catch (error) {
    console.error("Error initializing comments for image generation:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to initialize comments for image generation",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

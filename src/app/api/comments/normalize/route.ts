import { adminDb } from "@/lib/firebase-admin";
import { NextResponse } from "next/server";
import { QueryDocumentSnapshot, DocumentData } from "firebase-admin/firestore";

export async function POST() {
  try {
    const commentsRef = adminDb.collection("instagram_comments");
    let processed = 0;
    let skipped = 0;
    const batchSize = 500; // Firestore batch limit
    const queryLimit = 1000; // Number of documents to fetch per query
    let lastDoc: QueryDocumentSnapshot<DocumentData> | null = null;
    let hasMore = true;

    const defaultValues = {
      imageGenerated: false,
      imageUrl: "",
      imageGenerationStatus: "NOT_STARTED",
      imageGenerating: false,
      platform: "instagram",
    };

    while (hasMore) {
      // Build query with pagination
      let query = commentsRef.orderBy("created_at", "desc").limit(queryLimit);
      if (lastDoc) {
        query = query.startAfter(lastDoc);
      }

      const snapshot = await query.get();

      if (snapshot.empty) {
        hasMore = false;
        continue;
      }

      // Update lastDoc for next iteration
      lastDoc = snapshot.docs[snapshot.docs.length - 1];

      // Process documents in batches
      let batch = adminDb.batch();
      let currentBatchSize = 0;

      for (const doc of snapshot.docs) {
        const data = doc.data();
        const updates: Record<string, any> = {};
        let needsUpdate = false;

        // Check each field and add to updates if missing
        for (const [key, value] of Object.entries(defaultValues)) {
          if (data[key] === undefined) {
            updates[key] = value;
            needsUpdate = true;
          }
        }

        if (needsUpdate) {
          batch.update(doc.ref, updates);
          processed++;
          currentBatchSize++;

          // If we've reached the batch size limit, commit and create a new batch
          if (currentBatchSize === batchSize) {
            await batch.commit();
            batch = adminDb.batch();
            currentBatchSize = 0;
          }
        } else {
          skipped++;
        }
      }

      // Commit any remaining updates in the current batch
      if (currentBatchSize > 0) {
        await batch.commit();
      }

      // Check if we've processed all documents
      if (snapshot.docs.length < queryLimit) {
        hasMore = false;
      }

      // Log progress
      console.log(`Processed batch: ${processed} updated, ${skipped} skipped`);
    }

    return NextResponse.json({
      success: true,
      processed,
      skipped,
      message: `Successfully normalized ${processed} comments (${skipped} already normalized)`,
    });
  } catch (error) {
    console.error("Error normalizing comments:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to normalize comments",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";
import { DocumentData, Query } from "firebase-admin/firestore";

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const page = parseInt(url.searchParams.get("page") || "1");
    const countOnly = url.searchParams.get("countOnly") === "true";

    // Calculate offset based on page and limit
    const offset = (page - 1) * limit;

    // Start building the query
    const commentsRef = adminDb.collection("instagram_comments");
    let query: Query<DocumentData> = commentsRef
      .where("imageGenerated", "==", true)
      .orderBy("created_at", "desc");

    // Get total count of generated images
    const countSnapshot = await commentsRef
      .where("imageGenerated", "==", true)
      .count()
      .get();
    const totalCount = countSnapshot.data().count;

    // If only count is requested, return the count
    if (countOnly) {
      return NextResponse.json({ count: totalCount });
    }

    // Apply pagination
    query = query.limit(limit);

    // Apply offset using startAfter if not the first page
    if (page > 1) {
      // Get the document at the offset position
      const offsetSnapshot = await query.limit(offset).get();
      if (!offsetSnapshot.empty) {
        const lastVisible = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        query = query.startAfter(lastVisible);
      }
    }

    // Execute the query
    const snapshot = await query.limit(limit).get();

    // Process the results
    const images = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        authorName: data.authorName || "",
        username: data.username || "",
        postBody: data.postBody || "",
        created_at:
          data.created_at?.toDate?.()?.toISOString() ||
          new Date().toISOString(),
        comment_like_count: data.comment_like_count || 0,
        metrics: data.metrics || { likes: 0 },
        imageUrl: data.imageUrl || "",
        imageGenerated: !!data.imageGenerated,
        textAnalysis: data.textAnalysis || null,
        sentimentAnalysis: data.sentimentAnalysis || null,
        storyAnalysis: data.storyAnalysis || null,
        cultureFitAnalysis: data.cultureFitAnalysis || null,
        qualityCheck: data.qualityCheck || null,
        artDirection: data.artDirection || null,
        post_id: data.post_id || "",
      };
    });

    // Return the results with pagination info
    return NextResponse.json({
      images,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
        hasMore: images.length === limit && offset + images.length < totalCount,
      },
    });
  } catch (error) {
    console.error("Error fetching generated images:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch generated images",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

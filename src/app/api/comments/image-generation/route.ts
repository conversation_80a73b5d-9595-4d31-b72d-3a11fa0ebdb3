/* eslint-disable @typescript-eslint/no-unused-vars */
import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";
import { DocumentData, Query, WhereFilterOp } from "firebase-admin/firestore";

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "100");
    const page = parseInt(url.searchParams.get("page") || "1");
    const statusOnly = url.searchParams.get("statusOnly") === "true";

    // Calculate offset based on page and limit
    const offset = (page - 1) * limit;

    // Start building the query
    const commentsRef = adminDb.collection("instagram_comments");
    let query: Query<DocumentData> = commentsRef;

    // Apply sorting by creation date (newest first)
    query = query.orderBy("created_at", "desc");

    // Get total count for status calculations
    const countSnapshot = await commentsRef.count().get();
    const totalCount = countSnapshot.data().count;

    // Get counts for different statuses
    const processedSnapshot = await commentsRef
      .where("imageGenerated", "==", true)
      .count()
      .get();
    const processedCount = processedSnapshot.data().count;

    const inProgressSnapshot = await commentsRef
      .where("imageGenerating", "==", true)
      .where("imageGenerated", "==", false)
      .count()
      .get();
    const inProgressCount = inProgressSnapshot.data().count;

    const failedSnapshot = await commentsRef
      .where("imageGenerating", "==", false)
      .where("imageGenerated", "==", false)
      .count()
      .get();
    const failedCount = failedSnapshot.data().count;

    // Calculate not initialized count
    const initializedSnapshot = await commentsRef
      .where("imageGenerating", "in", [true, false])
      .count()
      .get();
    const initializedCount = initializedSnapshot.data().count;
    const notInitializedCount = totalCount - initializedCount;

    // Create status object
    const status = {
      total: totalCount,
      processed: processedCount,
      failed: failedCount,
      inProgress: inProgressCount,
      notInitialized: notInitializedCount,
    };

    // If only status is requested, return just the status
    if (statusOnly) {
      return NextResponse.json({ status });
    }

    // Apply pagination
    query = query.limit(limit);

    // Apply offset using startAfter if not the first page
    if (page > 1) {
      // Get the document at the offset position
      const offsetSnapshot = await query.limit(offset).get();
      if (!offsetSnapshot.empty) {
        const lastVisible = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        query = query.startAfter(lastVisible);
      }
    }

    // Execute the query
    const snapshot = await query.limit(limit).get();

    // Process the results
    const comments = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        authorName: data.authorName || "",
        username: data.username || "",
        postBody: data.postBody || "",
        created_at:
          data.created_at?.toDate?.()?.toISOString() ||
          new Date().toISOString(),
        comment_like_count: data.comment_like_count || 0,
        metrics: data.metrics || { likes: 0 },
        imageUrl: data.imageUrl || "",
        imageGenerated: !!data.imageGenerated,
        imageGenerating: !!data.imageGenerating,
        post_id: data.post_id || "",
      };
    });

    // Return the results with pagination info and status
    return NextResponse.json({
      comments,
      status: {
        ...status,
        fetchedCount: comments.length,
      },
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
        hasMore:
          comments.length === limit && offset + comments.length < totalCount,
      },
    });
  } catch (error) {
    console.error("Error fetching comments for image generation:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch comments for image generation",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

/* eslint-disable @typescript-eslint/no-unused-vars */
import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";

export async function POST() {
  try {
    const commentsRef = adminDb.collection("instagram_comments");
    const snapshot = await commentsRef.get();

    // Group comments by post_id
    const commentsByPostId = new Map<string, any[]>();
    snapshot.docs.forEach((doc) => {
      const data = doc.data();
      const postId = data.post_id;
      if (!commentsByPostId.has(postId)) {
        commentsByPostId.set(postId, []);
      }
      commentsByPostId.get(postId)?.push({ id: doc.id, ...data });
    });

    // Find and delete duplicates
    const deletedCount = new Set<string>();
    const batch = adminDb.batch();

    for (const [postId, comments] of commentsByPostId.entries()) {
      if (comments.length > 1) {
        // Keep the first comment, delete the rest
        comments.slice(1).forEach((comment) => {
          batch.delete(commentsRef.doc(comment.id));
          deletedCount.add(comment.id);
        });
      }
    }

    // Commit the batch
    await batch.commit();

    return NextResponse.json({
      success: true,
      deletedCount: deletedCount.size,
    });
  } catch (error) {
    console.error("Error deleting duplicate comments:", error);
    return NextResponse.json(
      { error: "Failed to delete duplicate comments" },
      { status: 500 }
    );
  }
}

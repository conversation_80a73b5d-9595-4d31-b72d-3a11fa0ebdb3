/* eslint-disable @typescript-eslint/no-unused-vars */
import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";
import {
  CollectionReference,
  DocumentData,
  Query,
  WhereFilterOp,
} from "firebase-admin/firestore";

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "100");
    const page = parseInt(url.searchParams.get("page") || "1");
    const sortBy = url.searchParams.get("sortBy") || "created_at";
    const sortOrder = url.searchParams.get("sortOrder") || "desc";
    const filterField = url.searchParams.get("filterField") || null;
    const filterValue = url.searchParams.get("filterValue") || null;
    const filterOperator = url.searchParams.get("filterOperator") || "==";
    const countOnly = url.searchParams.get("countOnly") === "true";

    // Calculate offset based on page and limit
    const offset = (page - 1) * limit;

    // Start building the query
    const commentsRef = adminDb.collection("instagram_comments");
    let query: Query<DocumentData> = commentsRef;

    // Apply filter if provided
    if (filterField && filterValue !== null) {
      // Handle boolean values
      let parsedValue: any = filterValue;
      if (filterValue === "true") parsedValue = true;
      if (filterValue === "false") parsedValue = false;

      // Apply the filter with the specified operator
      query = query.where(
        filterField,
        filterOperator as WhereFilterOp,
        parsedValue
      );
    }

    // If only count is requested, return the count
    if (countOnly) {
      const countSnapshot = await query.count().get();
      return NextResponse.json({ count: countSnapshot.data().count });
    }

    // Apply sorting
    query = query.orderBy(sortBy, sortOrder as "asc" | "desc");

    // Get total count for pagination info
    const countSnapshot = await query.count().get();
    const totalCount = countSnapshot.data().count;

    // Apply pagination
    query = query.limit(limit);

    // Apply offset using startAfter if not the first page
    if (page > 1) {
      // Get the document at the offset position
      const offsetSnapshot = await query.limit(offset).get();
      if (!offsetSnapshot.empty) {
        const lastVisible = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        query = query.startAfter(lastVisible);
      }
    }

    // Execute the query
    const snapshot = await query.limit(limit).get();

    // Process the results
    const comments = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        authorName: data.authorName || "",
        username: data.username || "",
        postBody: data.postBody || "",
        created_at:
          data.created_at?.toDate?.()?.toISOString() ||
          new Date().toISOString(),
        comment_like_count: data.comment_like_count || 0,
        metrics: data.metrics || { likes: 0 },
        imageUrl: data.imageUrl || "",
        imageGenerated: !!data.imageGenerated,
        imageGenerating: !!data.imageGenerating,
        post_id: data.post_id || "",
        // Include other fields as needed
      };
    });

    // Return the results with pagination info
    return NextResponse.json({
      comments,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
        hasMore:
          comments.length === limit && offset + comments.length < totalCount,
      },
    });
  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json(
      { error: "Failed to fetch comments", details: (error as Error).message },
      { status: 500 }
    );
  }
}

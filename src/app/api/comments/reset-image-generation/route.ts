import { NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase-admin";

export async function POST(request: Request) {
  try {
    // Get comment ID from request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      console.error("Error parsing request body:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Invalid JSON in request body",
          details: (error as Error).message,
        },
        { status: 400 }
      );
    }

    const { commentId } = body;

    if (!commentId) {
      return NextResponse.json(
        { error: "Comment ID is required" },
        { status: 400 }
      );
    }

    // Get the comment document
    const commentRef = adminDb.collection("instagram_comments").doc(commentId);
    let commentDoc;

    try {
      commentDoc = await commentRef.get();
    } catch (error) {
      console.error("Error fetching comment:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch comment",
          details: (error as Error).message,
        },
        { status: 500 }
      );
    }

    if (!commentDoc.exists) {
      return NextResponse.json({ error: "Comment not found" }, { status: 404 });
    }

    // Reset the image generation fields
    try {
      await commentRef.update({
        imageGenerating: false,
        imageGenerated: false,
        // Keep the imageUrl if it exists, but mark it as not generated
        // This allows keeping track of previous generations
        imageResetAt: new Date(),
      });
    } catch (error) {
      console.error("Error updating comment:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update comment",
          details: (error as Error).message,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Image generation status reset successfully",
      commentId,
      status: "reset",
    });
  } catch (error) {
    console.error("Error resetting image generation status:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to reset image generation status",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

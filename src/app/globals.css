@tailwind base;
@tailwind components;
@tailwind utilities;

.dark,
.dark-theme,
:is(.dark, .dark-theme) :where(.radix-themes:not(.light, .light-theme)) {
  --color-background: #000;
}

.dark,
.dark-theme {
  --green-1: #000;
  --green-2: #0d130e;
  --green-3: #172719;
  --green-4: #19391f;
  --green-5: #214828;
  --green-6: #295732;
  --green-7: #30683b;
  --green-8: #377b45;
  --green-9: #336b3e;
  --green-10: #2c5a35;
  --green-11: #85cb90;
  --green-12: #baf2c2;

  --green-a1: #00000000;
  --green-a2: #afffbc13;
  --green-a3: #97ffa427;
  --green-a4: #70ff8b39;
  --green-a5: #75ff8e48;
  --green-a6: #79ff9357;
  --green-a7: #76ff9168;
  --green-a8: #73ff907b;
  --green-a9: #7aff946b;
  --green-a10: #7dff975a;
  --green-a11: #a7ffb5cb;
  --green-a12: #c4ffccf2;

  --green-contrast: #fff;
  --green-surface: #1a261c80;
  --green-indicator: #336b3e;
  --green-track: #336b3e;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark,
    .dark-theme {
      --green-1: oklch(0% 0.0157 148.4);
      --green-2: oklch(18% 0.0157 148.4);
      --green-3: oklch(25.2% 0.0335 148.4);
      --green-4: oklch(31.1% 0.0583 148.4);
      --green-5: oklch(36.2% 0.0691 148.4);
      --green-6: oklch(41.4% 0.0804 148.4);
      --green-7: oklch(46.7% 0.0932 148.4);
      --green-8: oklch(52.3% 0.1086 148.4);
      --green-9: oklch(47.7% 0.0932 148.4);
      --green-10: oklch(42.5% 0.0804 148.4);
      --green-11: oklch(78% 0.1086 148.4);
      --green-12: oklch(91.1% 0.087 148.4);

      --green-a1: color(display-p3 0 0 0 / 0);
      --green-a2: color(display-p3 0.7333 0.9961 0.7333 / 0.075);
      --green-a3: color(display-p3 0.6941 1 0.6667 / 0.153);
      --green-a4: color(display-p3 0.5882 1 0.6078 / 0.22);
      --green-a5: color(display-p3 0.6078 1 0.6078 / 0.279);
      --green-a6: color(display-p3 0.6157 1 0.6275 / 0.338);
      --green-a7: color(display-p3 0.6039 1 0.6118 / 0.404);
      --green-a8: color(display-p3 0.6039 1 0.6118 / 0.475);
      --green-a9: color(display-p3 0.6157 1 0.6235 / 0.416);
      --green-a10: color(display-p3 0.6196 1 0.6392 / 0.35);
      --green-a11: color(display-p3 0.7412 1 0.7412 / 0.789);
      --green-a12: color(display-p3 0.8196 1 0.8235 / 0.942);

      --green-contrast: #fff;
      --green-surface: color(display-p3 0.1098 0.149 0.1098 / 0.5);
      --green-indicator: oklch(47.7% 0.0932 148.4);
      --green-track: oklch(47.7% 0.0932 148.4);
    }
  }
}

.dark,
.dark-theme {
  --gray-1: #000;
  --gray-2: #121212;
  --gray-3: #1f1f1f;
  --gray-4: #282828;
  --gray-5: #303030;
  --gray-6: #3a3a3a;
  --gray-7: #484848;
  --gray-8: #606060;
  --gray-9: #6e6e6e;
  --gray-10: #7b7b7b;
  --gray-11: #b4b4b4;
  --gray-12: #eee;

  --gray-a1: #00000000;
  --gray-a2: #ffffff12;
  --gray-a3: #ffffff1f;
  --gray-a4: #ffffff28;
  --gray-a5: #ffffff30;
  --gray-a6: #ffffff3a;
  --gray-a7: #ffffff48;
  --gray-a8: #ffffff60;
  --gray-a9: #ffffff6e;
  --gray-a10: #ffffff7b;
  --gray-a11: #ffffffb4;
  --gray-a12: #ffffffee;

  --gray-contrast: #ffffff;
  --gray-surface: rgba(0, 0, 0, 0.05);
  --gray-indicator: #6e6e6e;
  --gray-track: #6e6e6e;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark,
    .dark-theme {
      --gray-1: oklch(0% 0 none);
      --gray-2: oklch(18.4% 0 none);
      --gray-3: oklch(23.9% 0 none);
      --gray-4: oklch(27.5% 0 none);
      --gray-5: oklch(30.9% 0 none);
      --gray-6: oklch(34.7% 0 none);
      --gray-7: oklch(40.1% 0 none);
      --gray-8: oklch(48.7% 0 none);
      --gray-9: oklch(53.7% 0 none);
      --gray-10: oklch(58.4% 0 none);
      --gray-11: oklch(77% 0 none);
      --gray-12: oklch(94.9% 0 none);

      --gray-a1: color(display-p3 0 0 0 / 0);
      --gray-a2: color(display-p3 1 1 1 / 0.0706);
      --gray-a3: color(display-p3 1 1 1 / 0.1216);
      --gray-a4: color(display-p3 1 1 1 / 0.1569);
      --gray-a5: color(display-p3 1 1 1 / 0.1882);
      --gray-a6: color(display-p3 1 1 1 / 0.2275);
      --gray-a7: color(display-p3 1 1 1 / 0.2824);
      --gray-a8: color(display-p3 1 1 1 / 0.3765);
      --gray-a9: color(display-p3 1 1 1 / 0.4314);
      --gray-a10: color(display-p3 1 1 1 / 0.4824);
      --gray-a11: color(display-p3 1 1 1 / 0.7059);
      --gray-a12: color(display-p3 1 1 1 / 0.9333);

      --gray-contrast: #ffffff;
      --gray-surface: color(display-p3 0 0 0 / 5%);
      --gray-indicator: oklch(53.7% 0 none);
      --gray-track: oklch(53.7% 0 none);
    }
  }
}

body {
  font-family: var(--font-geist-sans), system-ui, sans-serif;
}

/* Improved Tabs styling */
.rt-TabsList {
  display: flex;
  gap: 1px;
  padding: 0 var(--space-2);
}

.rt-TabsTrigger {
  position: relative;
  padding: var(--space-3) var(--space-4);
  transition: all 0.2s ease;
  font-weight: 500;
  white-space: nowrap;
  border-radius: 0;
}

.rt-TabsTrigger[data-state="active"] {
  color: var(--accent-11);
}

.rt-TabsTrigger[data-state="active"]::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background: var(--accent-9);
}

/* ScrollArea for Tabs */
.rt-ScrollAreaRoot {
  overflow: hidden;
}

.rt-ScrollAreaViewport {
  width: 100%;
}

.rt-ScrollAreaScrollbar[data-orientation="horizontal"] {
  height: 8px;
  border-radius: 4px;
  background: var(--gray-a3);
}

.rt-ScrollAreaThumb[data-orientation="horizontal"] {
  background: var(--gray-a8);
  border-radius: 4px;
}

/* Improved Card styling */
.rt-Card {
  border: 1px solid var(--gray-a6);
  background: var(--color-background);
  transition: all 0.2s ease;
}

.rt-Card:hover {
  border-color: var(--gray-a7);
}

/* Improved TextArea styling */
.rt-TextArea {
  background: var(--gray-a2);
  border: 1px solid var(--gray-a6);
  transition: all 0.15s ease;
  font-family: var(--font-geist-mono), monospace;
}

.rt-TextArea:hover {
  border-color: var(--gray-a8);
}

.rt-TextArea:focus {
  border-color: var(--accent-8);
  box-shadow: 0 0 0 1px var(--accent-8);
}

/* Improved Button styling */
.rt-Button {
  font-weight: 500;
  transition: all 0.2s ease;
}

.rt-Button:hover {
  transform: translateY(-1px);
}

.rt-Button:active {
  transform: translateY(0);
}

/* Improved Badge styling */
.rt-Badge {
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* Improved ScrollArea styling */
.rt-ScrollAreaScrollbar {
  transition: all 0.2s ease;
  padding: 0 !important;
}

.rt-ScrollAreaThumb {
  background: var(--gray-a8);
}

/* Improved DropdownMenu styling */
.rt-DropdownMenuContent {
  border: 1px solid var(--gray-a6);
  background: var(--color-background);
}

.rt-DropdownMenuItem {
  transition: all 0.2s ease;
}

.rt-DropdownMenuItem:hover {
  background: var(--gray-a3);
}

/* Animation utilities */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

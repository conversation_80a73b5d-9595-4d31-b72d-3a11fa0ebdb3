"use client";

import { useAuth } from "@/hooks/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Loader from "@/components/loader";

export default function HomePage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      router.push(user ? "/dashboard/overview" : "/login");
    }
  }, [user, loading, router]);

  return <Loader />;
}

import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import "@radix-ui/themes/styles.css";
import { AuthProvider } from "@/hooks/auth-context";
import { Toaster } from "@/components/ui/toaster";
import { AgentsProvider } from "@/hooks/agents-context";
import { Providers } from "@/lib/providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ID8 Media Agents",
  description: "AI-powered content creation platform",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased dark`}
        suppressHydrationWarning
      >
        <Providers>
          <AuthProvider>
            <AgentsProvider>
              {children}
              <Toaster />
            </AgentsProvider>
          </AuthProvider>
        </Providers>
      </body>
    </html>
  );
}

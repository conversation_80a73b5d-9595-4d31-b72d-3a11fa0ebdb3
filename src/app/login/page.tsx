"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>lex, Heading, Text } from "@radix-ui/themes";
import { useAuth } from "@/hooks/auth-context";
import { useState } from "react";

export default function LoginPage() {
  const { signIn, signInWithGoogle } = useAuth();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    const formData = new FormData(e.currentTarget);
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    try {
      await signIn(email, password);
    } catch (error) {
      console.error(error);
      setError("Failed to sign in. Please check your credentials.");
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError("");
    setLoading(true);
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error(error);
      setError("Failed to sign in with Google.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card size="3" style={{ width: "100%", maxWidth: "400px" }}>
      <Flex direction="column" gap="5">
        <div className="text-center">
          <Heading size="8" mb="1">
            Welcome back
          </Heading>
          <Text size="2" color="gray">
            Sign in to your account to continue
          </Text>
        </div>

        {error && (
          <Text color="red" size="2" className="text-center">
            {error}
          </Text>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <Flex direction="column" gap="4">
            <div>
              <Text
                as="label"
                size="2"
                mb="2"
                weight="medium"
                className="block"
              >
                Email address
              </Text>
              <input
                name="email"
                type="email"
                placeholder="Enter your email"
                autoComplete="email"
                required
                className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-800 border-transparent rounded-md focus:border-blue-500 focus:bg-white dark:focus:bg-gray-900 focus:ring-2 focus:ring-blue-500 outline-none transition-all"
              />
            </div>

            <div>
              <Text
                as="label"
                size="2"
                mb="2"
                weight="medium"
                className="block"
              >
                Password
              </Text>
              <input
                name="password"
                type="password"
                placeholder="Enter your password"
                autoComplete="current-password"
                required
                className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-800 border-transparent rounded-md focus:border-blue-500 focus:bg-white dark:focus:bg-gray-900 focus:ring-2 focus:ring-blue-500 outline-none transition-all"
              />
            </div>

            <Button size="3" disabled={loading}>
              {loading ? "Signing in..." : "Sign in with Email"}
            </Button>
          </Flex>
        </form>

        <div className="relative">
          <div className="relative flex justify-center text-sm">
            <span className="px-2 text-gray-500">Or continue with</span>
          </div>
        </div>

        <Button
          size="3"
          variant="outline"
          disabled={loading}
          onClick={handleGoogleSignIn}
          className="w-full"
        >
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Sign in with Google
        </Button>
      </Flex>
    </Card>
  );
}

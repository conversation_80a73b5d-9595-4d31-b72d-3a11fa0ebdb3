import { doc, getDoc, getDocs, Query, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { InstagramPost } from "@/types";

export async function updatePost(postId: string, data: Partial<InstagramPost>) {
  const postRef = doc(db, "social_media_posts", postId);
  await setDoc(postRef, data, { merge: true });
}

export async function getPost(postId: string) {
  const postRef = doc(db, "social_media_posts", postId);
  const post = await getDoc(postRef);
  return post.data() as InstagramPost;
}

export async function getPosts(query: Query) {
  const posts = await getDocs(query);
  return posts.docs.map((doc) => doc.data()) as InstagramPost[];
}

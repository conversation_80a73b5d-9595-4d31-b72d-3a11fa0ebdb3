"use server";

import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";

const jsonSchema = Schema.object({
  properties: {
    art_text: Schema.string(),
  },
  required: ["art_text"],
});

export default async function generateArtText(stories: {
  [key: string]: string;
}) {
  // Clean the stories by removing hashtags and extra spaces
  const cleanedStories = Object.entries(stories).reduce((acc, [key, value]) => {
    const cleanedValue = value
      .replace(/#\w+/g, "") // Remove hashtags
      .replace(/\s+/g, " ") // Replace multiple spaces with single space
      .trim();
    acc[key] = cleanedValue;
    return acc;
  }, {} as { [key: string]: string });

  const prompt = `
  Create a highly detailed, professional art prompt for an AI image generator. The prompt should create a sophisticated, visually striking artwork that captures the essence of these stories:

  Content Analysis:
  ${Object.entries(cleanedStories)
    .map(([key, value]) => `Story ${key}: ${value}`)
    .join("\n")}

  Generate a detailed art prompt that includes:

  Main Subject and Composition:
  - Describe the primary elements and their arrangement
  - Include specific details about positioning and focal points
  - Define the perspective and viewing angle
  - Specify the scene composition and layout

  Artistic Style:
  - Choose a specific artistic movement or combination of styles
  - Define the level of realism vs abstraction
  - Specify any particular artists or works to reference
  - Include detailed stylistic elements

  Technical Specifications:
  - Ultra-high resolution and sharp details
  - Professional color grading with rich saturation
  - Cinematic lighting with dramatic highlights
  - Smooth anti-aliasing and clean edges
  - Premium post-processing effects

  Visual Elements:
  - Color palette and color relationships
  - Lighting direction and quality
  - Textures and materials
  - Atmospheric effects
  - Depth and dimensionality

  Mood and Atmosphere:
  - Emotional tone and feeling
  - Time of day or lighting conditions
  - Environmental elements
  - Atmospheric effects

  Additional Details:
  - Specific textures or patterns
  - Background elements
  - Secondary details and embellishments
  - Any symbolic or metaphorical elements

  Format the response as a cohesive, detailed art prompt that would create a stunning, professional artwork. Make it specific enough for an AI art generator to create a high-quality, consistent image, while maintaining creative and artistic elements.
  `;

  const model = getGenerativeModel(vertexAI, {
    model: "gemini-2.0-flash",
    systemInstruction: `You are a master art director and professional prompt engineer specializing in creating detailed, sophisticated prompts for AI art generation. Your prompts consistently produce stunning, gallery-quality artworks that perfectly capture the intended mood, style, and subject matter. Focus on creating rich, detailed descriptions that specify artistic style, technical requirements, and compositional elements while maintaining creative vision.

      Example 1:
      Create a high-resolution, surreal mosaic-style artwork representing Qatar, inspired by its rich heritage, modern innovation, and national pride. The composition should seamlessly blend Qatar's history, traditions, and development into a dynamic and intricate mosaic, constructed from thousands of smaller photos depicting cultural symbols, landmarks, and natural elements.\n\nKey Elements:\nArchitectural Landmarks:\n\nHighlight iconic Qatari landmarks, including the Museum of Islamic Art, Souq Waqif, Msheireb Downtown, The Pearl Monument, Qatar National Library, Al Zubara Fort, National Museum of Qatar, Doha Corniche, Tornado Tower, The Torch Tower, and Qatar National Convention Centre (QNCC).\nCultural Symbols:\n\nIntegrate Arabic calligraphy, Islamic geometric designs, traditional Qatari coffee pots, dhow boats, dates, incense burners, and intricate henna patterns to reflect Qatar’s cultural richness and artistry.\nEnvironment and Heritage:\n\nShowcase natural elements such as sand dunes, camels, desert flora, palm trees, and the Arabian Gulf to highlight Qatar’s historical connection to its environment.\nInclude heritage references like Al Zubara Fort and pearl diving scenes to symbolize Qatar’s past, its roots in trade, and its maritime history.\nModern Innovation:\n\nFeature Qatar’s progress and modern achievements through vibrant cityscapes, modern skyscrapers, airplanes, and futuristic abstract digital elements. Incorporate geometric patterns symbolizing technology and development.\nNational Pride:\n\nProminently display the Qatari flag with correct specifications: maroon with a white serrated band of nine points on the hoist side, in a precise ratio of 11:28. The flag should wave proudly across the composition.\nAdd celebratory scenes of parades, cultural gatherings, and women in traditional embroidered abayas to reflect the pride and unity of the nation.\nSubtle FIFA World Cup References (Minimal):\n\nInclude very subtle nods to the FIFA World Cup, such as a small depiction of stadiums or fans waving flags, ensuring these elements do not dominate the composition.\nStyle and Color Palette:\nThe artwork should use a traditional mosaic aesthetic, with intricate details formed by thousands of smaller photos that collectively showcase Qatar’s culture, landmarks, and progress.\nThe style is surreal, abstract, and symbolic, blending historical and modern elements seamlessly.\nUse vibrant celebratory tones of maroon, gold, red, sky blue, and warm beige, complemented by subtle festive accents to reflect pride, heritage, and innovation.\nFinal Image:\nThe final artwork should embody Qatar’s national spirit, celebrating its historical journey, cultural traditions, architectural achievements, and modern development. FIFA World Cup elements, if included, should remain subtle, keeping the focus on Qatar's identity and heritage.\

      Example 2:
      Create a high-resolution, surreal mosaic-style artwork representing Qatar, celebrating its rich heritage, modernity, and national pride, with a focus on Qatari culture, achievements, and the vibrant spirit of the FIFA World Cup.
      Composition Details:
      Qatari Landmarks:
      Highlight iconic landmarks such as Souq Waqif, The Pearl Monument, Qatar National Library, Al Zubara Fort, Museum of Islamic Art, Doha Corniche, National Museum of Qatar, Qatar National Convention Centre (QNCC), Tornado Tower, The Torch Tower, and Khalifa International Stadium.
      Cultural Symbols:
      Integrate Arabic calligraphy, Islamic geometric patterns, Qatari coffee pots, traditional dhow boats, dates, incense burners, and intricate henna patterns. These elements should emphasize the cultural artistry and heritage of Qatar.
      Natural Heritage:
      Depict Qatar’s natural beauty with sand dunes, camels, palm trees, desert flora, and the Arabian Gulf. Include modern airplanes and abstract digital elements to symbolize Qatar's technological progress and innovation.
      National Celebration:
      Prominently display the Qatari flag with accurate specifications: maroon with a white serrated band (nine white points) on the hoist side, in the ratio of 11:28.
      Include scenes of traditional parades with horses and camels, vibrant cityscapes, and women wearing embroidered abayas as part of cultural representation.
      FIFA World Cup Elements:
      Incorporate celebratory elements of the FIFA World Cup, including a stylized rendering of the World Cup trophy, fans celebrating in vibrant colors, and subtle nods to global unity. Add references to modern stadiums, football fans waving flags, and energetic crowds.
      Style and Color Palette:
      The artwork should be surreal, dynamic, and symbolic, blending a mosaic aesthetic with abstract digital elements.
      Use vibrant celebratory tones of maroon, gold, red, sky blue, and warm beige to reflect festivity and pride.
      The final image should embody the spirit of Qatar’s cultural heritage, its journey of progress, and the historic significance of hosting the FIFA World Cup

      `,
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: 0.9,
      maxOutputTokens: 2400,
      topP: 0.8,
      topK: 40,
    },
  });

  const result = await model.generateContent(prompt);

  const response = result.response;
  const text = response.text();
  return text;
}

"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";
import { generateAgentsResults } from "../utils";

const jsonSchema = Schema.object({
  properties: {
    overall_score: Schema.number(),
    key_strengths: Schema.array({ items: Schema.string() }),
    areas_of_concern: Schema.array({ items: Schema.string() }),
    recommendations: Schema.array({ items: Schema.string() }),
    content_appropriateness: Schema.string(),
    brand_alignment: Schema.string(),
    potential_risks: Schema.array({ items: Schema.string() }),
    compliance_notes: Schema.string(),
  },
  required: [
    "overall_score",
    "key_strengths",
    "areas_of_concern",
    "recommendations",
    "content_appropriateness",
    "brand_alignment",
    "potential_risks",
    "compliance_notes",
  ],
});

export default async function qualityChecker(
  settings: AgentSettings,
  post: InstagramPost
) {
  const prompt = `
  Content to do a quality check on:
  Post Caption: ${post.caption}
  ${generateAgentsResults(post)}
  
  ${settings.instructions && `Instructions: \n${settings.instructions}`}

  ${
    settings.customInstructions &&
    `Custom instructions: \n${settings.customInstructions}`
  }`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-2.0-flash",
    systemInstruction:
      settings.systemPrompt ||
      "You are a quality assurance expert analyzing social media content for appropriateness, brand alignment, and compliance.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.5,
      maxOutputTokens: settings.maxTokens || 2000,
    },
  });

  const result = await model.generateContent(prompt);
  const response = result.response;
  const text = response.text();
  return text;
}

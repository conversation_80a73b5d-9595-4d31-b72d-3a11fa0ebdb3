"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";
import { generateAgentsResults } from "../utils";

const jsonSchema = Schema.object({
  properties: {
    tone: Schema.string(),
    sentiment: Schema.string(),
    sentiment_score: Schema.number(),
  },
  required: ["tone", "sentiment", "sentiment_score"],
});

export default async function sentimentAnalyzer(
  settings: AgentSettings,
  post: InstagramPost
) {
  const prompt = `
  Content to do a sentiment analysis on:
  Post Caption: ${post.caption}
  ${generateAgentsResults(post)}

  ${settings.instructions && `Instructions: \n${settings.instructions}`}

  ${
    settings.customInstructions &&
    `Custom instructions: \n${settings.customInstructions}`
  }`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-2.0-flash",
    systemInstruction:
      settings.systemPrompt ||
      "You are a helpful sentiment analyzer. You are analyzing a social media post and providing a detailed analysis of the content.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.5,
      maxOutputTokens: settings.maxTokens || 2000,
    },
  });

  const result = await model.generateContent(prompt);

  const response = result.response;
  const text = response.text();
  return text;
}

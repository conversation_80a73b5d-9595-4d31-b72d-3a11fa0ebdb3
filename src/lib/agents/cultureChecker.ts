"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";
import { generateAgentsResults } from "../utils";

const jsonSchema = Schema.object({
  properties: {
    alignment_score: Schema.number(),
    cultural_elements: Schema.array({ items: Schema.string() }),
    brand_voice_match: Schema.object({
      properties: {
        score: Schema.number(),
        observations: Schema.array({ items: Schema.string() }),
      },
    }),
    recommendations: Schema.array({ items: Schema.string() }),
    sensitivity_concerns: Schema.array({ items: Schema.string() }),
  },
  required: [
    "alignment_score",
    "cultural_elements",
    "brand_voice_match",
    "recommendations",
    "sensitivity_concerns",
  ],
});

export default async function cultureChecker(
  settings: AgentSettings,
  post: InstagramPost
) {
  const prompt = `
  Content to do a culture check on:
  Post Caption: ${post.caption}
  ${generateAgentsResults(post)}
  
  ${settings.instructions && `Instructions: \n${settings.instructions}`}

  ${
    settings.customInstructions &&
    `Custom instructions: \n${settings.customInstructions}`
  }`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-2.0-flash",
    systemInstruction:
      settings.systemPrompt ||
      "You are a culture and brand alignment expert analyzing social media content for cultural sensitivity and brand voice alignment.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.5,
      maxOutputTokens: settings.maxTokens || 2000,
    },
  });

  const result = await model.generateContent(prompt);
  const response = result.response;
  const text = response.text();
  return text;
}

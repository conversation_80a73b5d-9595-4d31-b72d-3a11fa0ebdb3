"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";
import { generateAgentsResults } from "../utils";

const jsonSchema = Schema.object({
  properties: {
    story: Schema.string(),
    story_type: Schema.string(),
    notes: Schema.string(),
  },
  required: ["story", "story_type", "notes"],
});

export default async function storyGenerator(
  settings: AgentSettings,
  post: InstagramPost
) {
  const prompt = `
  Content to generate a story from:
  Post Caption: ${post.caption}
  ${generateAgentsResults(post)}

  ${settings.instructions && `Instructions: \n${settings.instructions}`}

  ${
    settings.customInstructions &&
    `Custom instructions: \n${settings.customInstructions}`
  }`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-2.0-flash",
    systemInstruction:
      settings.systemPrompt ||
      "You are a helpful story generator. You are generating a story from a social media post.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.5,
      maxOutputTokens: settings.maxTokens || 2000,
    },
  });

  const result = await model.generateContent(prompt);

  const response = result.response;
  const text = response.text();
  return text;
}

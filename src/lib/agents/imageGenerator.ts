"use server";
import { AgentSettings, InstagramPost } from "@/types";
import { storage } from "@/lib/firebase";
import { ref, getDownloadURL } from "firebase/storage";
import { GoogleAuth } from "google-auth-library";

interface ImageGenerationResult {
  caption: string;
  storageUrl: string;
}

const LOCATION = process.env.GOOGLE_CLOUD_LOCATION || "me-central1";

// Initialize Google Auth client with service account credentials
const auth = new GoogleAuth({
  scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "qnd-platform",
  credentials: {
    type: "service_account",
    project_id: "qnd-platform",
    private_key_id: "d7810aee3f03d54751d93e449fa3b3d8a1f657e5",
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      "<EMAIL>",
    client_id: "107042730709320593986",
    universe_domain: "googleapis.com",
  },
});

async function getAccessToken(): Promise<string> {
  try {
    const client = await auth.getClient();
    const token = await client.getAccessToken();
    return token.token || "";
  } catch (error) {
    console.error("Error getting access token:", error);
    throw new Error(
      `Failed to get access token: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

async function generateImageWithVertexAI(
  post: InstagramPost,
  settings: AgentSettings
): Promise<string> {
  const artDirection = post.agents.artGenerator!;

  // Enhanced prompt structure for better image generation
  const prompt = `Create a highly detailed, professional ${
    artDirection.visual_theme.style
  } artwork with the following specifications:

Main Subject and Composition:
- Primary Focus: ${artDirection.composition.focal_points.join(", ")}
- Layout: ${artDirection.composition.layout}
- Perspective: Dynamic and engaging viewpoint
- Scene Composition: ${artDirection.storytelling.visual_flow}

Artistic Style:
- Primary Style: ${artDirection.visual_theme.style}
- Mood: ${artDirection.visual_theme.mood}
- Atmosphere: ${artDirection.visual_theme.atmosphere}
- Visual Impact: ${artDirection.storytelling.emotional_impact}

Technical Specifications:
- Ultra-high resolution with sharp details
- Professional color grading
- Cinematic lighting with dramatic highlights
- Smooth anti-aliasing and clean edges
- Premium post-processing effects

Visual Elements:
- Color Palette: ${artDirection.color_palette
    .map((c) => `${c.color} for ${c.purpose}`)
    .join(", ")}
- Lighting: Dramatic and atmospheric
- Textures: Rich and detailed
- Depth: Multiple layers with foreground, midground, and background elements

Key Elements:
${artDirection.key_elements
  .map((e) => `- ${e.element} positioned ${e.placement}`)
  .join("\n")}

Additional Requirements:
- No text or typography elements
- Avoid human faces or recognizable individuals
- Focus on abstract or artistic interpretation
- Create a gallery-quality aesthetic
- Maintain professional and safe content

Style Enhancements:
- Add subtle light effects and atmospheric elements
- Include delicate details and intricate patterns
- Create smooth transitions between elements
- Incorporate subtle texture overlays
- Maintain high contrast and visual hierarchy`.trim();

  try {
    const accessToken = await getAccessToken();
    const endpoint = `https://${LOCATION}-aiplatform.googleapis.com/v1/projects/${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}/locations/${LOCATION}/publishers/google/models/${settings.model}:predict`;

    const requestBody = {
      instances: [{ prompt }],
      parameters: {
        sampleCount: 1,
        enhancePrompt: false,
        aspectRatio: "16:9",
        personGeneration: "allow_all",
        safetySetting: "block_only_high",
        imageSize: "1920x1080",
        negativePrompt: `
          text, words, letters, numbers, watermark, signature, logo, 
          blurry, distorted, low quality, oversaturated, overexposed,
          bad anatomy, deformed features, disfigured, mutated,
          inappropriate content, offensive content, disturbing imagery,
          political content, religious symbols, branded content,
          social media elements, user interface elements, Arabic text,
          Arabic characters, Arabic letters, Arabic symbols, Arabic numbers,
          Arabic punctuation, Arabic diacritics, Arabic ligatures,
          Arabic glyphs, Arabic characters, Arabic letters, Arabic symbols,
          Arabic numbers, Arabic punctuation, Arabic diacritics, Arabic ligatures,
          Arabic glyphs, Arabic characters, Arabic letters, Arabic symbols,
          Arabic numbers, Arabic punctuation, Arabic diacritics, Arabic ligatures,
          Arabic glyphs, Arabic characters, Arabic letters, Arabic symbols,
        `
          .replace(/\s+/g, " ")
          .trim(),
        storageUri: "gs://qnd-platform.firebasestorage.app/images_from_vertex",
      },
    };

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Vertex AI API error response:", errorText);
      throw new Error(
        `HTTP error! status: ${response.status}, details: ${errorText}`
      );
    }

    const data = await response.json();

    // Validate response structure
    if (
      !data ||
      !Array.isArray(data.predictions) ||
      data.predictions.length === 0
    ) {
      console.error("Invalid response structure:", data);
      throw new Error(
        "Invalid response from Vertex AI: No predictions returned"
      );
    }

    const prediction = data.predictions[0];
    if (!prediction || !prediction.gcsUri) {
      console.error("Invalid prediction structure:", prediction);
      throw new Error("Invalid prediction format: Missing GCS URI");
    }

    // Extract the path from the GCS URI
    const gcsPath = prediction.gcsUri.replace(
      "gs://qnd-platform.firebasestorage.app/",
      ""
    );

    // Get a reference to the file in Firebase Storage
    const storageRef = ref(storage, gcsPath);

    // Get the download URL with authentication token
    const downloadUrl = await getDownloadURL(storageRef);

    return downloadUrl;
  } catch (error) {
    console.error("Error generating image:", error);
    const enhancedError = new Error(
      `Failed to generate image: ${
        error instanceof Error ? error.message : "Unknown error"
      }. Model: ${settings.model}, Location: ${LOCATION}`
    );
    throw enhancedError;
  }
}

export default async function imageGenerator(
  settings: AgentSettings,
  post: InstagramPost
): Promise<ImageGenerationResult> {
  try {
    // Generate the image and get the download URL
    const storageUrl = await generateImageWithVertexAI(post, settings);

    return {
      caption: post.agents.storyGenerator?.story || post.caption,
      storageUrl,
    };
  } catch (error) {
    console.error("Error in image generator:", error);
    throw error;
  }
}

"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";

const jsonSchema = Schema.object({
  properties: {
    summary: Schema.string(),
    engagement_potential: Schema.string(),
    content_quality: Schema.string(),
  },
  required: ["summary", "engagement_potential", "content_quality"],
});

export default async function textAnalyzer(
  settings: AgentSettings,
  post: InstagramPost
) {
  const prompt = `
  Content to analyze:
  Post ID: ${post.id}
  Post Caption: ${post.caption}
  Post User Full Name: ${post.user_full_name}
  Post Comment Count: ${post.comment_count}
  Post Like Count: ${post.like_count}
  Post Hashtags: ${post.hashtags} 
  Content Type: ${post.content_type}
  Platform: ${post.platform}
  
  ${settings.instructions && `Instructions: \n${settings.instructions}`}

  ${
    settings.customInstructions &&
    `Custom instructions: \n${settings.customInstructions}`
  }`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-2.0-flash",
    systemInstruction:
      settings.systemPrompt ||
      "You are a helpful assistant. You are analyzing a social media post and providing a detailed analysis of the content.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.5,
      maxOutputTokens: settings.maxTokens || 2000,
    },
  });

  const result = await model.generateContent(prompt);

  const response = result.response;
  const text = response.text();
  return text;
}

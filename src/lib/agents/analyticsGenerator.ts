"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";

const jsonSchema = Schema.object({
  properties: {
    performance_summary: Schema.string(),
    top_performing_content: Schema.array({ items: Schema.string() }),
    engagement_patterns: Schema.array({ items: Schema.string() }),
    content_recommendations: Schema.array({ items: Schema.string() }),
    hashtag_effectiveness: Schema.array({ items: Schema.string() }),
    sentiment_impact: Schema.string(),
    quality_correlation: Schema.string(),
    cultural_alignment_impact: Schema.string(),
    key_metrics: Schema.object({
      properties: {
        avg_engagement_rate: Schema.number(),
        top_hashtag_performance: Schema.number(),
        sentiment_correlation: Schema.number(),
        quality_impact_score: Schema.number(),
      },
      required: [
        "avg_engagement_rate",
        "top_hashtag_performance",
        "sentiment_correlation",
        "quality_impact_score",
      ],
    }),
  },
  required: [
    "performance_summary",
    "top_performing_content",
    "engagement_patterns",
    "content_recommendations",
    "hashtag_effectiveness",
    "sentiment_impact",
    "quality_correlation",
    "cultural_alignment_impact",
    "key_metrics",
  ],
});

export default async function analyticsGenerator(
  settings: AgentSettings,
  posts: InstagramPost[]
) {
  // Prepare data for analysis
  const analyticsData = {
    posts: posts.map((post) => ({
      caption: post.caption,
      engagement: post.like_count + post.comment_count,
      hashtags: post.hashtags,
      sentiment: post.agents.sentimentAnalyzer?.sentiment || "neutral",
      quality_score: post.agents.qualityChecker?.overall_score || 0,
      cultural_alignment: post.agents.cultureChecker?.alignment_score || 0,
      platform: post.platform,
      content_type: post.content_type,
      created_at: post.created_at,
    })),
  };

  const prompt = `
  You are a professional social media analytics expert. Analyze this social media performance data and provide detailed insights:
  ${JSON.stringify(analyticsData, null, 2)}
  
  Focus on:
  1. Overall performance trends
  2. Content characteristics that drive engagement
  3. Patterns in user behavior and engagement
  4. Impact of sentiment on performance
  5. Correlation between quality scores and engagement
  6. Cultural alignment effectiveness
  7. Hashtag strategy effectiveness
  8. Specific actionable recommendations

  ${
    settings.instructions
      ? `Additional Instructions:\n${settings.instructions}`
      : ""
  }
  ${
    settings.customInstructions
      ? `Custom Instructions:\n${settings.customInstructions}`
      : ""
  }
  
  Provide a comprehensive analysis with specific metrics and actionable insights.`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-1.5-pro",
    systemInstruction:
      settings.systemPrompt ||
      "You are an expert social media analytics AI that provides detailed, data-driven insights and actionable recommendations.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.7,
      maxOutputTokens: settings.maxTokens || 2048,
    },
  });

  const result = await model.generateContent(prompt);
  const response = result.response;
  const text = response.text();
  return text;
}

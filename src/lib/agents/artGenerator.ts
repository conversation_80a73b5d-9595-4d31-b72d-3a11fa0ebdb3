"use server";

import { AgentSettings, InstagramPost } from "@/types";
import { vertexAI } from "../firebase";
import { getGenerativeModel, Schema } from "firebase/vertexai";
import { generateAgentsResults } from "../utils";

const jsonSchema = Schema.object({
  properties: {
    visual_theme: Schema.object({
      properties: {
        mood: Schema.string(),
        style: Schema.string(),
        atmosphere: Schema.string(),
      },
    }),
    color_palette: Schema.array({
      items: Schema.object({
        properties: {
          color: Schema.string(),
          purpose: Schema.string(),
        },
      }),
    }),
    composition: Schema.object({
      properties: {
        layout: Schema.string(),
        focal_points: Schema.array({ items: Schema.string() }),
        balance_notes: Schema.string(),
      },
    }),
    key_elements: Schema.array({
      items: Schema.object({
        properties: {
          element: Schema.string(),
          significance: Schema.string(),
          placement: Schema.string(),
        },
      }),
    }),
    storytelling: Schema.object({
      properties: {
        narrative_elements: Schema.array({ items: Schema.string() }),
        visual_flow: Schema.string(),
        emotional_impact: Schema.string(),
      },
    }),
    technical_recommendations: Schema.array({ items: Schema.string() }),
  },
  required: [
    "visual_theme",
    "color_palette",
    "composition",
    "key_elements",
    "storytelling",
    "technical_recommendations",
  ],
});

export default async function artGenerator(
  settings: AgentSettings,
  post: InstagramPost
) {
  const prompt = `
  Content to create art direction from:
  Post Caption: ${post.caption}
  ${generateAgentsResults(post)}
  
  ${settings.instructions && `Instructions: \n${settings.instructions}`}

  ${
    settings.customInstructions &&
    `Custom instructions: \n${settings.customInstructions}`
  }`;

  const model = getGenerativeModel(vertexAI, {
    model: settings.model || "gemini-2.0-flash",
    systemInstruction:
      settings.systemPrompt ||
      "You are an art director creating visual storytelling narratives and artistic direction from social media captions.",
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: jsonSchema,
      temperature: settings.temperature || 0.7,
      maxOutputTokens: settings.maxTokens || 2000,
    },
  });

  const result = await model.generateContent(prompt);
  const response = result.response;
  const text = response.text();
  return text;
}

export const serviceAccount = {
  type: "service_account",
  project_id: "qnd-platform",
  private_key_id: "6324718c5074f5c621ecabaeca5535d450b931db",
  private_key:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: "<EMAIL>",
  client_id: "107042730709320593986",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url:
    "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-mjjrt%40qnd-platform.iam.gserviceaccount.com",
  universe_domain: "googleapis.com",
};

export const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

export const AGENTS = {
  textAnalyzer: "textAnalyzer",
  sentimentAnalyzer: "sentimentAnalyzer",
  storyGenerator: "storyGenerator",
  qualityChecker: "qualityChecker",
  cultureChecker: "cultureChecker",
  artGenerator: "artGenerator",
  imageGenerator: "imageGenerator",
};

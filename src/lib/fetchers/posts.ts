import { db } from "@/lib/firebase";
import { InstagramPost } from "@/types";
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  startAfter,
  getCountFromServer,
  QueryDocumentSnapshot,
  DocumentData,
} from "firebase/firestore";

interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

interface MosaicGeneratorResult {
  url: string;
  prompt: string;
  processed_at?: FirestoreTimestamp;
}

export interface PostWithDocId extends InstagramPost {
  docId: string;
  agents: InstagramPost["agents"] & {
    mosaicGenerator?: MosaicGeneratorResult;
  };
}

export interface FetchPostsOptions {
  page: number;
  pageSize: number;
  hashtag?: string;
  status?: string;
  search?: string;
}

export interface FetchPostsResult {
  posts: PostWithDocId[];
  totalItems: number;
  totalPages: number;
}

export async function fetchPosts({
  page,
  pageSize,
  hashtag,
  status,
  search,
}: FetchPostsOptions): Promise<FetchPostsResult> {
  const postsCollection = collection(db, "social_media_posts");

  // Build the base query
  let baseQuery = query(postsCollection, orderBy("timestamp", "desc"));

  // Add filters
  if (hashtag && hashtag !== "all") {
    baseQuery = query(baseQuery, where("hashtag", "==", hashtag));
  }
  if (status && status !== "all") {
    if (status === "ready") {
      baseQuery = query(baseQuery, where("agents.textAnalyzer", "==", null));
    } else {
      baseQuery = query(baseQuery, where("status", "==", status));
    }
  }

  // Get total count
  const snapshot = await getCountFromServer(baseQuery);
  const totalItems = snapshot.data().count;
  const totalPages = Math.ceil(totalItems / pageSize);

  // Get the cursor for pagination
  let lastVisible: QueryDocumentSnapshot<DocumentData> | null = null;
  if (page > 1) {
    // Get the last document of the previous page
    const cursorQuery = query(baseQuery, limit((page - 1) * pageSize));
    const cursorSnapshot = await getDocs(cursorQuery);
    const docs = cursorSnapshot.docs;
    lastVisible = docs[docs.length - 1] || null;
  }

  // Add pagination
  const paginatedQuery = query(
    baseQuery,
    limit(pageSize),
    ...(lastVisible ? [startAfter(lastVisible)] : [])
  );

  // Get posts
  const querySnapshot = await getDocs(paginatedQuery);
  let posts = querySnapshot.docs.map((doc) => ({
    ...(doc.data() as InstagramPost),
    docId: doc.id,
  }));

  // Apply search filter client-side if needed
  if (search) {
    const searchLower = search.toLowerCase();
    posts = posts.filter(
      (post) =>
        post.caption?.toLowerCase().includes(searchLower) ||
        post.user_username?.toLowerCase().includes(searchLower)
    );
  }

  return {
    posts,
    totalItems,
    totalPages,
  };
}

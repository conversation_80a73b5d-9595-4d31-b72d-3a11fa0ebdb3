export const fetchInstagramData = async (
  hashtag: string,
  pagination?: string
) => {
  const url = `${
    process.env.NEXT_PUBLIC_X_RAPIDAPI_INSTAGRAM_URL
  }${hashtag}&feed_type=recent${
    pagination ? `&pagination_token=${pagination}` : ""
  }`;
  const options = {
    method: "GET",
    headers: {
      "x-rapidapi-key": process.env.NEXT_PUBLIC_X_RAPIDAPI_KEY || "",
      "x-rapidapi-host": process.env.NEXT_PUBLIC_X_RAPIDAPI_HOST || "",
    },
  };

  try {
    const response = await fetch(url, options);
    const result = await response.text();
    return result;
  } catch (error) {
    console.error(error);
    return null;
  }
};

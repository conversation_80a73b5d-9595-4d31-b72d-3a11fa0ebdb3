import { InstagramPost } from "@/types";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function generateAgentsResults(post: InstagramPost) {
  return `
  ${
    post.agents?.textAnalyzer &&
    `Text Analyzer Result: ${JSON.stringify(post.agents?.textAnalyzer)}}`
  }
  ${
    post.agents?.sentimentAnalyzer &&
    `Sentiment Analyzer Result: ${JSON.stringify(
      post.agents?.sentimentAnalyzer
    )}}`
  }
  ${
    post.agents?.storyGenerator &&
    `Story Generator Result: ${JSON.stringify(post.agents?.storyGenerator)}}`
  }
  ${
    post.agents?.qualityChecker &&
    `Quality Checker Result: ${JSON.stringify(post.agents?.qualityChecker)}}`
  }
  ${
    post.agents?.cultureChecker &&
    `Culture Checker Result: ${JSON.stringify(post.agents?.cultureChecker)}}`
  }
  ${
    post.agents?.artGenerator &&
    `Art Generator Result: ${JSON.stringify(post.agents?.artGenerator)}}`
  }
  `;
}

export function postStatus(status: string) {
  switch (status) {
    case "processing":
      return {
        text: "Processing",
        badge: "processing",
      };
    case "text_analyzing":
      return {
        text: "Analyzed",
        badge: "text_analyzing",
      };
    case "sentiment_analyzing":
      return {
        text: "Sentiment Analyzed",
        badge: "sentiment_analyzing",
      };
    case "story_generating":
      return {
        text: "Story Generated",
        badge: "story_generating",
      };
    case "quality_checking":
      return {
        text: "Quality Checked",
        badge: "quality_checking",
      };
    case "culture_checking":
      return {
        text: "Culture Checked",
        badge: "culture_checking",
      };
    case "art_generating":
      return {
        text: "Art Generated",
        badge: "art_generating",
      };
    case "image_generating":
      return {
        text: "Image Generated",
        badge: "image_generating",
      };
    case "completed":
      return {
        text: "Completed",
        badge: "completed",
      };
    default:
      return {
        text: "Failed",
        badge: "failed",
      };
  }
}

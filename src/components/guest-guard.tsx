"use client";

import { useAuth } from "@/hooks/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Loader from "./loader";

export function GuestGuard({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push("/dashboard/overview");
    }
  }, [user, loading, router]);

  if (loading || user) {
    return <Loader />;
  }

  return <>{children}</>;
}

"use client";

import { useRef, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Cross2Icon,
  CheckIcon,
  InfoCircledIcon,
  ExclamationTriangleIcon,
  ResetIcon,
} from "@radix-ui/react-icons";

export interface LogMessage {
  id: string;
  type: "info" | "error" | "success" | "warning";
  message: string;
  timestamp: Date;
  details?: string;
  data?: object; // Changed to object type for JSON-serializable data
}

interface ConsoleProps {
  logs: LogMessage[];
  onClearLogs: () => void;
  title?: string;
  height?: string;
  showDataPreview?: boolean;
}

export function Console({
  logs,
  onClearLogs,
  title = "Console",
  height = "400px",
  showDataPreview = false,
}: ConsoleProps) {
  const [logFilter, setLogFilter] = useState<LogMessage["type"] | "all">("all");
  const consoleRef = useRef<HTMLDivElement>(null);
  const [isConsoleHovered, setIsConsoleHovered] = useState(false);

  // Update console scroll only when not hovered
  useEffect(() => {
    if (consoleRef.current && !isConsoleHovered) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [logs, isConsoleHovered]);

  const filteredLogs =
    logFilter === "all" ? logs : logs.filter((log) => log.type === logFilter);

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">{title}</h3>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Button
              variant={logFilter === "all" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setLogFilter("all")}
              className="h-7 px-2"
            >
              All
            </Button>
            <Button
              variant={logFilter === "info" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setLogFilter("info")}
              className="h-7 px-2"
            >
              <InfoCircledIcon className="mr-1 h-3 w-3" />
              Info
            </Button>
            <Button
              variant={logFilter === "success" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setLogFilter("success")}
              className="h-7 px-2"
            >
              <CheckIcon className="mr-1 h-3 w-3" />
              Success
            </Button>
            <Button
              variant={logFilter === "warning" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setLogFilter("warning")}
              className="h-7 px-2"
            >
              <ExclamationTriangleIcon className="mr-1 h-3 w-3" />
              Warning
            </Button>
            <Button
              variant={logFilter === "error" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setLogFilter("error")}
              className="h-7 px-2"
            >
              <Cross2Icon className="mr-1 h-3 w-3" />
              Error
            </Button>
          </div>
          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearLogs}
              className="h-7 px-2"
            >
              <ResetIcon className="mr-1 h-3 w-3" />
              Clear
            </Button>
          </div>
        </div>
      </div>
      <div className="relative">
        <div
          ref={consoleRef}
          className="overflow-y-auto rounded-md border bg-muted p-4 font-mono text-sm"
          style={{ height }}
          onMouseEnter={() => setIsConsoleHovered(true)}
          onMouseLeave={() => setIsConsoleHovered(false)}
        >
          <div className="space-y-2">
            {filteredLogs.map((log) => (
              <div
                key={log.id}
                className="rounded border bg-background p-2 transition-colors hover:bg-accent"
              >
                <div
                  className={`flex items-start gap-2 ${
                    log.type === "error"
                      ? "text-destructive"
                      : log.type === "success"
                      ? "text-green-500"
                      : log.type === "warning"
                      ? "text-yellow-500"
                      : "text-muted-foreground"
                  }`}
                >
                  <span className="whitespace-nowrap text-xs opacity-50">
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                  <span className="flex-1 font-medium">
                    {log.type === "error" && (
                      <Cross2Icon className="mr-1 h-3 w-3 inline" />
                    )}
                    {log.type === "success" && (
                      <CheckIcon className="mr-1 h-3 w-3 inline" />
                    )}
                    {log.type === "warning" && (
                      <ExclamationTriangleIcon className="mr-1 h-3 w-3 inline" />
                    )}
                    {log.type === "info" && (
                      <InfoCircledIcon className="mr-1 h-3 w-3 inline" />
                    )}
                    {log.message}
                  </span>
                </div>
                {log.details && (
                  <div className="mt-1 ml-[4.5rem] text-xs text-muted-foreground">
                    {log.details}
                  </div>
                )}
                {showDataPreview && log.data && (
                  <div className="mt-2 ml-[4.5rem] text-xs">
                    <pre className="bg-muted p-2 rounded overflow-x-auto">
                      {JSON.stringify(log.data, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        {filteredLogs.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-muted-foreground">No logs to display</p>
          </div>
        )}
      </div>
    </div>
  );
}

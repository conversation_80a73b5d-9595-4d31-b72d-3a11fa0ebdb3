"use client";

import { Button, DropdownMenu } from "@radix-ui/themes";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { MoonIcon, SunIcon, DesktopIcon } from "@radix-ui/react-icons";

export function ThemeSwitcher() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <Button variant="ghost" className="gap-2">
          {theme === "light" ? (
            <SunIcon className="h-4 w-4" />
          ) : theme === "dark" ? (
            <MoonIcon className="h-4 w-4" />
          ) : (
            <DesktopIcon className="h-4 w-4" />
          )}
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Item onClick={() => setTheme("light")}>
          <SunIcon className="h-4 w-4 mr-2" />
          Light
        </DropdownMenu.Item>
        <DropdownMenu.Item onClick={() => setTheme("dark")}>
          <MoonIcon className="h-4 w-4 mr-2" />
          Dark
        </DropdownMenu.Item>
        <DropdownMenu.Item onClick={() => setTheme("system")}>
          <DesktopIcon className="h-4 w-4 mr-2" />
          System
        </DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}

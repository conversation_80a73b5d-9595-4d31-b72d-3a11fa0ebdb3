"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import {
  GearIcon,
  ExitIcon,
  PersonIcon,
  HeartIcon,
  CheckCircledIcon,
  MixIcon,
  ColorWheelIcon,
  ImageIcon,
  MagnifyingGlassIcon,
  ChatBubbleIcon,
  DrawingPinIcon,
  BarChartIcon,
  HamburgerMenuIcon,
  Cross1Icon,
  ChevronRightIcon,
  DashboardIcon,
  RocketIcon,
  LayersIcon,
  GridIcon,
  FileTextIcon,
  StarIcon,
  StackIcon,
  Component2Icon,
  CubeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  DotsHorizontalIcon,
} from "@radix-ui/react-icons";
import { DatabaseIcon, ActivityIcon, SettingsIcon } from "lucide-react";
import { Badge } from "./ui/badge";
import { useAuth } from "@/hooks/auth-context";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: Array<{
    name: string;
    href: string;
    icon: React.ComponentType<{ className?: string }>;
    badge?: string;
    children?: Array<{
      name: string;
      href: string;
      icon: React.ComponentType<{ className?: string }>;
      badge?: string;
    }>;
  }>;
}

const navigation: NavigationItem[] = [
  {
    name: "Main",
    href: "/dashboard/overview",
    icon: DashboardIcon,
    children: [
      {
        name: "Overview",
        href: "/dashboard/overview",
        icon: DashboardIcon,
      },
      { name: "Analytics", href: "/dashboard/analytics", icon: BarChartIcon },
      { name: "Statistics", href: "/dashboard/stats", icon: GridIcon },
    ],
  },
  {
    name: "Data Ingestion",
    href: "/dashboard/ingestion",
    icon: DatabaseIcon,
    badge: "New",
    children: [
      {
        name: "Dashboard",
        href: "/dashboard/ingestion",
        icon: DashboardIcon,
      },
      {
        name: "Stream Monitor",
        href: "/dashboard/ingestion/monitor",
        icon: ActivityIcon,
      },
      {
        name: "Configuration",
        href: "/dashboard/ingestion/config",
        icon: SettingsIcon,
      },
      {
        name: "Logs",
        href: "/dashboard/ingestion/logs",
        icon: FileTextIcon,
      },
    ],
  },
  {
    name: "Social Media",
    href: "/dashboard",
    icon: ChatBubbleIcon,
    children: [
      { name: "Fetched Posts", href: "/dashboard/posts", icon: DrawingPinIcon },
      {
        name: "Agent Runner",
        href: "/dashboard/agent-runner",
        icon: RocketIcon,
        badge: "Alpha",
      },
      {
        name: "Content Fetcher",
        href: "/dashboard",
        icon: MagnifyingGlassIcon,
      },
      {
        name: "Instagram Comments",
        href: "/dashboard/instagram-comments",
        icon: ChatBubbleIcon,
      },
      {
        name: "Posts Migration",
        href: "/dashboard/posts-migration",
        icon: StackIcon,
      },
      {
        name: "Social to Image",
        href: "/dashboard/images",
        icon: ImageIcon,
      },
      {
        name: "Social to Mosaic",
        href: "/dashboard/mosaics",
        icon: LayersIcon,
      },
    ],
  },
  {
    name: "QND Tools",
    href: "/dashboard/qnd",
    icon: StarIcon,
    children: [
      {
        name: "Image Generator",
        href: "/dashboard/comment-to-image",
        icon: ImageIcon,
      },
      {
        name: "Generated Images",
        href: "/dashboard/generated-images",
        icon: StackIcon,
      },
      {
        name: "Mosaic Generator",
        href: "/dashboard/instagram-mosaic",
        icon: LayersIcon,
      },
      {
        name: "Mosaic Gallery",
        href: "/dashboard/instagram-mosaic/gallery",
        icon: GridIcon,
      },
      {
        name: "Mosaics",
        href: "/dashboard/mosaics",
        icon: LayersIcon,
      },
      {
        name: "Classic Mosaic",
        href: "/dashboard/mosaic-generator",
        icon: Component2Icon,
      },
    ],
  },
  {
    name: "AI Tools",
    href: "/dashboard/ai",
    icon: CubeIcon,
    children: [
      {
        name: "Workers",
        href: "/dashboard/workers",
        icon: Component2Icon,
        children: [
          {
            name: "Text Analyzer",
            href: "/dashboard/workers/textAnalyzer",
            icon: FileTextIcon,
          },
          {
            name: "Sentiment",
            href: "/dashboard/workers/sentimentAnalyzer",
            icon: HeartIcon,
          },
          {
            name: "Story Generator",
            href: "/dashboard/workers/storyGenerator",
            icon: ChatBubbleIcon,
          },
          {
            name: "Quality Assurance",
            href: "/dashboard/workers/qualityChecker",
            icon: CheckCircledIcon,
          },
          {
            name: "Culture Fit",
            href: "/dashboard/workers/cultureChecker",
            icon: MixIcon,
          },
          {
            name: "Art Director",
            href: "/dashboard/workers/artGenerator",
            icon: ColorWheelIcon,
          },
          {
            name: "Image Generator",
            href: "/dashboard/workers/imageGenerator",
            icon: ImageIcon,
          },
        ],
      },
      {
        name: "Agents",
        href: "/dashboard/agents",
        icon: RocketIcon,
        children: [
          {
            name: "Text Analyzer",
            href: "/dashboard/agents/textAnalyzer",
            icon: FileTextIcon,
          },
          {
            name: "Sentiment",
            href: "/dashboard/agents/sentimentAnalyzer",
            icon: HeartIcon,
          },
          {
            name: "Story Generator",
            href: "/dashboard/agents/storyGenerator",
            icon: ChatBubbleIcon,
          },
          {
            name: "Quality Assurance",
            href: "/dashboard/agents/qualityChecker",
            icon: CheckCircledIcon,
          },
          {
            name: "Culture Fit",
            href: "/dashboard/agents/cultureChecker",
            icon: MixIcon,
          },
          {
            name: "Art Director",
            href: "/dashboard/agents/artGenerator",
            icon: ColorWheelIcon,
          },
          {
            name: "Image Generator",
            href: "/dashboard/agents/imageGenerator",
            icon: ImageIcon,
          },
        ],
      },
    ],
  },
  { name: "Profile", href: "/dashboard/profile", icon: PersonIcon },
  { name: "Settings", href: "/dashboard/settings", icon: GearIcon },
];

// Enhanced Navigation component with collapsible sections and search
const NavigationMenu = ({ pathname }: { pathname: string }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set()
  );

  // Initialize expanded sections based on current path
  useEffect(() => {
    const expanded = new Set<string>();
    navigation.forEach((item) => {
      if (item.children) {
        const hasActiveChild = item.children.some((child) => {
          if (child.children) {
            return child.children.some(
              (subChild) => pathname === subChild.href
            );
          }
          return pathname === child.href;
        });
        if (hasActiveChild || pathname.startsWith(item.href)) {
          expanded.add(item.href);

          // Also expand sub-sections if needed
          item.children.forEach((child) => {
            if (
              child.children &&
              child.children.some((subChild) => pathname === subChild.href)
            ) {
              expanded.add(child.href);
            }
          });
        }
      }
    });
    setExpandedSections(expanded);
  }, [pathname]);

  const toggleSection = (href: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(href)) {
        newSet.delete(href);
      } else {
        newSet.add(href);
      }
      return newSet;
    });
  };

  // Filter navigation based on search
  const filteredNavigation = navigation.filter((item) => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    const matchesItem = item.name.toLowerCase().includes(searchLower);

    if (matchesItem) return true;

    // Check if any children match
    if (item.children) {
      return item.children.some((child) => {
        const matchesChild = child.name.toLowerCase().includes(searchLower);
        if (matchesChild) return true;

        if (child.children) {
          return child.children.some((subChild) =>
            subChild.name.toLowerCase().includes(searchLower)
          );
        }
        return false;
      });
    }

    return false;
  });

  const renderNavigationItem = (
    item: NavigationItem,
    level: number = 0,
    parentExpanded: boolean = true
  ) => {
    const isActive = pathname === item.href;
    const isExpanded = expandedSections.has(item.href);
    const hasChildren = item.children && item.children.length > 0;

    const paddingLeft = level === 0 ? "pl-3" : level === 1 ? "pl-6" : "pl-9";
    const textSize = level === 0 ? "text-sm" : "text-xs";
    const iconSize = level === 0 ? "h-5 w-5" : "h-4 w-4";

    return (
      <div
        key={item.href}
        className={cn("space-y-1", !parentExpanded && "hidden")}
      >
        {hasChildren ? (
          <div>
            <Button
              variant="ghost"
              onClick={() => toggleSection(item.href)}
              className={cn(
                "w-full justify-between group transition-all duration-200 hover:bg-accent/80",
                paddingLeft,
                textSize,
                isActive && "bg-accent text-accent-foreground font-medium",
                level === 0 && "h-10 font-medium",
                level > 0 && "h-8 font-normal"
              )}
            >
              <div className="flex items-center gap-3">
                <item.icon className={cn(iconSize, "shrink-0")} />
                <span className="truncate">{item.name}</span>
                {item.badge && (
                  <Badge
                    variant="secondary"
                    className="ml-auto text-xs px-1.5 py-0.5"
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-1">
                {level === 0 && item.children && (
                  <span className="text-xs text-muted-foreground bg-muted rounded px-1.5 py-0.5">
                    {item.children.length}
                  </span>
                )}
                {isExpanded ? (
                  <ChevronUpIcon className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                ) : (
                  <ChevronDownIcon className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                )}
              </div>
            </Button>

            {/* Children with smooth animation */}
            <div
              className={cn(
                "overflow-hidden transition-all duration-300 ease-in-out",
                isExpanded ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
              )}
            >
              <div className="space-y-1 py-1">
                {item.children?.map((child) =>
                  renderNavigationItem(child, level + 1, isExpanded)
                )}
              </div>
            </div>
          </div>
        ) : (
          <Link href={item.href} className="block">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start group transition-all duration-200 hover:bg-accent/80 hover:translate-x-1",
                paddingLeft,
                textSize,
                isActive &&
                  "bg-accent text-accent-foreground font-medium shadow-sm",
                level === 0 && "h-10 font-medium",
                level > 0 && "h-8 font-normal"
              )}
            >
              <div className="flex items-center gap-3 w-full">
                <item.icon className={cn(iconSize, "shrink-0")} />
                <span className="truncate">{item.name}</span>
                {item.badge && (
                  <Badge
                    variant="secondary"
                    className="ml-auto text-xs px-1.5 py-0.5"
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
              {isActive && (
                <div className="w-1 h-6 bg-primary rounded-full ml-auto" />
              )}
            </Button>
          </Link>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search */}
      <div className="p-3 border-b">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search navigation..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9 h-9 bg-background/50 border-muted focus:bg-background"
          />
        </div>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1">
        <nav className="p-2 space-y-2">
          {filteredNavigation.map((item) => renderNavigationItem(item))}
        </nav>
      </ScrollArea>

      {/* Footer */}
      <div className="p-3 border-t bg-muted/30">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>© 2024 ID8 Media</span>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <DotsHorizontalIcon className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { user, logout } = useAuth();

  // Initialize openItems with parent menu of current path
  const [openItems, setOpenItems] = useState<string[]>(() => {
    const parentMenu = navigation.find((item) =>
      item.children?.some((child) => pathname.startsWith(child.href))
    );
    return parentMenu ? [parentMenu.href] : [];
  });

  // Update openItems when pathname changes
  useEffect(() => {
    const parentMenu = navigation.find((item) =>
      item.children?.some((child) => pathname.startsWith(child.href))
    );
    if (parentMenu && !openItems.includes(parentMenu.href)) {
      setOpenItems((prev) => [...prev, parentMenu.href]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  // Get current page title with improved breadcrumb logic
  const getCurrentPageTitle = () => {
    // Check for exact matches first
    for (const item of navigation) {
      if (item.href === pathname) {
        return item.name;
      }

      if (item.children) {
        for (const child of item.children) {
          if (child.href === pathname) {
            return child.name;
          }

          if (child.children) {
            for (const subChild of child.children) {
              if (subChild.href === pathname) {
                return subChild.name;
              }
            }
          }
        }
      }
    }

    // Fallback to partial matches
    const currentItem = navigation.find((item) =>
      item.children
        ? item.children.some((child) => pathname.startsWith(child.href))
        : pathname.startsWith(item.href)
    );

    if (currentItem?.children) {
      const childItem = currentItem.children.find((child) =>
        pathname.startsWith(child.href)
      );
      return childItem?.name || currentItem.name;
    }

    return currentItem?.name || "Dashboard";
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      {/* Desktop Sidebar */}
      <aside className="hidden md:flex md:w-72 md:flex-col md:border-r bg-card/50 backdrop-blur-sm">
        <div className="flex h-16 items-center justify-between border-b px-4 bg-background/80">
          <div className="flex items-center gap-3 py-2">
            <div className="relative">
              <Image
                src="/logo.svg"
                alt="ID8 Agents"
                width={120}
                height={32}
                className="h-8 w-auto object-contain"
                priority
              />
            </div>
            <Badge variant="default" className="text-xs font-medium">
              Sentra Platform
            </Badge>
          </div>
        </div>
        <NavigationMenu pathname={pathname} />
      </aside>

      {/* Mobile Sidebar */}
      {isSidebarOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm"
            onClick={() => setIsSidebarOpen(false)}
          />

          {/* Sidebar */}
          <div className="fixed inset-y-0 left-0 w-80 bg-card border-r shadow-2xl">
            <div className="flex h-16 items-center justify-between border-b px-4 bg-background/95">
              <div className="flex items-center gap-3">
                <Image
                  src="/logo.svg"
                  alt="ID8 Agents"
                  width={120}
                  height={32}
                  className="h-8 w-auto object-contain"
                  priority
                />
                <Badge variant="outline" className="text-xs font-medium">
                  v2.7.3
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsSidebarOpen(false)}
                className="hover:bg-destructive/10 hover:text-destructive"
              >
                <Cross1Icon className="h-5 w-5" />
              </Button>
            </div>
            <NavigationMenu pathname={pathname} />
          </div>
        </div>
      )}

      <main className="flex-1 flex flex-col overflow-hidden">
        <header className="flex h-16 items-center justify-between border-b px-4 md:px-6 bg-background/95 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden hover:bg-accent"
              onClick={() => setIsSidebarOpen(true)}
            >
              <HamburgerMenuIcon className="h-5 w-5" />
            </Button>

            {/* Enhanced Breadcrumb navigation */}
            <nav className="flex items-center space-x-1 text-sm md:text-base">
              <Link
                href="/dashboard"
                className="text-muted-foreground hover:text-foreground transition-colors font-medium"
              >
                Dashboard
              </Link>
              <ChevronRightIcon className="h-4 w-4 text-muted-foreground" />
              <span className="font-semibold text-foreground">
                {getCurrentPageTitle()}
              </span>
            </nav>
          </div>

          <div className="flex items-center gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-10 w-auto rounded-full hover:bg-accent/50 transition-colors"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.photoURL || ""} />
                    <AvatarFallback className="bg-primary/10 text-primary">
                      <PersonIcon className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium ml-2 hidden sm:inline">
                    {user?.displayName || "User"}
                  </span>
                  <ChevronDownIcon className="h-4 w-4 ml-1 hidden sm:inline" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="flex flex-col">
                  <span className="text-sm font-medium">
                    {user?.displayName || "User"}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {user?.email}
                  </span>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <Link href="/dashboard/profile">
                  <DropdownMenuItem className="cursor-pointer">
                    <PersonIcon className="mr-2 h-4 w-4" />
                    Profile
                  </DropdownMenuItem>
                </Link>
                <Link href="/dashboard/settings">
                  <DropdownMenuItem className="cursor-pointer">
                    <GearIcon className="mr-2 h-4 w-4" />
                    Settings
                  </DropdownMenuItem>
                </Link>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => logout()}
                  className="text-red-600 cursor-pointer focus:text-red-600 focus:bg-red-50"
                >
                  <ExitIcon className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <div className="flex-1 overflow-y-auto p-4 md:p-6 bg-background">
          {children}
        </div>
      </main>
    </div>
  );
}

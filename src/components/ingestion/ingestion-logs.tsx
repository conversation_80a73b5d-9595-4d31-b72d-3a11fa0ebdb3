"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  InfoIcon,
  SearchIcon,
  DownloadIcon,
  RefreshCwIcon,
  FilterIcon,
  ClockIcon,
} from "lucide-react";

interface LogEntry {
  id: string;
  timestamp: string;
  level: "info" | "warning" | "error" | "success";
  platform: string;
  message: string;
  details?: string;
  metadata?: Record<string, any>;
}

// Mock log data
const generateMockLogs = (): LogEntry[] => {
  const platforms = ["X (Twitter)", "Meta", "YouTube", "TikTok"];
  const levels: LogEntry["level"][] = ["info", "warning", "error", "success"];
  const messages = [
    "Successfully ingested batch of 50 posts",
    "Rate limit warning: approaching quota limit",
    "API connection timeout after 30 seconds",
    "Webhook endpoint responded successfully",
    "Failed to authenticate with platform API",
    "Data transformation completed successfully",
    "Invalid JSON response from platform API",
    "Pub/Sub message published successfully",
    "Database connection restored",
    "Retry attempt #3 for failed request",
  ];

  return Array.from({ length: 100 }, (_, i) => ({
    id: `log-${i + 1}`,
    timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    level: levels[Math.floor(Math.random() * levels.length)],
    platform: platforms[Math.floor(Math.random() * platforms.length)],
    message: messages[Math.floor(Math.random() * messages.length)],
    details:
      Math.random() > 0.7
        ? "Additional error details would appear here..."
        : undefined,
    metadata: {
      requestId: `req-${Math.random().toString(36).substr(2, 9)}`,
      duration: Math.floor(Math.random() * 5000),
      retryCount: Math.floor(Math.random() * 3),
    },
  })).sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
};

export function IngestionLogs() {
  const [logs, setLogs] = useState<LogEntry[]>(generateMockLogs());
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>(logs);
  const [searchTerm, setSearchTerm] = useState("");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [platformFilter, setPlatformFilter] = useState<string>("all");
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-refresh logs
  useEffect(() => {
    if (!isAutoRefresh) return;

    const interval = setInterval(() => {
      // Add new mock log entry
      const newLog: LogEntry = {
        id: `log-${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: ["info", "warning", "error", "success"][
          Math.floor(Math.random() * 4)
        ] as LogEntry["level"],
        platform: ["X (Twitter)", "Meta", "YouTube", "TikTok"][
          Math.floor(Math.random() * 4)
        ],
        message: "New ingestion event logged",
        metadata: {
          requestId: `req-${Math.random().toString(36).substr(2, 9)}`,
          duration: Math.floor(Math.random() * 5000),
        },
      };

      setLogs((prev) => [newLog, ...prev.slice(0, 99)]);
    }, 3000);

    return () => clearInterval(interval);
  }, [isAutoRefresh]);

  // Filter logs
  useEffect(() => {
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.platform.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (levelFilter !== "all") {
      filtered = filtered.filter((log) => log.level === levelFilter);
    }

    if (platformFilter !== "all") {
      filtered = filtered.filter((log) => log.platform === platformFilter);
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, levelFilter, platformFilter]);

  const getLevelIcon = (level: LogEntry["level"]) => {
    switch (level) {
      case "success":
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case "error":
        return <XCircleIcon className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangleIcon className="h-4 w-4 text-yellow-600" />;
      default:
        return <InfoIcon className="h-4 w-4 text-blue-600" />;
    }
  };

  const getLevelVariant = (level: LogEntry["level"]) => {
    switch (level) {
      case "success":
        return "default";
      case "error":
        return "destructive";
      case "warning":
        return "secondary";
      default:
        return "outline";
    }
  };

  const handleExportLogs = () => {
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataUri =
      "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);

    const exportFileDefaultName = `ingestion-logs-${
      new Date().toISOString().split("T")[0]
    }.json`;

    const linkElement = document.createElement("a");
    linkElement.setAttribute("href", dataUri);
    linkElement.setAttribute("download", exportFileDefaultName);
    linkElement.click();
  };

  const clearFilters = () => {
    setSearchTerm("");
    setLevelFilter("all");
    setPlatformFilter("all");
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Ingestion Logs</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant={isAutoRefresh ? "default" : "outline"}
                size="sm"
                onClick={() => setIsAutoRefresh(!isAutoRefresh)}
              >
                <RefreshCwIcon
                  className={`h-4 w-4 mr-2 ${
                    isAutoRefresh ? "animate-spin" : ""
                  }`}
                />
                Auto Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportLogs}>
                <DownloadIcon className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>

            <Select value={levelFilter} onValueChange={setLevelFilter}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="success">Success</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>

            <Select value={platformFilter} onValueChange={setPlatformFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Platforms</SelectItem>
                <SelectItem value="X (Twitter)">X (Twitter)</SelectItem>
                <SelectItem value="Meta">Meta</SelectItem>
                <SelectItem value="YouTube">YouTube</SelectItem>
                <SelectItem value="TikTok">TikTok</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={clearFilters}>
              <FilterIcon className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>

          <div className="mt-4 flex items-center gap-4 text-sm text-muted-foreground">
            <span>
              Showing {filteredLogs.length} of {logs.length} logs
            </span>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span>Success</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span>Info</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                <span>Warning</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                <span>Error</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Logs Table */}
      <Card>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]" ref={scrollAreaRef}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">Timestamp</TableHead>
                  <TableHead className="w-[100px]">Level</TableHead>
                  <TableHead className="w-[120px]">Platform</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead className="w-[100px]">Duration</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow
                    key={log.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => setSelectedLog(log)}
                  >
                    <TableCell className="font-mono text-xs">
                      <div className="flex items-center gap-2">
                        <ClockIcon className="h-3 w-3 text-muted-foreground" />
                        {new Date(log.timestamp).toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={getLevelVariant(log.level)}
                        className="flex items-center gap-1"
                      >
                        {getLevelIcon(log.level)}
                        {log.level}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{log.platform}</Badge>
                    </TableCell>
                    <TableCell className="max-w-[300px]">
                      <div className="truncate">{log.message}</div>
                      {log.details && (
                        <div className="text-xs text-muted-foreground truncate">
                          {log.details}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-xs text-muted-foreground">
                      {log.metadata?.duration
                        ? `${log.metadata.duration}ms`
                        : "-"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Log Details Modal */}
      {selectedLog && (
        <Card className="fixed inset-4 z-50 bg-background border shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                {getLevelIcon(selectedLog.level)}
                Log Details
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedLog(null)}
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Timestamp
                </Label>
                <p className="font-mono text-sm">
                  {new Date(selectedLog.timestamp).toLocaleString()}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Level
                </Label>
                <Badge
                  variant={getLevelVariant(selectedLog.level)}
                  className="flex items-center gap-1 w-fit"
                >
                  {getLevelIcon(selectedLog.level)}
                  {selectedLog.level}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Platform
                </Label>
                <p>{selectedLog.platform}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Request ID
                </Label>
                <p className="font-mono text-sm">
                  {selectedLog.metadata?.requestId || "N/A"}
                </p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Message
              </Label>
              <p>{selectedLog.message}</p>
            </div>

            {selectedLog.details && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Details
                </Label>
                <p className="text-sm">{selectedLog.details}</p>
              </div>
            )}

            {selectedLog.metadata && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Metadata
                </Label>
                <pre className="bg-muted p-3 rounded-md text-xs overflow-auto">
                  {JSON.stringify(selectedLog.metadata, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {selectedLog && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setSelectedLog(null)}
        />
      )}
    </div>
  );
}

function Label({
  children,
  className,
  ...props
}: {
  children: React.ReactNode;
  className?: string;
} & React.LabelHTMLAttributes<HTMLLabelElement>) {
  return (
    <label
      className={`block text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${
        className || ""
      }`}
      {...props}
    >
      {children}
    </label>
  );
}

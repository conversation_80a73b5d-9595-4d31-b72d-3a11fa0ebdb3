"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";
import {
  ActivityIcon,
  ZapIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  PauseIcon,
  PlayIcon,
  SettingsIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  WifiIcon,
  WifiOffIcon,
} from "lucide-react";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

interface StreamData {
  timestamp: string;
  throughput: number;
  latency: number;
  errors: number;
  connections: number;
}

interface StreamMonitorProps {
  platforms: PlatformStatus[];
}

// Mock real-time data generation
const generateStreamData = (): StreamData[] => {
  const data: StreamData[] = [];
  const now = new Date();

  for (let i = 29; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60000); // Every minute
    data.push({
      timestamp: timestamp.toISOString(),
      throughput: Math.floor(Math.random() * 100) + 50,
      latency: Math.floor(Math.random() * 200) + 50,
      errors: Math.floor(Math.random() * 5),
      connections: Math.floor(Math.random() * 20) + 80,
    });
  }

  return data;
};

export function StreamMonitor({ platforms }: StreamMonitorProps) {
  const [streamData, setStreamData] = useState<StreamData[]>(
    generateStreamData()
  );
  const [selectedPlatform, setSelectedPlatform] = useState("all");
  const [timeRange, setTimeRange] = useState("30m");
  const [isMonitoring, setIsMonitoring] = useState(true);

  // Simulate real-time updates
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(() => {
      setStreamData((prev) => {
        const newData = [...prev.slice(1)]; // Remove oldest
        const now = new Date();

        // Add new data point
        newData.push({
          timestamp: now.toISOString(),
          throughput: Math.floor(Math.random() * 100) + 50,
          latency: Math.floor(Math.random() * 200) + 50,
          errors: Math.floor(Math.random() * 5),
          connections: Math.floor(Math.random() * 20) + 80,
        });

        return newData;
      });
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isMonitoring]);

  const currentData = streamData[streamData.length - 1];
  const previousData = streamData[streamData.length - 2];

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) {
      return <TrendingUpIcon className="h-4 w-4 text-green-600" />;
    } else if (current < previous) {
      return <TrendingDownIcon className="h-4 w-4 text-red-600" />;
    }
    return <ActivityIcon className="h-4 w-4 text-gray-600" />;
  };

  const getHealthStatus = (platform: PlatformStatus) => {
    switch (platform.streamHealth) {
      case "healthy":
        return {
          icon: <CheckCircleIcon className="h-4 w-4 text-green-600" />,
          color: "text-green-600",
        };
      case "degraded":
        return {
          icon: <AlertTriangleIcon className="h-4 w-4 text-yellow-600" />,
          color: "text-yellow-600",
        };
      case "critical":
        return {
          icon: <AlertTriangleIcon className="h-4 w-4 text-red-600" />,
          color: "text-red-600",
        };
      default:
        return {
          icon: <WifiOffIcon className="h-4 w-4 text-gray-400" />,
          color: "text-gray-400",
        };
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      {/* Control Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Stream Monitor</h3>
          <p className="text-sm text-muted-foreground">
            Real-time monitoring of data ingestion streams
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              {platforms.map((platform) => (
                <SelectItem key={platform.platform} value={platform.platform}>
                  {platform.platform}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="30m">30 minutes</SelectItem>
              <SelectItem value="1h">1 hour</SelectItem>
              <SelectItem value="6h">6 hours</SelectItem>
              <SelectItem value="24h">24 hours</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant={isMonitoring ? "default" : "outline"}
            onClick={() => setIsMonitoring(!isMonitoring)}
          >
            {isMonitoring ? (
              <PauseIcon className="h-4 w-4 mr-2" />
            ) : (
              <PlayIcon className="h-4 w-4 mr-2" />
            )}
            {isMonitoring ? "Pause" : "Start"}
          </Button>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Throughput</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">
                    {currentData?.throughput || 0}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    msgs/min
                  </span>
                  {previousData &&
                    getTrendIcon(
                      currentData?.throughput || 0,
                      previousData.throughput
                    )}
                </div>
              </div>
              <ZapIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Latency</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">
                    {currentData?.latency || 0}
                  </span>
                  <span className="text-sm text-muted-foreground">ms</span>
                  {previousData &&
                    getTrendIcon(
                      previousData.latency,
                      currentData?.latency || 0
                    )}
                </div>
              </div>
              <ActivityIcon className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Active Connections
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">
                    {currentData?.connections || 0}
                  </span>
                  {previousData &&
                    getTrendIcon(
                      currentData?.connections || 0,
                      previousData.connections
                    )}
                </div>
              </div>
              <WifiIcon className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Error Rate</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">
                    {currentData?.errors || 0}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    errors/min
                  </span>
                  {previousData &&
                    getTrendIcon(previousData.errors, currentData?.errors || 0)}
                </div>
              </div>
              <AlertTriangleIcon className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Throughput & Latency Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Throughput & Latency</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={streamData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={formatTimestamp}
                  interval="preserveStartEnd"
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="throughput"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  name="Throughput (msgs/min)"
                  dot={{ r: 3 }}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="latency"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Latency (ms)"
                  dot={{ r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Error Rate Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Error Rate & Connections</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={streamData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={formatTimestamp}
                  interval="preserveStartEnd"
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                />
                <Area
                  yAxisId="right"
                  type="monotone"
                  dataKey="connections"
                  stackId="1"
                  stroke="#8b5cf6"
                  fill="#8b5cf6"
                  fillOpacity={0.3}
                  name="Connections"
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="errors"
                  stroke="#ef4444"
                  strokeWidth={2}
                  name="Errors/min"
                  dot={{ r: 3 }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Platform Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Health Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {platforms.map((platform) => {
              const health = getHealthStatus(platform);
              return (
                <div
                  key={platform.platform}
                  className="p-4 border rounded-lg space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{platform.platform}</h4>
                    <div className="flex items-center gap-1">
                      {health.icon}
                      <span className={`text-xs ${health.color}`}>
                        {platform.streamHealth}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Rate Limit</span>
                      <span>{platform.rateLimitRemaining}/1000</span>
                    </div>
                    <Progress
                      value={(platform.rateLimitRemaining / 1000) * 100}
                      className="h-2"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">API Quota</span>
                      <span>{platform.apiQuotaUsed}%</span>
                    </div>
                    <Progress value={platform.apiQuotaUsed} className="h-2" />
                  </div>

                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>
                      Records: {platform.recordsIngested.toLocaleString()}
                    </span>
                    <span>Errors: {platform.errorCount}</span>
                  </div>

                  <div className="flex gap-1">
                    <Button size="sm" variant="outline" className="flex-1">
                      <SettingsIcon className="h-3 w-3 mr-1" />
                      Config
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      {platform.status === "active" ? (
                        <PauseIcon className="h-3 w-3 mr-1" />
                      ) : (
                        <PlayIcon className="h-3 w-3 mr-1" />
                      )}
                      {platform.status === "active" ? "Pause" : "Start"}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Stream Health Alerts */}
      <Card>
        <CardHeader>
          <CardTitle>Health Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {platforms
              .filter((p) => p.streamHealth !== "healthy")
              .map((platform) => (
                <div
                  key={platform.platform}
                  className={`p-3 rounded-lg border-l-4 ${
                    platform.streamHealth === "critical"
                      ? "border-red-500 bg-red-50"
                      : "border-yellow-500 bg-yellow-50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getHealthStatus(platform).icon}
                      <span className="font-medium">{platform.platform}</span>
                      <Badge
                        variant={
                          platform.streamHealth === "critical"
                            ? "destructive"
                            : "secondary"
                        }
                      >
                        {platform.streamHealth}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {new Date(platform.lastSync).toLocaleString()}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {platform.streamHealth === "critical"
                      ? "Stream is experiencing critical issues and may require immediate attention."
                      : "Stream performance is degraded but operational."}
                  </p>
                </div>
              ))}

            {platforms.every((p) => p.streamHealth === "healthy") && (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircleIcon className="h-12 w-12 mx-auto mb-2 text-green-600" />
                <p>All streams are healthy!</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  SaveIcon,
  TestTubeIcon,
  EyeIcon,
  EyeOffIcon,
  SettingsIcon,
  KeyIcon,
  ShieldIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
} from "lucide-react";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

interface PlatformConfigurationProps {
  platforms: PlatformStatus[];
}

interface PlatformConfig {
  name: string;
  enabled: boolean;
  apiKey: string;
  apiSecret: string;
  bearerToken?: string;
  webhookUrl?: string;
  rateLimit: number;
  batchSize: number;
  retryAttempts: number;
  environment: "production" | "staging" | "development";
  filters: string[];
}

const defaultConfigs: Record<string, PlatformConfig> = {
  "X (Twitter)": {
    name: "X (Twitter)",
    enabled: true,
    apiKey: "xvz1evFS4wEEPTGEFPHBog",
    apiSecret: "••••••••••••••••••••••••••••••••••••••••••••••••••",
    bearerToken: "••••••••••••••••••••••••••••••••••••••••••••••••••",
    webhookUrl: "https://api.yourapp.com/webhooks/twitter",
    rateLimit: 300,
    batchSize: 100,
    retryAttempts: 3,
    environment: "production",
    filters: ["#socialmedia", "#trending", "#viral"],
  },
  Meta: {
    name: "Meta",
    enabled: true,
    apiKey: "your_app_id",
    apiSecret: "••••••••••••••••••••••••••••••••••••••••••••••••••",
    webhookUrl: "https://api.yourapp.com/webhooks/meta",
    rateLimit: 200,
    batchSize: 50,
    retryAttempts: 3,
    environment: "production",
    filters: ["public_posts", "pages", "groups"],
  },
  YouTube: {
    name: "YouTube",
    enabled: true,
    apiKey: "AIzaSyAOiA_Od3CAkp9WbJ8QmqKP",
    apiSecret: "••••••••••••••••••••••••••••••••••••••••••••••••••",
    webhookUrl: "https://api.yourapp.com/webhooks/youtube",
    rateLimit: 100,
    batchSize: 25,
    retryAttempts: 5,
    environment: "production",
    filters: ["comments", "videos", "channels"],
  },
  TikTok: {
    name: "TikTok",
    enabled: false,
    apiKey: "",
    apiSecret: "",
    webhookUrl: "https://api.yourapp.com/webhooks/tiktok",
    rateLimit: 100,
    batchSize: 20,
    retryAttempts: 3,
    environment: "staging",
    filters: ["videos", "comments", "hashtags"],
  },
};

export function PlatformConfiguration({
  platforms,
}: PlatformConfigurationProps) {
  const [configs, setConfigs] = useState(defaultConfigs);
  const [selectedPlatform, setSelectedPlatform] = useState("X (Twitter)");
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [testResults, setTestResults] = useState<Record<string, string>>({});
  const [isTesting, setIsTesting] = useState<Record<string, boolean>>({});

  const handleConfigChange = (platform: string, field: string, value: any) => {
    setConfigs((prev) => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value,
      },
    }));
  };

  const handleSaveConfig = async (platform: string) => {
    try {
      // Simulate API call to save configuration
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log(`Saving configuration for ${platform}:`, configs[platform]);
      // Here you would make an actual API call to save the configuration
    } catch (error) {
      console.error("Failed to save configuration:", error);
    }
  };

  const handleTestConnection = async (platform: string) => {
    setIsTesting((prev) => ({ ...prev, [platform]: true }));
    setTestResults((prev) => ({ ...prev, [platform]: "" }));

    try {
      // Simulate API call to test connection
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock test result
      const isSuccess = Math.random() > 0.3; // 70% success rate for demo
      setTestResults((prev) => ({
        ...prev,
        [platform]: isSuccess ? "success" : "error",
      }));
    } catch (error) {
      setTestResults((prev) => ({ ...prev, [platform]: "error" }));
    } finally {
      setIsTesting((prev) => ({ ...prev, [platform]: false }));
    }
  };

  const toggleSecretVisibility = (platform: string, field: string) => {
    const key = `${platform}-${field}`;
    setShowSecrets((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const getTestResultIcon = (result: string) => {
    switch (result) {
      case "success":
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case "error":
        return <AlertTriangleIcon className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const currentConfig = configs[selectedPlatform];

  return (
    <div className="space-y-6">
      {/* Platform Selection */}
      <div className="flex items-center gap-4">
        <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select platform" />
          </SelectTrigger>
          <SelectContent>
            {Object.keys(configs).map((platform) => (
              <SelectItem key={platform} value={platform}>
                <div className="flex items-center gap-2">
                  {platform}
                  <Badge
                    variant={
                      configs[platform].enabled ? "default" : "secondary"
                    }
                  >
                    {configs[platform].enabled ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          onClick={() => handleTestConnection(selectedPlatform)}
          disabled={isTesting[selectedPlatform]}
          variant="outline"
        >
          <TestTubeIcon className="h-4 w-4 mr-2" />
          {isTesting[selectedPlatform] ? "Testing..." : "Test Connection"}
        </Button>

        {testResults[selectedPlatform] && (
          <div className="flex items-center gap-2">
            {getTestResultIcon(testResults[selectedPlatform])}
            <span className="text-sm">
              {testResults[selectedPlatform] === "success"
                ? "Connection successful"
                : "Connection failed"}
            </span>
          </div>
        )}
      </div>

      {/* Configuration Form */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5" />
              {currentConfig.name} Configuration
            </CardTitle>
            <div className="flex items-center gap-2">
              <Switch
                checked={currentConfig.enabled}
                onCheckedChange={(checked) =>
                  handleConfigChange(selectedPlatform, "enabled", checked)
                }
              />
              <Label>Enabled</Label>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="credentials" className="space-y-4">
            <TabsList>
              <TabsTrigger value="credentials">
                <KeyIcon className="h-4 w-4 mr-2" />
                Credentials
              </TabsTrigger>
              <TabsTrigger value="settings">
                <SettingsIcon className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
              <TabsTrigger value="security">
                <ShieldIcon className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
            </TabsList>

            <TabsContent value="credentials" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <div className="relative">
                    <Input
                      id="apiKey"
                      type={
                        showSecrets[`${selectedPlatform}-apiKey`]
                          ? "text"
                          : "password"
                      }
                      value={currentConfig.apiKey}
                      onChange={(e) =>
                        handleConfigChange(
                          selectedPlatform,
                          "apiKey",
                          e.target.value
                        )
                      }
                      placeholder="Enter API key"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() =>
                        toggleSecretVisibility(selectedPlatform, "apiKey")
                      }
                    >
                      {showSecrets[`${selectedPlatform}-apiKey`] ? (
                        <EyeOffIcon className="h-4 w-4" />
                      ) : (
                        <EyeIcon className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiSecret">API Secret</Label>
                  <div className="relative">
                    <Input
                      id="apiSecret"
                      type={
                        showSecrets[`${selectedPlatform}-apiSecret`]
                          ? "text"
                          : "password"
                      }
                      value={currentConfig.apiSecret}
                      onChange={(e) =>
                        handleConfigChange(
                          selectedPlatform,
                          "apiSecret",
                          e.target.value
                        )
                      }
                      placeholder="Enter API secret"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() =>
                        toggleSecretVisibility(selectedPlatform, "apiSecret")
                      }
                    >
                      {showSecrets[`${selectedPlatform}-apiSecret`] ? (
                        <EyeOffIcon className="h-4 w-4" />
                      ) : (
                        <EyeIcon className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {currentConfig.bearerToken !== undefined && (
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="bearerToken">Bearer Token</Label>
                    <div className="relative">
                      <Input
                        id="bearerToken"
                        type={
                          showSecrets[`${selectedPlatform}-bearerToken`]
                            ? "text"
                            : "password"
                        }
                        value={currentConfig.bearerToken}
                        onChange={(e) =>
                          handleConfigChange(
                            selectedPlatform,
                            "bearerToken",
                            e.target.value
                          )
                        }
                        placeholder="Enter bearer token"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() =>
                          toggleSecretVisibility(
                            selectedPlatform,
                            "bearerToken"
                          )
                        }
                      >
                        {showSecrets[`${selectedPlatform}-bearerToken`] ? (
                          <EyeOffIcon className="h-4 w-4" />
                        ) : (
                          <EyeIcon className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="webhookUrl">Webhook URL</Label>
                  <Input
                    id="webhookUrl"
                    type="url"
                    value={currentConfig.webhookUrl || ""}
                    onChange={(e) =>
                      handleConfigChange(
                        selectedPlatform,
                        "webhookUrl",
                        e.target.value
                      )
                    }
                    placeholder="Enter webhook URL"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="rateLimit">
                    Rate Limit (requests/minute)
                  </Label>
                  <Input
                    id="rateLimit"
                    type="number"
                    value={currentConfig.rateLimit}
                    onChange={(e) =>
                      handleConfigChange(
                        selectedPlatform,
                        "rateLimit",
                        parseInt(e.target.value)
                      )
                    }
                    min="1"
                    max="1000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch Size</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    value={currentConfig.batchSize}
                    onChange={(e) =>
                      handleConfigChange(
                        selectedPlatform,
                        "batchSize",
                        parseInt(e.target.value)
                      )
                    }
                    min="1"
                    max="500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="retryAttempts">Retry Attempts</Label>
                  <Input
                    id="retryAttempts"
                    type="number"
                    value={currentConfig.retryAttempts}
                    onChange={(e) =>
                      handleConfigChange(
                        selectedPlatform,
                        "retryAttempts",
                        parseInt(e.target.value)
                      )
                    }
                    min="1"
                    max="10"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="environment">Environment</Label>
                  <Select
                    value={currentConfig.environment}
                    onValueChange={(value) =>
                      handleConfigChange(selectedPlatform, "environment", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="development">Development</SelectItem>
                      <SelectItem value="staging">Staging</SelectItem>
                      <SelectItem value="production">Production</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="filters">Content Filters</Label>
                  <Textarea
                    id="filters"
                    value={currentConfig.filters.join(", ")}
                    onChange={(e) =>
                      handleConfigChange(
                        selectedPlatform,
                        "filters",
                        e.target.value.split(", ")
                      )
                    }
                    placeholder="Enter filters separated by commas"
                    rows={3}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangleIcon className="h-5 w-5 text-yellow-600" />
                    <h4 className="font-medium text-yellow-800">
                      Security Settings
                    </h4>
                  </div>
                  <p className="text-sm text-yellow-700">
                    These settings control security features for the platform
                    integration. Changes require administrator approval.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <Label>Enable IP Whitelisting</Label>
                      <p className="text-sm text-muted-foreground">
                        Restrict API access to specific IPs
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <Label>Require HTTPS</Label>
                      <p className="text-sm text-muted-foreground">
                        Force secure connections only
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <Label>Enable Logging</Label>
                      <p className="text-sm text-muted-foreground">
                        Log all API interactions
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <Label>Data Encryption</Label>
                      <p className="text-sm text-muted-foreground">
                        Encrypt data in transit and at rest
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end pt-4">
            <Button onClick={() => handleSaveConfig(selectedPlatform)}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Configuration
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircleIcon,
  AlertTriangleIcon,
  XCircleIcon,
  ClockIcon,
  WifiIcon,
  WifiOffIcon,
} from "lucide-react";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

interface PlatformStatusCardProps {
  platform: PlatformStatus;
}

export function PlatformStatusCard({ platform }: PlatformStatusCardProps) {
  const getStatusIcon = () => {
    switch (platform.status) {
      case "active":
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case "error":
        return <XCircleIcon className="h-4 w-4 text-red-600" />;
      case "maintenance":
        return <ClockIcon className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertTriangleIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStreamHealthIcon = () => {
    switch (platform.streamHealth) {
      case "healthy":
        return <WifiIcon className="h-4 w-4 text-green-600" />;
      case "degraded":
        return <WifiIcon className="h-4 w-4 text-yellow-600" />;
      case "critical":
        return <WifiOffIcon className="h-4 w-4 text-red-600" />;
      default:
        return <WifiOffIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default";
      case "error":
        return "destructive";
      case "maintenance":
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex items-center gap-3">
        {getStatusIcon()}
        <div>
          <div className="flex items-center gap-2">
            <span className="font-medium">{platform.platform}</span>
            <Badge variant={getStatusVariant(platform.status)}>
              {platform.status}
            </Badge>
          </div>
          <div className="text-sm text-muted-foreground">
            {platform.recordsIngested.toLocaleString()} records
          </div>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="text-right">
          <div className="text-sm font-medium">{platform.apiQuotaUsed}%</div>
          <Progress value={platform.apiQuotaUsed} className="w-16 h-2" />
        </div>

        <div className="flex items-center gap-1">
          {getStreamHealthIcon()}
          <span className="text-xs text-muted-foreground">
            {platform.streamHealth}
          </span>
        </div>
      </div>
    </div>
  );
}

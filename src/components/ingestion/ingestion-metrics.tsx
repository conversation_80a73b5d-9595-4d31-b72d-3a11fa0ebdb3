"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PlatformStatus {
  platform: string;
  status: "active" | "inactive" | "error" | "maintenance";
  lastSync: string;
  recordsIngested: number;
  errorCount: number;
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  rateLimitRemaining: number;
  nextResetTime: string;
  streamHealth: "healthy" | "degraded" | "critical";
}

interface IngestionMetricsProps {
  platforms: PlatformStatus[];
}

// Mock data for charts
const timeSeriesData = [
  { time: "00:00", "X (Twitter)": 120, Meta: 80, YouTube: 45, TikTok: 30 },
  { time: "04:00", "X (Twitter)": 150, Meta: 95, YouTube: 55, TikTok: 25 },
  { time: "08:00", "X (Twitter)": 180, Meta: 110, YouTube: 70, TikTok: 35 },
  { time: "12:00", "X (Twitter)": 200, Meta: 130, YouTube: 85, TikTok: 40 },
  { time: "16:00", "X (Twitter)": 175, Meta: 120, YouTube: 65, TikTok: 45 },
  { time: "20:00", "X (Twitter)": 160, Meta: 105, YouTube: 50, TikTok: 35 },
];

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff7300"];

export function IngestionMetrics({ platforms }: IngestionMetricsProps) {
  const [selectedMetric, setSelectedMetric] = useState("records");
  const [timeRange, setTimeRange] = useState("24h");

  // Prepare data for pie chart
  const pieData = platforms.map((platform, index) => ({
    name: platform.platform,
    value: platform.recordsIngested,
    color: COLORS[index % COLORS.length],
  }));

  // Prepare data for bar chart (API quota usage)
  const quotaData = platforms.map((platform) => ({
    platform: platform.platform.replace(" (Twitter)", ""),
    quota: platform.apiQuotaUsed,
    errors: platform.errorCount,
  }));

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex gap-4">
        <Select value={selectedMetric} onValueChange={setSelectedMetric}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select metric" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="records">Records Ingested</SelectItem>
            <SelectItem value="quota">API Quota Usage</SelectItem>
            <SelectItem value="errors">Error Rate</SelectItem>
          </SelectContent>
        </Select>

        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1h">Last Hour</SelectItem>
            <SelectItem value="24h">Last 24h</SelectItem>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Time Series Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Ingestion Trends ({timeRange})</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="X (Twitter)"
                stroke="#8884d8"
                strokeWidth={2}
                dot={{ r: 4 }}
              />
              <Line
                type="monotone"
                dataKey="Meta"
                stroke="#82ca9d"
                strokeWidth={2}
                dot={{ r: 4 }}
              />
              <Line
                type="monotone"
                dataKey="YouTube"
                stroke="#ffc658"
                strokeWidth={2}
                dot={{ r: 4 }}
              />
              <Line
                type="monotone"
                dataKey="TikTok"
                stroke="#ff7300"
                strokeWidth={2}
                dot={{ r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Platform Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Records Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => value.toLocaleString()}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* API Quota Usage */}
        <Card>
          <CardHeader>
            <CardTitle>API Quota & Errors</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={quotaData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="platform" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="quota" fill="#8884d8" name="API Quota %" />
                <Bar dataKey="errors" fill="#ff7300" name="Errors" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Summary Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Summary Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {platforms
                  .reduce((sum, p) => sum + p.recordsIngested, 0)
                  .toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Total Records</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {(
                  platforms.reduce((sum, p) => sum + p.apiQuotaUsed, 0) /
                  platforms.length
                ).toFixed(1)}
                %
              </div>
              <div className="text-sm text-muted-foreground">Avg API Usage</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {platforms.reduce((sum, p) => sum + p.errorCount, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Errors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {platforms.filter((p) => p.status === "active").length}
              </div>
              <div className="text-sm text-muted-foreground">
                Active Platforms
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

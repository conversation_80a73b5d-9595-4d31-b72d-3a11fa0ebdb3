import { useState } from "react";
import { LogMessage } from "@/components/ui/console";

function generateUniqueId() {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

export function useConsole() {
  const [logs, setLogs] = useState<LogMessage[]>([]);

  const addLog = (
    message: string,
    type: LogMessage["type"] = "info",
    details?: string,
    data?: object
  ) => {
    setLogs((prev) => [
      ...prev,
      {
        id: generateUniqueId(),
        type,
        message,
        details,
        data,
        timestamp: new Date(),
      },
    ]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return {
    logs,
    addLog,
    clearLogs,
  };
}

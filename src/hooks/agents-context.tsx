"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { db } from "../lib/firebase";
import { collection, getDocs, doc, setDoc } from "firebase/firestore";
import { AgentSettings } from "@/types";
import { useAuth } from "./auth-context";

interface AgentConfig {
  id: string;
  name: string;
  description: string;
  icon: string;
  color?: string;
}

export const agentConfig: Record<string, AgentConfig> = {
  textAnalyzer: {
    id: "textAnalyzer",
    name: "Text Analyzer",
    description: "Converts post comments into structured JSON with tags",
    icon: "MagnifyingGlassIcon",
    color: "blue",
  },
  sentimentAnalyzer: {
    id: "sentimentAnalyzer",
    name: "Sentiment Analysis",
    description: "Analyzes the emotional tone of comments",
    icon: "HeartIcon",
    color: "red",
  },
  storyGenerator: {
    id: "storyGenerator",
    name: "Story Generator",
    description: "Creates engaging stories from comments",
    icon: "LightningBoltIcon",
    color: "amber",
  },
  qualityChecker: {
    id: "qualityChecker",
    name: "Quality Assurance",
    description: "Ensures content meets quality standards",
    icon: "RocketIcon",
    color: "green",
  },
  cultureChecker: {
    id: "cultureChecker",
    name: "Culture Agent",
    description: "Adapts content to cultural contexts",
    icon: "GlobeIcon",
    color: "purple",
  },
  artGenerator: {
    id: "artGenerator",
    name: "Art Director",
    description: "Generates art stories from comments",
    icon: "DrawingPinIcon",
    color: "pink",
  },
  imageGenerator: {
    id: "imageGenerator",
    name: "Image Generator",
    description: "Creates visual representations of comments",
    icon: "ImageIcon",
    color: "indigo",
  },
};

export const defaultSettings: AgentSettings = {
  id: "",
  name: "",
  description: "",
  icon: "",
  color: "",
  enabled: true,
  systemPrompt: "",
  instructions: "",
  temperature: 0.7,
  maxTokens: 1000,
  model: "gemini-2.0-flash-002",
  customInstructions: "",
};

interface AgentsContextType {
  agents: Record<string, AgentSettings>;
  loading: boolean;
  updateAgent: (agentId: string, settings: AgentSettings) => Promise<void>;
  refreshAgents: () => Promise<void>;
}

const AgentsContext = createContext<AgentsContextType>({} as AgentsContextType);

export function AgentsProvider({ children }: { children: React.ReactNode }) {
  const [agents, setAgents] = useState<Record<string, AgentSettings>>({});
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const loadAgents = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const agentsCollection = collection(db, "agents");
      const snapshot = await getDocs(agentsCollection);
      const loadedAgents: Record<string, AgentSettings> = {};

      snapshot.docs.forEach((doc) => {
        loadedAgents[doc.id] = doc.data() as AgentSettings;
      });

      // Initialize missing agents with default settings
      Object.keys(agentConfig).forEach((agentId) => {
        if (!loadedAgents[agentId]) {
          loadedAgents[agentId] = {
            ...defaultSettings,
            id: agentId,
            name: agentConfig[agentId].name,
            description: agentConfig[agentId].description,
            icon: agentConfig[agentId].icon,
            color: agentConfig[agentId].color || "",
          };
        }
      });

      setAgents(loadedAgents);
    } catch (error) {
      console.error("Error loading agents:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAgents();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const updateAgent = async (agentId: string, settings: AgentSettings) => {
    if (!user) return;

    try {
      // Update in Firestore
      const docRef = doc(db, "agents", agentId);
      await setDoc(docRef, {
        ...settings,
        updatedAt: new Date().toISOString(),
        userId: user.uid,
      });

      // Update local state
      setAgents((prev) => ({
        ...prev,
        [agentId]: settings,
      }));
    } catch (error) {
      console.error("Error updating agent:", error);
      throw error;
    }
  };

  const refreshAgents = async () => {
    await loadAgents();
  };

  return (
    <AgentsContext.Provider
      value={{
        agents,
        loading,
        updateAgent,
        refreshAgents,
      }}
    >
      {children}
    </AgentsContext.Provider>
  );
}

export const useAgents = () => {
  const context = useContext(AgentsContext);
  if (!context) {
    throw new Error("useAgents must be used within an AgentsProvider");
  }
  return context;
};

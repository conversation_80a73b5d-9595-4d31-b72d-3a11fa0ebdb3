export type ResetCommentSuccessResult = {
  success: true;
  message: string;
  commentId: string;
};

export type ResetCommentErrorResult = {
  success: false;
  error: string;
  details?: string;
};

export type ResetCommentResult =
  | ResetCommentSuccessResult
  | ResetCommentErrorResult;

export type BatchResetSuccessResult = {
  success: true;
  message: string;
  reset: number;
  total: number;
  remaining: number;
};

export type BatchResetErrorResult = {
  success: false;
  error: string;
  details?: string;
};

export type BatchResetResult = BatchResetSuccessResult | BatchResetErrorResult;

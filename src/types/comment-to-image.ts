export enum ImageGenerationStatus {
  NOT_STARTED = "NOT_STARTED",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

export interface Comment {
  id: string;
  authorName: string;
  username: string;
  postBody: string;
  platform: string;
  created_at: {
    seconds: number;
    nanoseconds: number;
  };
  comment_like_count: number;
  metrics: {
    likes: number;
  };
  // Image generation fields
  imageUrl?: string;
  imageGenerated?: boolean;
  imageGenerating?: boolean;
  imageGenerationStatus?: ImageGenerationStatus;
  imageCaption?: string;
  imageStyle?: string;
  imageDimensions?: {
    width: string;
    height: string;
  };
  imageProcessedAt?: string;
  imageResetAt?: {
    seconds: number;
    nanoseconds: number;
  };
  // Art direction fields
  mood?: string;
  color_palette?: string[];
  focal_points?: string[];
  visual_elements?: string[];
  lighting?: string;
  texture_style?: string;
  atmosphere?: string;
  emotional_impact?: string;
  visual_flow?: string;
  key_elements?: string[];
  // Analysis fields
  textAnalysis?: {
    topics: string[];
    summary: string;
    focal_points: string[];
    keywords: string[];
  };
  sentimentAnalysis?: {
    intensity: string;
    emotions: string[];
    sentiment: string;
    sentiment_score: string;
    highlights: string[];
    summary: string;
  };
  storyAnalysis?: {
    title: string;
    story: string;
    tone: string;
    style: string;
  };
  cultureFitAnalysis?: {
    market_fit: string;
    recommendations: string[];
    alignment_score: string;
    brand_values: string[];
    warnings: string[];
    suggestions: string[];
  };
  qualityCheck?: {
    readability_score: string;
    uniqueness_score: string;
    overall_score: string;
    engagement_score: string;
    improvements: string[];
    feedback: string[];
  };
  artDirection?: {
    composition: string;
    style: string;
    mood: string;
    texture_style: string;
    color_palette: string[];
    key_elements: string[];
    focal_points: string[];
    visual_elements: string[];
    atmosphere: string;
    lighting: string;
    visual_flow: string;
    emotional_impact: string;
  };
  error?: string;
  mosaic_generated?: boolean;
  mosaic_url?: string;
}

export interface ProcessingStatus {
  total: number;
  processed: number;
  failed: number;
  inProgress: number;
  notInitialized: number;
  fetchedCount: number;
}

export interface CommentToImageClientProps {
  initialComments: Comment[];
  initialStatus: ProcessingStatus;
}

// Helper functions to check comment status
export const isCommentProcessed = (comment: Comment): boolean => {
  return (
    comment.imageUrl !== undefined &&
    comment.imageUrl !== "" &&
    comment.imageGenerationStatus === ImageGenerationStatus.COMPLETED
  );
};

export const isCommentFailed = (comment: Comment): boolean => {
  return comment.imageGenerationStatus === ImageGenerationStatus.FAILED;
};

export const isCommentInProgress = (comment: Comment): boolean => {
  return (
    comment.imageGenerationStatus === ImageGenerationStatus.IN_PROGRESS ||
    comment.imageGenerating === true
  );
};

export const isCommentNotStarted = (comment: Comment): boolean => {
  return (
    !comment.imageUrl || // No image URL means not processed
    comment.imageGenerationStatus === ImageGenerationStatus.NOT_STARTED ||
    !comment.imageGenerationStatus // Handle legacy data with no status
  );
};

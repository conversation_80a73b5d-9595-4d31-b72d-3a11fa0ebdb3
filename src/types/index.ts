export interface AgentSettings {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  enabled: boolean;
  systemPrompt?: string;
  instructions?: string;
  temperature?: number;
  maxTokens?: number;
  model: string;
  customInstructions?: string;
  // Image generation specific settings
  sampleCount?: number;
  aspectRatio?: "1:1" | "9:16" | "16:9" | "3:4" | "4:3";
  sampleImageStyle?:
    | "photograph"
    | "digital_art"
    | "landscape"
    | "sketch"
    | "watercolor"
    | "cyberpunk"
    | "pop_art";
  personGeneration?: "dont_allow" | "allow_adult" | "allow_all";
  safetySetting?:
    | "block_low_and_above"
    | "block_medium_and_above"
    | "block_only_high"
    | "block_none";
}

export interface InstagramPost {
  id: string;
  caption: string;
  batch: number;
  platform: string;
  status: string;
  timestamp: string;
  hashtag: string;
  content_type: string;
  created_at: string;
  updated_at: string;
  hashtags: string[];
  user_full_name: string;
  user_username: string;
  user_profile_pic_url: string;
  user_id: string;
  comment_count: number;
  like_count: number;
  mosaic_generated?: boolean;
  mosaic_url?: string;
  agents: {
    textAnalyzer?: TextAnalyzerResult;
    sentimentAnalyzer?: SentimentAnalyzerResult;
    storyGenerator?: StoryGeneratorResult;
    qualityChecker?: QualityCheckerResult;
    cultureChecker?: CultureCheckerResult;
    artGenerator?: ArtGeneratorResult;
    imageGenerator?: ImageGeneratorResult;
  };
}

export interface TextAnalyzerResult {
  summary: string;
  engagement_potential: string;
  content_quality: string;
  processed_at?: string;
}

export interface SentimentAnalyzerResult {
  tone: string;
  sentiment: string;
  sentiment_score: number;
  processed_at?: string;
}

export interface StoryGeneratorResult {
  story: string;
  story_type: string;
  notes: string;
  processed_at?: string;
}

export interface QualityCheckerResult {
  overall_score: number;
  key_strengths: string[];
  areas_of_concern: string[];
  recommendations: string[];
  content_appropriateness: string;
  brand_alignment: string;
  potential_risks: string[];
  compliance_notes: string;
  processed_at?: string;
}

export interface CultureCheckerResult {
  alignment_score: number;
  cultural_elements: string[];
  brand_voice_match: {
    score: number;
    observations: string[];
  };
  recommendations: string[];
  sensitivity_concerns: string[];
  processed_at?: string;
}

export interface ArtGeneratorResult {
  visual_theme: {
    mood: string;
    style: string;
    atmosphere: string;
  };
  color_palette: {
    color: string;
    purpose: string;
  }[];
  composition: {
    layout: string;
    focal_points: string[];
    balance_notes: string;
  };
  key_elements: {
    element: string;
    significance: string;
    placement: string;
  }[];
  storytelling: {
    narrative_elements: string[];
    visual_flow: string;
    emotional_impact: string;
  };
  technical_recommendations: string[];
  processed_at?: string;
}

export interface ImageGeneratorResult {
  url: string;
  caption: string;
  storageUrl: string;
  processed_at?: string;
  results?: {
    url: string;
    caption: string;
    storageUrl: string;
  };
}

export interface AgentData {
  textAnalyzer?: TextAnalyzerResult;
  sentimentAnalyzer?: SentimentAnalyzerResult;
  storyGenerator?: StoryGeneratorResult;
  qualityChecker?: QualityCheckerResult;
  cultureChecker?: CultureCheckerResult;
  artGenerator?: ArtGeneratorResult;
  imageGenerator?: ImageGeneratorResult;
  processed_at?: string | null;
}

export interface AgentConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  instructions?: string;
  customInstructions?: string;
  enabled?: boolean;
}
